CKEDITOR.editorConfig = function( config ) {
	// Define changes to default configuration here.
	// For complete reference see:
	// http://docs.ckeditor.com/#!/api/CKEDITOR.config
	config.extraPlugins = 'mathjax,image2,font,uploadimage,colorbutton,colordialog,sourcedialog';	
	
	config.toolbarGroups = [
		{ name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
        { name: 'basicstyles', groups: [ 'basicstyles' ] },
		{ name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
		{ name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
		'/',
		{ name: 'insert', groups: [ 'insert' ] },

		{ name: 'styles', groups: [ 'styles' ] },
		{ name: 'colors', groups: [ 'colors' ] },
		{ name: 'tools', groups: [ 'tools' ] },
		{ name: 'others', groups: [ 'others' ] },
		{ name: 'about', groups: [ 'about' ] },
        { name: 'links', groups: [ 'links' ] }
	];

	config.removeButtons = 'Save,Templates,NewPage,Preview,Print,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Smiley,PageBreak,Iframe,Language,CreateDiv,Flash,About,Maximize,ShowBlocks';


	// Set the most common block elements.
	config.format_tags = 'p;h1;h2;h3;pre';


	// Simplify the dialog windows.
	config.removeDialogTabs = 'image:advanced;link:advanced';
	
	//config.font_names = 'Arial;Comic Sans MS;Courier New;Georgia;Helvetica;Lucida Sans Unicode;Merriweather;Merriweather Sans;Tahoma;Times New Roman;Trebuchet MS;Verdana';
	config.font_names = 'Arial;Hindi/Bhaskar;Comic Sans MS;Courier New;Georgia;Helvetica;Lucida Sans Unicode;Merriweather;Tahoma;Times New Roman;Trebuchet MS;Verdana';

	config.font_names = 'Open Sans/Open Sans, sans-serif;' + config.font_names;
	config.contentsCss = [
		'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap'
	];
	//config.font_names = ((CKEDITOR.config.font_names !== 'undefined')? CKEDITOR.config.font_names : "") + 'GoogleWebFonts';
	
    config.mathJaxLib = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML';
	config.mathJax = {
		extensions: ['tex2jax.js'],
		jax: ['input/TeX', 'output/HTML-CSS'],
		tex2jax: {
			inlineMath: [['$','$']],
			displayMath: [['$$','$$'], ['\\[','\\]']],
			processEscapes: true
		}
	}
};