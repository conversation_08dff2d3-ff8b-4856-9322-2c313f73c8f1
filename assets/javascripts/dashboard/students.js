var instituteName,userName,contactName,contactNo;
// var emailPattern=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
instituteName=document.getElementById('institutename');
userName=document.getElementById('username');
contactName=document.getElementById('contactName');
contactNo=document.getElementById('contactNo');
var errorMsg=document.getElementById('error');
var successMsg=document.getElementById('success');
var table='';
function addTeachers() {
    if ((instituteName.value && userName.value && contactName.value && contactNo.value) != "") {
        $.ajax({
            type: "POST",
            url: 'dashboard/addInstitute',
            data: {
                userName: userName.value,
                contactName: contactName.value,
                contactNumber: contactNo.value,
                instituteName: instituteName.value,
                mode: 'add'
            },
            success: function (data, status,xhr) {


                if (data.status == "OK") {
                    successMsg.innerText = 'Submitted Successfully'
                    errorMsg.innerText ='';
                    techersData();

                } else {
                    successMsg.innerText = '';
                    errorMsg.innerText = 'User is not registered in WS';

                }

            }
        });
    }
}
function editInstitute() {
    if ((instituteName.value && userName.value && contactName.value && contactNo.value) != "") {
        $.ajax({
            type: "POST",
            url: 'addInstitute',
            data: {
                userName: userName.value,
                contactName: contactName.value,
                contactNumber: contactNo.value,
                instituteName: instituteName.value,
                mode: 'edit'
            },
            success: function (data, status,xhr) {


                if (data.status == "OK") {
                    successMsg.innerText = 'Submitted Successfully';
                    errorMsg.innerText ='';
                    instituteData();

                } else {
                    successMsg.innerText = '';
                    errorMsg.innerText = 'User is not registered in WS';

                }

            }
        });
    }
}

function deleteInstitute() {

}

//Data tables

function studentsData () {

    $('#studentsTable').DataTable( {
        "responsive": true,
        "columnDefs": [
            { responsivePriority: 1, targets: 4},

        ],
        // "processing": true,
        "serverSide": true,
        "bRetrieve": true,
        // "searching": false,
        "ordering":  false,
        "ajax": {
            "url": "dashboard/getInstitutesDetails",
            "type": "GET",
            "data": function ( outData ) {

                return outData;
            },
            dataFilter:function(inData){
                // what is being sent back from the server (if no error)

                return inData;
            },
            error:function(err, status){
                // what error is seen(it could be either server side or client side.

            },
        },
        "columns": [{
            "data": "instituteName",
        },

            {
                "data": "userName",

            },
            {
                "data": "contactName",
            },
            {
                "data": "contactNumber",
            },
            {
                "data":"id",
                "render": function (data, type, row) {

                    if ( row.id !=null) {
                        // var  tests =row.userName;
                        //   //var newStr = myStr.replace(/,/g, '-');
                        //   tests =tests.replace(/./g,"ans")
                        //   console.log("usrssrss==="+tests);

                        return "<a href='javascript:editInstitute(\"" + row.contactName + "\"," + row.id + ",\""+row.contactNumber+"\",\""+row.userName+"\",\""+row.instituteName+"\")'><i class='material-icons'>edit</i></a>";
                    }

                }
            },
            {
                "data":"id",
                "render": function (data, type, row) {

                    if ( row.id !=null) {
                        // var  tests =row.userName;
                        //   //var newStr = myStr.replace(/,/g, '-');
                        //   tests =tests.replace(/./g,"ans")
                        //   console.log("usrssrss==="+tests);

                        return "<a href='javascript:deleteInstitute(\"" + row.id +")'><i class=\"material-icons delete\">delete</i></a>";
                    }

                }
            }



        ]
    });

}


studentsData();


