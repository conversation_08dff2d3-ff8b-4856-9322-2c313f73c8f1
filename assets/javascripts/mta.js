function showMTAQuestions(){
    var questionDiv = document.createElement('div');
    var questions = quiz.questions;
    var answers=[];
    var questionsStr="";
    for(var index = 0; index < questions.length; ++index) {
     answers[index]= questions[index].ans1;
    }
    answers = shuffleArray(answers);
    for(var index = 0; index < answers.length; ++index) {
        questionsStr += " <div class='row'>" +
        "                                <div class='col-md-5 text-left col-md-offset-1'>"+(index+1)+".&nbsp;"+questions[index].ps+"</div>" +
        "                                <div class='col-md-5 text-left'><select name='answers"+index+"' id='answers"+index+"'><option value=''>Choose answer</option> </select></td>" +
        "                                 </div>" +
        "                            </div>" +
        "                            <hr class='quizhr'>";
    }
    questionDiv.innerHTML=questionsStr;
    $("#mtaModal").find('.question').text(""); // to remove old ones
    $("#mtaModal").find('#question')[0].appendChild(questionDiv);

    for(var index = 0; index < answers.length; ++index) {
        populateOptions("answers"+index,answers);
    }
}

function scoreAndShowAnswersMTA(data){
    var readMode=false;
    var msgStr;
    quiz.answers=data.results;
    quiz.score = 0;
    if(quiz.mode == "read"){
        readMode = true;

        quiz.questions = data.results;

        for(var i = 0; i < quiz.questions.length; ++i) {
            userAnswers[i] = {};

            userAnswers[i]['ans'] = quiz.questions[i].ans1;
            userAnswers[i]['id'] = quiz.questions[i].id;
        }
        quiz.userAnswers = userAnswers;
        $("#mtaModal").modal('show');
        modalName = "mtaModal";
        $("#mtaModal").find('.score-container').hide();
    }else {
        var score = getScoreMTA(quiz.questions);
        quiz.score = score.correctAnswers;
        msgStr = scoreMessage(score,quiz.questions.length);
        quiz.userAnswers = userAnswers;
        updateWithQuizAnswers(userAnswers,score);
        $("#mtaModal").find('.score-container').show();
    }

    var scoreDiv = document.createElement('div');
    var answerDiv = document.createElement('div');

    $("#mtaModal").find('.questionsection').css("display","none");
    $("#mtaModal").find('.answersection').css("display","block");
    $("#mtaModal").find('.score-container').text("");
    if(!readMode) {
        scoreDiv.innerHTML = msgStr;
        $("#mtaModal").find('#score-container')[0].appendChild(scoreDiv);
    }
    var displayQuestions = quiz.questions;
    var answerStr="";
    var correctAnswer="";
    var  answerClass = "";
    for(var index = 0; index < quiz.questions.length; ++index) {
        answerClass = "green";
        if(userAnswers[index]['ans']=="") answerClass = "blue";
        else if (userAnswers[index]['ans']==quiz.questions[index].ans1) answerClass = "green";
        else answerClass = "orange";
        answerStr += "<div class='row'><div style='margin-top: 30px;>"+
            "<div class='row'>"+
            "<div class='col-md-9'>"+
            "<span>"+(index+1)+".&nbsp;"+displayQuestions[index].ps+"</span>"+ "&nbsp;&nbsp;<span class='"+answerClass+"'>"+userAnswers[index]['ans']+"</span>"+
            "</div>";
        if(!readMode) {
            answerStr +="<div class='col-md-3 left-border-vr  text-center correct-answers'>" +
                "<p style='font-weight: 700;'>Correct Answer</p>" +
                "<p>" +
                "<span style='text-transform: capitalize'>" + displayQuestions[index].ans1 + "</span>" +
                "</p>" +
                "</div>";
        }
        answerStr += "</div>"+
            "</div></div>";
        if(!readMode) answerStr+="<hr>";

    }
    answerDiv.innerHTML=answerStr;
    $("#mtaModal").find('.answer-holder').text(""); // to remove old ones
    $("#mtaModal").find('#answer-holder')[0].appendChild(answerDiv);
    //initializing stuff..if the same quiz is loaded again
    quiz.currentIndex=0;
    quiz.mode="play";
    resetUserAnswers();
}

function getScoreMTA(answers) {
    var score={}, isIncorrect = false;
    var length = answers.length;
    var answer,userAnswer="";
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;
    score.totalQuesions=answers.length;
    var selectElement;
    for(var i = 0; i < length; ++i){
        userAnswers[i] = {};
        selectElement = document.getElementById("answers"+i);
        userAnswers[i]['ans'] = selectElement.options[selectElement.selectedIndex].value;
        userAnswers[i]['id'] = answers[i]['id'];
       if(userAnswers[i]['ans']=="") score.skipped++;
        else if (userAnswers[i]['ans']==answers[i].ans1) score.correctAnswers++;
        else score.wrongAnswers++;


    }
    return score;
}
function populateOptions(fieldName,options){

    var select = document.getElementById(fieldName);
    select.options.length = 1;
    // if("selectedSubject"==fieldName) select.options.length = 2;

    for(var i=0; typeof options!='undefined' && i<options.length; i++) {
        var opt = options[i];
        var el = document.createElement("option");
        el.textContent = opt;
        el.value = opt;
        select.appendChild(el);
    }

    select.focus();

}
