var section,sections;
var factor = 0.8;

function getFontSize(el) {
  var fs = $(el).css('font-size');    
    if(!el.originalFontSize)el.originalFontSize =fs; //set dynamic property for later reset  
    return  parseFloat(fs);  
  }

 //  if(siteId ==4 || siteId ==6) {
 //    function setFontSize(fact){
 //      if(section==null)
 //       section = $('#htmlContent').find('*')       
 //     .filter(
 //       function(){return  $(this).clone()
 //        .children()
 //        .remove()
 //        .end()
 //        .text().trim().length > 0;
 //      }); //filter -> exclude all elements without text
 //     section.each(function(){  
 //      var newsize = fact ? getFontSize(this) * fact : this.originalFontSize;
      
 //      if(newsize) $(this).css('font-size', newsize );      
 //    }); 
 //   }
 // }
  function setFontSize(fact){
    if(section==null) {
      section = $('#htmlreadingcontent iframe').contents().find('*').filter(function() {
        return $(this).children().clone().children().remove().end().text().trim().length > 0;
      });
      sections = $('#htmlreadingcontent').contents().find('*').filter(function() {
        return $(this).children().clone().children().remove().end().text().trim().length > 0;
      });
    } //filter -> exclude all elements without text
    if(section!=null) {
      section = $('#htmlreadingcontent iframe').contents().find('*').filter(function() {
        return $(this).children().clone().children().remove().end().text().trim().length > 0;
      });
      sections = $('#htmlreadingcontent').contents().find('*').filter(function() {
        return $(this).children().clone().children().remove().end().text().trim().length > 0;
      });
    }
    section.each(function(){
      var newsize = fact ? getFontSize(this) * fact : this.originalFontSize;
      if(newsize) $(this).css('font-size', newsize );
    });
    sections.each(function(){
      var newsize = fact ? getFontSize(this) * fact : this.originalFontSize;
      console.log(newsize);
      if(newsize) $(this).css('font-size', newsize );
    });
  }

function resetFont(){
  setFontSize();
}

function increaseFont() {
  setFontSize(1 / factor);
  console.log('test');
}

function decreaseFont(){
  setFontSize(factor);
}
