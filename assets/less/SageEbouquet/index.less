@import "variables/color.less";
@import "variables/fonts.less";
a{
  cursor: pointer !important;
}
.triangle1{
  position: absolute;
  right: 0;
  top: 0px;
  img{
    width: 400px;
  }
}
.triangle2{
  position: absolute;
  bottom: 0;
  left:0;
  img{
    width: 400px;
  }
}
.content-Preview {
  position: relative;
  //height: calc(100vh - 186px);
  height: auto;
  /*background: url('../../images/sage/Header_Image.png') center no-repeat;
  background-size: cover;
  padding: 2px 0;
  padding-bottom: 2rem;*/
  h4{
    /*font-size: 18px;
    font-weight: normal;
    color:@sageTheme;
    margin-left:-2rem;*/
  }
  /*.imgWrapper{
    position: relative;
    img{
      height: 395px;
      margin-top: 1rem;
      margin-left: -2rem;
    }
  }*/
}
.content-Preview h2 {
  /*font-size: 18px;
  color: #1F419B;
  font-weight: bold;*/
}
.circle {
  width:100px;height:100px;
  border: solid 1px #555;
  background-color: #fff;
  box-shadow: 3px 3px  rgba(0,0,0,0.8);
  -moz-box-shadow: 3px 3px  rgba(0,0,0,0.8);
  -webkit-box-shadow: 3px 3px  rgba(0,0,0,0.8);
  -o-box-shadow:3px 3px  rgba(0,0,0,0.8);
  border-radius:75px;
  text-align: center;
  img{
    margin-top: 1.2rem;
    height: 32px;
  }
  a{
    font-size: 12px;
    //font-weight: bold;
    font-family: @robotoBold;
    color:@sageTheme;
    display: block;
    cursor: pointer;
    &:hover{
      text-decoration: none;
    }
  }

  &:nth-child(2){
    p{
      color:rgba(45,187,249,1);
    }
  }
}
.searchSage{
  width: 400px;
  height: 40px;
  border-radius: 50px !important;
  border:1px solid @sageTheme!important;
  padding: 1rem;
  outline:0;
  position: relative;
}
.searchbar{
  input {
    border-radius: 50px !important;
    border-color: #233982;
    border-width: 1.5px;
    height: 50px !important;
    padding-left: 30px !important;
    padding-right: 50px !important;
    font-size: 15px !important;
    &.focus-visible {
      background-color: transparent !important;
    }
  }
  button{
    background: none;
    border:none;
    outline:0;
    position: absolute;
    right: 50px;
    width: 50px;
    height: 50px !important;
    border-radius: 50% !important;
    z-index: 99 !important;
    img{
      height: 30px;
    }
  }
  ul.typeahead {
    width: 85%;
    li a:active {
      color: #333333;
    }
  }
}
.browseMenu{
  display: none;
  width: 500px;
  min-height: 100px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  position: absolute;
  top: 4rem;
  z-index: 99;
  right: 5rem;
  padding: 2rem;
  &::before {
    content: "";
    width: 0px;
    height: 0px;
    border: 0.8em solid transparent;
    position: absolute;
    right: 60px;
    top: -20px;
    border-bottom: 10px solid #ededed;

  }
  h4{
    color:#000;
    font-size: 14px;
    font-family:@sageFont;
    //font-weight: bold;
    font-family: @robotoBold;
  }
  ul{
    padding: 0;
    margin-left: 26px;
    li{
      list-style-type: disc;
      padding-bottom: 0.5rem;

      a{
        color: #000 !important;
      }
    }
  }
}
.language{
  padding: 0;
  margin-left: 15px;
  li{
    list-style-type: disc;
    padding-bottom: 0.5rem;
    a{
      color:#000 !important;
    }
  }
}
.circle1{
  border-radius: 50px;
  width: 28px;
  height: 28px;
  border:1px solid @sageTheme;
  img{
    height: 24px;
  }
}
.subHeader{
  h4{
    margin-bottom: 0;
    margin-left: 8px;
  }
}
.circle2{
  border-radius: 50px;
  width: 24px;
  height: 24px;
  border:1px solid @sageTheme;
  display: inline-block;
  text-align: center;
  margin-left: 8px;
}
.browse{

}
.content-wrappers{
  position: absolute;
  top:80px;
  width: 235px;
  h2{
    color:#fff;
  }
  a{
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 60px;
    border-radius: 50px;
    margin-top: 1rem;
    border: 1px solid #fff;
    color:@blue;
    text-decoration: none;
    //font-weight: bold;
    font-family: @robotoBold;
    &:hover{
      text-decoration: none;
    }
    img{
      width: 50px;
      height: 50px !important;
      margin-top: 0px !important;
      margin-left: -25px!important;
    }
    span{
      position: relative;
      left: -3rem;
      color: #0056b3;
    }
  }
}
#loginBenifits{
  .modal-content{
   padding: 1rem;
  }
  .modal-header{
    border: none;
  }
  .modal-footer{
    border: none;
  }
  h3{
      font-size: 20px;
      //font-weight: bold;
      font-family: @robotoBold;
      color:@sageTheme;
    margin-top: 1rem;
  }
  h2{
    font-size:24px;
    //font-weight: bold;
    font-family: @robotoBold;
    color:@sageTheme;
    border-bottom: 1px solid;
    padding-bottom: 3px;
  }
  p{
    color:#000000;
    font-size: 18px;
    font-family: @sageFont;
    //font-weight: @medium;
    margin-top: 0.5rem;
  }
  ul{
    li{
      font-size: 16px;
      font-family: @sageFont;
      color:#000000;
    }
  }
  .btn-login{
    background:@sageTheme;
    color:#fff;
  }
  .btn-close{
    color:#fff;
  }
}
#popupLogin{
padding: 1rem;
}
.loginnow{
  color:@sageTheme !important;
  text-decoration: underline !important;
  //font-weight: bold;
  font-family: @robotoBold;
  cursor: pointer;
}
.forgotPassword{
  h3{
    font-size: 18px;
    color:@sageTheme;
    text-align: center;
    margin-top: 1rem;
  }
  p{
    font-size: 14px;
    color:@grey1;
  }
  #fPbtn{
    width: 100%;
    color:#ffffff;
    background:@sageTheme;
    margin-top: 2rem !important;
  }

}
#back-login{
  background: #dddddd;
  color:#555;
  display: flex;
  align-items: center;
  justify-content: center;
}
#reset-password-completed,#reset-google-paswd,#account-exists{
  p{
    margin-top: 2rem;
    color:@grey1;
    font-size: 15px;
    &:first-child{
      color:@sageTheme;
    }
  }
}
.btn-back{
  background: #dddddd !important;
}
#fp-user-email,#fp-user-email1{
  color:@sageTheme;
}
.evidya{
  .user_profile{
    margin-top: 0;
    #profile-menu{
      li{
        a[data-target="#orders"]{
          display: none;
        }
      }
    }
  }
  #notesMenu, #formatMenu {
    display: block !important;
  }
}
.desktop_image {
  img {
    width: 450px;
  }
}
.index_buttons {
  ul li {
    padding: 0 20px;
    a.humanities {
      background-color: @etext-Orange;
      color: #FFF;
      border-radius: 10px;
      padding: 8px 50px;
      font-size: 16px;
      line-height: 1.2;
      width: 250px;
      height: 65px;
      border-bottom: 5px solid #bf7e23;
      border-top: 3px solid #ffa638;
      box-shadow: 0 10px 20px rgba(93, 57, 36, 0.5);
      &:active{
        box-shadow: 0 5px 10px rgba(93, 57, 36, 0.5);
      }
    }
    a.management {
      background-color: #d3d3d3;
      color: #000;
      border-radius: 10px;
      padding: 16px 50px;
      font-size: 16px;
      width: 250px;
      height: 65px;

      border-bottom: 5px solid #c5c9c9;
      border-top: 3px solid #e5e5e5;
      box-shadow: 0 10px 20px rgba(93, 57, 36, 0.5);
      &:active {
        box-shadow: 0 5px 10px rgba(93, 57, 36, 0.5);
      }
    }
  }
}
.index_quick_links {
  ul {
    li {
      padding: 0 15px;
      border-right: 2px solid @etext-Grey;
      line-height: normal;
      &:last-child{
        border-right: none;
      }
      a {
        color: @etext-Blue;
        font-size: 16px;
      }
    }
  }
}

.index-right {
  position: relative;
  top: -50px;
  .heading{
    h1 {
      font-size: 30px;
      font-family: HelveticaNeue-Medium;
    }
    h4 {
      font-size: 16px;
    }
  }
  .icon_texts {
    .icon {
      img {
        width: 70px;
      }
    }
    .texts {
      h5 {
        font-size: 14px;
      }
      p {
        font-size: 14px;
        line-height: normal;
        margin-bottom: 0;
      }
    }
  }
}

.profile-popup-open {
  &:before {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
  }
  .evidyaLogin {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    left: 0;
    margin: 0 auto;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.5);
    z-index: 999999;
    &:before {
      display: none;
    }
  }
}

// SAGE e-Bouquet styles
.ebouquet {
  .banner-info {
    h1.maintext {
      //font-weight: 700;
      font-family: @robotoBlack;
      color: #1E4598;
    }
    h1.subtext {
      //font-weight: 300;
      font-family: @robotoLight;
      color: #6C635D;
    }
    img {
      width: 90%;
      margin: 1rem 0;
    }
  }
  .ask-options {
    background-color: #EAEAEA;
    h1 {
      //font-weight: 700;
      font-family: @robotoBlack;
      color: #58595B;
    }
    ul.title-style {
      li {
        list-style-type: none;
        margin: 0 25px;
        width: 10px;
        height: 10px;
        //background-color: #000000;
        border-radius: 50px;
        &:first-child {
          background-color: #00B59A;
        }
        &:nth-child(2) {
          background-color: #FF9D00;
        }
        &:nth-child(3) {
          background-color: #FF553A;
        }
        &:nth-child(4) {
          background-color: #0E8ECE;
        }
        &:nth-child(5) {
          background-color: #B164A5;
        }
        &:nth-child(6) {
          background-color: #FFCB66;
        }
        &:nth-child(7) {
          background-color: #00B59A;
        }
        &:last-child {
          background-color: #FF9D00;
        }
      }
    }
    .options-info {
      .circle-style {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        font-size: 17px;
        line-height: 150px;
        text-align: center;
        //font-weight: 700;
        color: #58595B;
        position: relative;
        border: 6px solid #16CABD;
        margin: 0 15px;
        box-shadow: inset -7px 5px 10px #CCC;
        &:before {
          content: '';
          position: absolute;
          top: -12px;
          left: -12px;
          width: 172px;
          height: 172px;
          border-radius: 50%;
          border: 1.5px solid #000;
        }
        span {
          display: inline-block;
          vertical-align: middle;
          line-height: normal;
          padding: 0 20px;
          position: relative;
          z-index: 99;
          font-family: @robotoBold;
        }
        img {
          width: 60px;
          position: absolute;
          top: -30px;
          left: -10px;
        }
      }
      .green-border {
        border-color: #16CABD;
      }
      .orange-border {
        border-color: #FF9D00;
      }
      .red-border {
        border-color: #FF553A;
      }
      .blue-border {
        border-color: #0E8ECE;
      }
      .purple-border {
        border-color: #B164A5;
      }
      .yellow-border {
        border-color: #FFCB66;
      }
    }
  }
  .big-links {
    a {
      display: block;
      background-color: #58595B;
      color: #ffffff;
      border-bottom-left-radius: 30px;
      border-bottom-right-radius: 30px;
      &:hover {
        text-decoration: none;
        background-color: #FFCB66;
        color: #58595B;
      }
      h3 {
        padding: 1.5rem 2.5rem;
        //font-weight: 700;
        font-family: @robotoBold;
      }
    }
  }
}
