@import "variables/color.less";
@import "variables/common.less";
@import "variables/responsive.less";
@import "reboot.less";
@import "header.less";
@import "index.less";
@import "store.less";
@import "about.less";
@import "library.less";
@import "footer.less";
@import "../variables/admin/admin.less";
@import "bookcreate.less";
@import "libadmin.less";
@import "usagereport.less";
@import "admin.less";
@import "../variables/store/store.less";
@import "../adminws/bookcreate.less";
@import "../adminws/mcqcreate.less";

/*Library*/
@import "../variables/library/library.less";

/* STORE 1 */
@import "../categories/categories.less";

// From SageStyle.less
header{
  height:auto;
  width: 100%;
/*  background:#1F419B ;*/
  display: block;
  align-items: center;
  box-shadow: none;
  >div{
    width: 100%;
  }
}
.login{
  //padding: 0.5rem 2rem;
  //color:#1F419B;
  //background: white;
  //margin-right:2rem ;
  //border-radius:4px ;
}
/*.content-Preview{
  position: relative;
  height: calc(100vh - 185px);
  h2{
    font-size: 24px;
    color:#1F419B;
    font-weight: bold;
  }
}*/

.img-wrapper{
  width: 150px;
  position: relative;
  img{
    width: 150px;
    height: 200px;
    margin-top: 1rem;
    &:hover{
      .zoomImage{
        display: block;

      }
    }
  }
}
.text-wrapper h4{
  width: 150px;
  font-size: 14px;
  color:#444444;
  margin-top: 0.8rem;
}
.zoomImage{
  display: none;
  position: absolute;
  left: 15rem;
  top: 0;
  img{
    height: 400px;
    width: 300px;
  }
}

.sageEvidya{
  .ws-header{
    background: #1E4598;
    //padding: 0;
    width: 100%;
  }
  .ws-header .navbar-nav .nav-link{
    color:#ffffff;
    font-family: @sageFont;
  }
  .ws-header .navbar-nav .nav-item.active{
    background: transparent;
  }
}
.evidLandingPage{
  .order-pr{
    display: none !important;
  }
}
.library .generate{
  display: none !important;
}
.library .vidClass{
  display: none !important;
}
.library .vidTest{
  display: none !important;
}


@-webkit-keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}

@keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}
.loading-icon{
  position: fixed;
  width: 100%;
  height: 100%;
  top:0;
  background: fade(@ws-darkBlack,90%);
  z-index: 9999;
  overflow: hidden;
}
.load-wrapper{
  position: absolute;
  top: 35%;
  width: 100%;
}
#book-read-material{
  .tab-content>.tab-pane{
    min-height: calc(100vh - 150px);
  }
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px; }

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out; }

.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s; }

.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0; }

.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s; }

.loader:after {
  left: 3.5em; }

@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }


.bookTemplate #notesLoading .content-wrapper{
  height: auto;
}
header{
  width:100%;
  z-index: 999;
  //background:linear-gradient(to bottom, white 80%, #e0e0e0);
  top:0;
  //box-shadow:0 4px 4px rgba(0, 0, 0, 0.2), 0 0 rgba(0, 0, 0, 0.1);
  &.normalHeader{
    box-shadow: none;
    position: relative;
    .ws-header{
      max-width: 100%;
    }
  }
}
.preview-book-btns{
  display: none;
}

.etexts .book-read-material {
  .pr-back-btn {
    display: none !important;
  }
}
.etexts .read-content .price-wrapper {
  .section-btns {
    display: none !important;
  }
}
.etexts .bookTemplate .menu li a {
  padding: 0.9rem 1rem;
}
.etexts .export-notes .export-study-set {
  display: none !important;
}
.prevnextbtn{
  width:768px;
  margin-left: 3rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .prevnextbtn{
    width:300px !important;
    margin-left: 0;
  }
}
//.ebouquet .bookTemplate {
//  height: calc(100vh - 100px);
//}
//.evidya .bookTemplate .content-wrapper {
//  height: calc(100vh - 100px);
//}
//.evidya #book-read-material, .evidya .book-read-material {
//  min-height: calc(100vh - 149px);
//  max-height: calc(100vh - 149px);
//  width: 100% !important;
//  overflow: hidden;
//  overflow-y: auto;
//  max-width: 100%;
//}
.ebouquet.hasScrolled.custom-fix{
  header{
   //display: none;
  }
}
//.ebouquet.custom-fix .bookTemplate .shadowHeader{
//  position: fixed;
//  top: 0;
//}
@media @iPhone {
  .ebouquet .bookTemplate .export-notes {
    top: 99px;
  }

  .ebouquet.hasScrolled .bookTemplate .export-notes {
    top: 50px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #chapter-details-tabs{
    width:25% !important;
  }
  .contentEdit{
    width: 80px !important;
  }
  .bookTemplate .tab-header .contentEdit #notesMenu {
    padding-left: 10px;
  }
}
.eutkarsh.evidya.etexts.ebouquet button#toggle {
  display:none;
}
button#toggle {
  display:none;
}