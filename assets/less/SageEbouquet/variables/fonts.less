@font-face {
  font-family: 'Roboto-Regular';
  src: url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.eot?#iefix') format('embedded-opentype'),
  url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.woff') format('woff'),
  url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.ttf')  format('truetype'),
  url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.svg#Roboto-Regular') format('svg');
}

@font-face {
  font-family: 'Roboto-Light';
  src: url('fonts/ebouquet/Roboto-Light/Roboto-Light.eot?#iefix') format('embedded-opentype'),
  url('fonts/ebouquet/Roboto-Light/Roboto-Light.woff') format('woff'),
  url('fonts/ebouquet/Roboto-Light/Roboto-Light.ttf')  format('truetype'),
  url('fonts/ebouquet/Roboto-Light/Roboto-Light.svg#Roboto-Light') format('svg');
}

@font-face {
  font-family: 'Roboto-Bold';
  src: url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.eot?#iefix') format('embedded-opentype'),
  url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.woff') format('woff'),
  url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.ttf')  format('truetype'),
  url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.svg#Roboto-Bold') format('svg');
}

@font-face {
  font-family: 'Roboto-Black';
  src: url('fonts/ebouquet/Roboto-Black/Roboto-Black.eot?#iefix') format('embedded-opentype'),
  url('fonts/ebouquet/Roboto-Black/Roboto-Black.woff') format('woff'),
  url('fonts/ebouquet/Roboto-Black/Roboto-Black.ttf')  format('truetype'),
  url('fonts/ebouquet/Roboto-Black/Roboto-Black.svg#Roboto-Black') format('svg');
}

@sageFont: 'Roboto-Regular';
@robotoLight: 'Roboto-Light';
@robotoBlack: 'Roboto-Black';
@robotoBold: 'Roboto-Bold';
@medium:500;
@regular:400;
