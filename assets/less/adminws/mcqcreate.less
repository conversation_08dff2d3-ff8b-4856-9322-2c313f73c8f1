// MCQ Create Page New Styles
@import "../variables/ws_color_theme.less";
@import "../variables/ws_fonts.less";
@import "../variables/responsive.less";

.mcq_creation {
  #static-content {
    box-shadow: 0 2px 10px #E9EEF5;
    background: #FFF;
    .firstPage {
      background-color: #F3F7FA;
      box-shadow: 0 2px 10px #E9EEF5;
      input {
        background: #fff;
        padding: .375rem .75rem;
        border-radius: 5px;
        border: 1px solid #B4CDDE;
        box-shadow: 0 2px 4px #ECECFB;
        font-size: 14px;
        width: 100%;
      }
      select {
        font-size: 14px;
        color: #212121;
        border: 1px solid #B4CDDE;
        box-shadow: 0 2px 4px #ECECFB;
        width: 100%;
      }
      label {
        font-weight: 500;
        font-size: 12px;
        margin-bottom: 0.2rem;
      }
      label.form-check-label {
        font-weight: normal;
        font-size: 14px;
        margin-bottom: 0;
      }
      .form-check input {
        width: 16px;
        height: 16px;
        box-shadow: none;
      }
      .inlineEditor {
        border-color: #B4CDDE;
      }
    }
    #questionLabel {
      font-weight: 500;
      font-size: 12px;
      margin-bottom: 0.2rem;
      text-transform: uppercase;
    }
    .inlineEditor {
      border-color: #CCC;
    }
    .quiz {
      width:100%;
      margin: 0 auto;
      input[type="checkbox"] {
        width: 18px;
        height: 18px;
      }
    }
    .quiz1, .quiz2 {
      width:100%;
      input {
        border-color: #CCC;
      }
    }
    .quiz1 {
      label {
        font-weight: 500;
        font-size: 12px;
        margin-bottom: 0.2rem;
        color: #212529;
      }
    }
    .quiz3, .quiz4 {
      width:100%;
    }
    .smallerText {
      label {
        text-transform: uppercase;
        font-weight: 500;
        font-size: 12px;
        margin-bottom: 0.2rem;
      }
      input {
        border-color: #CCC;
        font-size: 14px;
      }
      select {
        font-size: 14px;
      }
    }
    .quiz6 {
      .btn {
        text-transform: uppercase;
        font-size: 13px;
      }
      .btn-default {
        color: #0AAEF9;
        border-color: #0AAEF9;
        &:focus, &:active {
          background-color: #efefef !important;
          border-color: transparent !important;
          box-shadow: 0 0 0 0.1rem rgba(23,162,184,.5) !important;
        }
      }
      .btn-outline-primary {
        color: #007bff !important;
        &:focus, &:active {
          background-color: transparent !important;
          border-color: #007bff !important;
          box-shadow: 0 0 0 0.1rem rgba(0,123,255,.5) !important;
        }
        &:hover {
          background: transparent !important;
        }
      }
    }
    .quiz8 {
      .alert {
        margin-left: 0;
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
        p {
          margin-bottom: 0;
        }
      }
    }
    .cktext {
      &.red-border {
        border: none;
        .has-error {
          border-color: red;
          border-width: 2px;
        }
        .red-border {
          border:none;
          border-bottom: 2px solid red;
        }
      }
    }
  }
  #sidebar {
    .quiz-number {
      height: 30px;
      width: 35px;
      padding: 4px;
      float: left;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      margin-bottom: 10px;
      border-width: 2px;
      font-weight: bold;
      &:focus {
        color: #FFF;
      }
    }
  }
}
