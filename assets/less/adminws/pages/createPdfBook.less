@import "_common";

#createPdfBook {
  .container{
    width: 60% ;
  }
  .lightblue_bg {
    background-color: #F3F7FA;
    box-shadow: 0 2px 10px #e9eef5;
    padding: 0.1rem;
  }

  .nav-pills {
    box-shadow: 0 2px 4px #b9b9c8;
  }

  #UploadSizeAlert, #UploadPdfFile, #notesUploadAlert {
    padding-left: 0px !important;
  }

  h4 {
    font-family: 'Quicksand', sans-serif !important;
    color: black !important;
  }

  .form-group {
    label {
      font-weight: 500;
      font-size: 12px;
      font-family: 'Quicksand', sans-serif !important;
    }
  }
  input  {
    font-family: 'Quicksand', sans-serif !important;
  }
  button{
    font-family: 'Quicksand', sans-serif !important;
   margin-bottom: 1rem !important;
  }
  .invalid-feedback {
    font-size: 15px !important;
  }
  .form-fields{
    padding-top: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .form-control{
    &:focus{
      box-shadow: none;
    }
  }
  @media @extraSmallDevices, @smallDevices{
    #actionButtons{
      display: flex;
      padding-left: 0px !important;
      margin: 0.25rem;
    }
   #cancelBtn{
      margin-right: 0.25rem;
    }
   .container{
     width: 100% ;
   }
  }
}