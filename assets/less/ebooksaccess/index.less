@import "variables/color.less";
@import "variables/fonts.less";
@import "variables/responsive";

.ebooksaccess {
  .curve-bg {
    position: absolute;
    top: 90px;
    right: 0;
    @media @smallScreen{
      top: 120px;
    }

    img {
      width: 800px;
      @media @extraLargeScreen {
        width: 700px;
      }
      @media @largeScreen {
        width: 600px;
      }
      @media @mediumScreen {
        width: 500px;

      }
    }
  }
  .banner_wrap {
    @media @mediumScreen {
      margin-top: 0 !important;
    }
    @media @smallScreen {

      margin-top: -150px !important;
      padding-top: 8rem !important;
      border-radius: 0px 0px 20px 20px;
    }
    .banner_info {
      @media @smallScreen {
        margin-top: 5rem;
      }
    }
    h1 {
      color: #444444;
      font-weight: normal;
      @media @mediumScreen {
        font-size: 2rem;
      }
      @media @smallScreen {
        color: #ffffff;
        font-size: 1.6rem;
        justify-content: center;
      }
      ion-icon {
        padding: 0 10px;
        color: rgba(68, 68, 68, 0.48);
        @media @smallScreen {
          padding: 0 5px;
          color: #ffffff;
          font-size: 30px;
        }
      }
      span {
        color: rgba(68, 68, 68, 0.48);
        @media @smallScreen {
          color: #ffffff;
        }
      }
    }
    h5 {
      color: rgba(68, 68, 68, 0.48);
      font-weight: normal;
      font-size: 1.15rem;
      font-family: @ebooksAccessFont;
      line-height: 28px;
      @media @mediumScreen {
        font-size: 1.1rem;
        line-height: 25px;
      }
      @media @smallScreen {
        font-size: .9rem;
        line-height: 22px;
        text-align: center;
      }
    }
    button {
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      border-width: 1.25px;
      width: 140px;
      font-weight: normal;
      @media @smallScreen {
        width: 120px;
      }
    }
    a.btn {
      box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      border-width: 1.25px;
      width: 140px;
      font-weight: normal;
      @media @smallScreen {
        width: 120px;
      }
    }
    .login-btn {
      border-color: #444444;
      background: none;
    }
    .signup-btn {
      background: @btn-Gradient;
      border-color: @ebooksAccessTheme;
      color: #ffffff;
    }
    p, i.fa {
      color: rgba(68, 68, 68, 0.48);
      margin-bottom: 0;
    }
    i.fa {
      font-size: 24px;
      @media @smallScreen {
        font-size: 22px;
      }
    }

    .banner_img {
      margin-top: -7rem;
      @media @mediumScreen {
        margin-top: -5rem;
      }
      @media @smallScreen {
        margin-top: 4rem;
        margin-bottom: -40px;
      }
      img {
        width: 100%;
      }
    }
  }
}
