@prep-primary: #0F0839;
@prep-secondary:#04001D;
@prep-white:#fff;
@prep-red:#E83500;

body{
  background: @prep-secondary !important;
  font-family: Righteous !important;
  overflow-x: hidden !important;
}
h1, h2, h3, h4, h5, h6 {
  color: @white !important;
}
p,a{
  color: @white !important;
}

.ebook_detail{
  background:@prep-secondary !important;
}
.ebooks .ebooks_filter{
  background: transparent !important;
  box-shadow: 0 3px 10px 0 rgba( 31, 38, 135, 0.37 ) !important;
  backdrop-filter: blur( 2px ) !important;
  -webkit-backdrop-filter: blur( 2px ) !important;
  border-radius: 10px !important;
  border: 1px solid rgba( 255, 255, 255, 0.18) !important;
  width: 100%  !important;
}
.books-list{
  background: @prep-secondary !important;
  //box-shadow: 0 3px 40px 0 rgba( 31, 38, 135, 0.37 ) !important;
  backdrop-filter: blur( 2px ) !important;
  -webkit-backdrop-filter: blur( 2px ) !important;
  border-radius: 10px !important;
  //border: 1px solid rgba( 255, 255, 255, 0.18) !important;
}

.books-list .image-wrapper{
  background: transparent !important;
  box-shadow: 0 3px 40px 0 rgba( 31, 38, 135, 0.37 ) !important;
  backdrop-filter: blur( 2px ) !important;
  -webkit-backdrop-filter: blur( 2px ) !important;
  border-radius: 10px !important;
  border: 1px solid rgba( 255, 255, 255, 0.18) !important;
}
#filters{
  h5{
    color: #fff !important;
  }
  #resetFilter{
    color: @prep-white !important;
  }
}

.book_info{
  color:@prep-white !important;
  h2{
    color:@prep-white !important;
  }
}
#buyNow,#okBuy,#linkLibrary,#linkOthers,#addtoLibrary{
  background-color: @prep-red !important;
  color: @prep-white !important;
  border: 1px solid @prep-red !important;
  transition: all 0.2s ease;
  box-shadow: 2px 2px 2px rgba(255,255,255,0.25),-2px -2px 2px rgba(255,255,255,0.25) !important;
  &:hover{
    transform: scale(1.06);
  }
  &:active{
    transform: scale(1);
  }
}
#continue{
  background-color: @prep-red !important;
  color: @prep-white !important;
  border: 1px solid @prep-red !important;
  transition: all 0.2s ease;
  &:hover{
    transform: scale(1.06);
  }
  &:active{
    transform: scale(1);
  }
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active{
  color:#6C757D !important;
  border: 1px solid #888 !important;
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart{
  color: @prep-white !important;
}
.tab-pane{
  color: @prep-white !important;
  h5{
    color: @prep-white !important;
  }
}
.orders{
  background: @prep-primary !important;
  img{
    width: 100px !important;
  }
}
.users-orders > p {
  font-size: 12px;
  border-bottom: 1px solid #666;
  padding-bottom: 0.5rem;
}
.payment-details{
  margin-top: 10px !important;
  span{
    color: @prep-white !important;
  }
}
.media-body{
  margin-left: 20px !important;
}
.page_title{
  h4{
    color: @prep-white;
  }
}

.my_books{
  min-height: 60vh;
}
.mdl-tabs.side-column .sidebar-background{
  background: @prep-secondary !important;
  border: 1px solid rgba(255,255,255,0.5);
  border-radius: 10px;
}
#relatedBooksContainer .bg-light,
#bestSellerBooksContainer .bg-light{
  background: @prep-secondary !important;
}
.related-book-wrapper{
  background: transparent !important;
}
#htmlreadingcontent iframe{
  background: @white !important;
}

.publishing_desk {
  table tbody, .table tbody {
    color: @white !important;
  }
  .main{
    margin: 10px;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%);
    border-radius: 4px;
    border: 1px solid rgba(255,255,255,0.5);
  }
  .nav-pills .nav-link.active{
    background-color: @prep-red !important;
    border-color: @prep-red !important;
  }
}
.pub-desks {
  .nav-tabs {
    border-bottom: none;
    padding-left: 15px;
  }
  .nav-link{
    border-color: rgba(255,255,255,0.2);
    color:@white;
  }
}
.page-item.disabled .page-link{
  color: #6c757d !important;
}

.light11{
  background: @prep-red !important;
  border-color:@prep-red !important;
}

.typeahead a{
  color: #000!important;
}
.mdl-layout__tab, .mdl-tabs__tab{
  color: #848484!important;
}
.nav-tabs a{
  color: #848484;
}
.mdl-tabs.side-column .sidebar-background{
  margin-top: 70px;
}
.new_book_create .container {
  padding: 0!important;
  border: 1px solid rgba(255,255,255,0.5) !important;
}
.new_book_create #pills-tab{
  padding-left: 0!important;
  background: transparent !important;
  border-bottom: 1px solid rgba(255,255,255,0.5);
}
.new_book_create #pills-tab .nav-item{
  padding-left: 0!important;
  padding-right: 5px !important;
}
.nav-pills .nav-link.active,
.nav-pills .show>.nav-link{
  background-color:@prep-red;
}
.book_details_info div .btn-primary,
.book_details_info #nxt-btn,
.chapter-wrapper .btn-primary{
  background: @prep-red !important;
  border-color:@prep-red !important;
}
#addedtags,#chapter-resources,#goBack {
  color: @white !important;
}
.chapter-wrapper {
  border: 1px solid rgba(255,255,255,0.5);
  padding: 5px;
  border-radius: 4px;
  background: rgba(211, 232, 249, 0.5);
}
#chapterdetailsdetails .col-6{
  display: flex;
  flex-direction: column;
}
#chapterdetailsdetails .col-6 a{
  text-decoration: underline !important;
  margin-bottom: 10px !important;
}
#chapter-resources tr td a.btn.btn-primary{
  background: @prep-red !important;
  border-color:@prep-red !important;
  color:@white !important;
}
#chapter-resources a{
  color: @white !important;
}
#published a,
#notpublished a,
.saveCard .saveSubmit{
  background: @prep-red !important;
  border-color:@prep-red !important;
  color:@white !important;
  flex-direction: row !important;
}
.all-container .container-wrapper div > .media .title{
  color: #444444 !important;
}
.all-container .container-wrapper div > .media a{
  color: #000000b3 !important;
}
.app-header__questionsQue h1, h2, h3, h4, h5, h6,
.question-wrapper p,
.mcq-answers,
#leaderBoardList p,
.leaderBoard__wrapper-navigations__card a .card__sec-1 p,
.leaderboard .calendar_wrapper #selectedCurrentDate a,
.noRanksMsg h4,.noRanksMsg p{
  color: #000 !important;
}
.content-wrapper h6,
.purchase-details,
.leaderboard-sidebar h6,
#totalBooksCount{
  color: #fff!important;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book{
  border-left: none !important;
}
.leaderboard h4 strong{
  color: @white !important;
}
.leaderboard-share{
  background-color: @prep-red !important;
}
.leaderboard .filter-wrapper a{
  color: #212121!important;
}
.leaderboard .filter-wrapper a.active{
  color: @white!important;
}
.leaderboard thead tr {
  background: transparent !important;
}
.cart_checkout .order_summary p,
.cartItems .book_desc p,
.cartItems .book_desc .book_title h5 a,
.cartItems .book_desc h5{
  color: #000 !important;
}
.cartItems .book_desc .book_title h5 a{
  color: #000 !important;
}
.book_discount_btn {
  color: #6C757D !important;
}
.book_title h5 a{
  color: #6C757D !important;
}
.book_publisher,
.continue_shop_btn,
#discountPriceFinal,
.use_ebooks_text h5{
  color: #6C757D !important;
}
.book_publisher a{
  color: #6C757D !important;
}
.delete_item a{
  color: #CE0000 !important;
}
#discountAnimation{
  z-index: -1 !important;
}
//.books-list .content-wrapper .add_to_cart_btn img{
//  filter: contrast(4) invert(1);
//  -webkit-filter: contrast(4) invert(1);
//}

.books-list .content-wrapper .add_to_cart_btn {
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000b3 !important;
  margin: auto;
  font-size: 12px;
  border: 1px solid #000000b3;
  position: relative;
  padding: 3px;
  bottom: -3px;
}
.books-list .content-wrapper .add_to_cart_btn:hover{
  background:@prep-red !important;
  color: @white !important;
  border-color: @prep-red !important;;
}
.books-list .content-wrapper .add_to_cart_btn:hover img{
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.my_books .books-content-wrapper{
  padding: 15px;
}
.left-div a h6{
  color: #212121 !important;
}
.left-div .book-publisher-name{
  color: #aeaeae !important;
}
.ebooks #content-data-books-ebooks .fadein-animated{
  background: #f4f4f4 !important;
}
.books-list .content-wrapper p.price{
  color: #212121 !important;
}
#orders-wrapper h3,
.mcq-name,
.shopping_cart h3,
.order_information h5,
.purchase-heading h3,
.purchase-details .instructions h5,
.ws_resultPage-title h2,
.ws_result-performance_title,
.daily__test-header__title .text-primary-modifier,
#backToall, #backfromnotes, #backfromAllnotes,
#relatedBooksContainer h5,
#bestSellerBooksContainer h5,
#relatedBooksContainer a,
#bestSellerBooksContainer a,
#revisionTitle,
#description,
.public-text,
.hero-title,
#wsResultPointsCount,
.my_books h4,
.leaderBoard__title h3,
.ws-next,
.ws-previous,
.flashcard-set a,
.flashcard-set p,
.mynotes h3,
.mynotes li,
.fav_title h2,
.fav_mcqsList h4,
#historyList h4,
.tests-access h6,
.ebook-access h6,
.read-book-chapters-wrapper li,
.ws-progressbar .ws-next i,
.ws-progressbar .ws-previous i,
#amazonBooksTitle h5,
#timer,
.backfromcard,
.suggested-videos h6,
.my-activity h3 strong,
.bookchapterHeader,
#resourceNameDiv,
.recommendedBooks-title,
#bronze,
#silver,
#gold {
  color: @white !important;
}
.library_book,
#ebook_AddToCartBtn{
  color: #444 !important;
}
.library-book a{
  color: #000 !important;
}
.library-book a.btn-outline-primary:hover{
  color: @white !important;
}
#cartModalLibBtn .btn-primary,
.save-card,
.my_books #libraryTabs a.current{
  background: @prep-red !important;
}

.review-wrap .text-quote,
.fav_mcqs-card_question p,
.fav_mcqs-card_option p,
.fav_mcqs-card_option,
.pointsSummary p,
.mobile_summary p,
.mobile_view_summary a,
.que-header,
#add-createTest input,
.book_variants a.btn-light,
.preview-book-btns .buy p,
.flip-box-back p,
.inner-container-show-1 p,
.que-options p,
.flashcard-set p,
.flashcard-set h4,
#secondUserDetail p,
#firstUserDetail p,
#thirdUserDetail p,
.complte-book p,
.unlock-info h5,
.unlock-info p,
#startMatch h3,
#startMatch p,
.playmatch p,
.bootstrap-select p,
.bootstrap-select a,
.video-wrapper p,
#htmlContent .ptyyy p,
#expandFlashcard p,
#expandFlashcard,
#finishMatch p,
#finishMatch h2,
.ck-editor__editable p,
#add-createTest #htmlContent,
.show-explanation p,
.show-explanation h5,
.show-explanation h2{
  color: #000 !important;
}
.review-wrap .box p{
  color: #000 !important;
}
.book-cart-modal .book_variants .card-body {
  min-height: 105px;
}
#videoModal .modal-dialog{
  margin-top: 230px;
}
.addwebmcq,
#add-createTest,
#htmlContent{
  background: #fff !important;
}
.addwebmcq p{
  color: #000 !important;
}
.an-header {
  background: transparent !important;
}
.slider-actions button:focus span,
.slider-actions button:hover span,
.slider-actions button:focus i,
.slider-actions button:hover i{
  color: @white !important;
}
.ws-input input:focus ~ label.ws-label{
  color: @white !important;
}

#successModal .modal-body p,
#successModal .modal-body p #msg-updated{
  color: #27AE60!important;
}
#htmlreadingcontent .btn-back{
  background:@white!important; ;
}
.left-div h6{
  color: #000 !important;
}

#webMcq{
  overflow: scroll;
}
.flDescription{
  color: #fff!important;
}
.mdl-tabs.side-column .sidebar-background{
  overflow: scroll;
}
.mcqRead h4,
.mcqRead h5,
.mcqRead p,
.mcqRead li{
  color: #000 !important;
}
.inner-ques p,
.inner-ques ul li,
.inner-ques h5{
  color: #000 !important;
}
.questionBlock p,
.questionBlock h5{
  color: #000 !important;
}
.ptyyy i{
  color: #000 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
  .leaderboard-share {
    z-index: 9999;
  }
}

// Print books list
.print-books {
  h1 {
    color: @prep-primary !important;
  }
  h4 {
    color: #FFF !important;
  }
  .category-lists {
    a.btn {
      color: @prep-primary !important;
      &:hover, &:focus, &:active, &:active:focus {
        border-color: #ff6b41 !important;
        background-color: #ff9475 !important;
      }
    }
  }
  .topSchoolBooks {
    h6 {
      a {
        color: #000 !important;
      }
    }
    &:hover {
      h6 {
        color: #ff6b41 !important;
        a {
          color: #ff6b41 !important;
        }
      }
    }
    .book-publisher-name {
      a {
        color: #949494 !important;
      }
    }
  }
  #emptyState {
    #emptyBox {
      margin: 0 auto 15px !important;
    }
  }
  .books-list {
    padding: 0;
  }
  .breadcrumb {
    li.breadcrumb-item {
      a:hover {
        color: @prep-red !important;
      }
    }
  }
}

// Print book detail
.amazonPrice, .flipkartPrice {
  p {
    color: @prep-primary !important;
  }
}
.customerReviews {
  .card {
    a, p {
      color: @prep-primary !important;
    }
  }
}

.subscription__sec label{
  color: #000;
}
.book_briefDetail{
  h2{
    color: #000 !important;
  }
  .book-publisher-name{
    color: black !important;
    a{
      color:#007bff!important; ;
    }
  }
}
.freeChapterLink{
  color: #000 !important;
}
.shippingAdressWrapper h3{
  color: inherit !important;
}
.shippingAdressWrapper a{
  color: #007bff !important;
}

.page-main-wrapper.information{
  background: #fff !important;
}

#goBack,
.showmoretab .info-description p,
#contents .info-description p {
  color: #000 !important;
}
.created-date,
.emptyContent{
  color: #999 !important;
}

.show-info-details,
.next-info-link{
  color: blue !important;
}
.information #contents .info-title{
  padding-top: 10px !important;
  color: #212121 !important;
}
.next-info-link a,
.prev-info-link a{
  color: blue !important;
}
.information #contents .info-description a{
  color: blue !important;
}

#batchUsers table tr td{
  color: #fff !important;
}
#section1Container table tr td,
#section2Container table tr td,
#section3Container table tr td,
#section4Container table tr td,
label{
  color: #fff !important;
}

.quizCreatorBulkInput .main .radioQuestion p,
label.checkbox{
  color: #000 !important;
}
#salesData_paginate  ul li a{
  color: #000 !important;
}

.mockTestListItem,
.leaderBoardTitle,
#infoSec *,
.mockTests__currentMonth-title,
.allMockTests h5,
.suggestedBooksTitle,
.showMoreText,
.currentMonthItemWrapper h3,
.openTestBtn{
  color: #fff !important;
}
.accordion-item button p,
.accordion-content p,
.examCard__examName p,
.examActionItem .card-title p{
  color: #000 !important;
}
.quizInfo p{
  color: rgba(0, 0, 0, 0.3) !important;
}
.currentMonthItem {
  box-shadow: 0 2px 2px rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.44) !important;
}
.booksList__items-item{
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
}