@prep-primary: #0F0839;
@prep-secondary:#04001D;
@prep-white:#fff;
@prep-red:#E83500;
.prepjoySites{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70vh;
  margin: 0 auto;

  @media screen and (max-width: 820px){
    height: 73vh !important;
  }
  @media screen and (max-width: 767px){
    height: 70vh !important;
  }
  @media screen and (max-width: 415px){
    height: 73vh !important;
  }
  &-wrapper{
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-shadow: 0 3px 4px 0 rgba( 31, 38, 135, 0.17 ) !important;
    backdrop-filter: blur( 2px ) !important;
    -webkit-backdrop-filter: blur( 2px ) !important;
    border-radius: 5px !important;
    border: 1px solid rgba( 255, 255, 255, 0.18) !important;
    height: 80%;
    &__itemOne{
      margin-top: auto;
      img{
        width: 150px;
      }
      h2{
        font-size: 33px;
      }
      p{
        font-size: 1.2rem;
        color: #9999 !important;
      }

      @media screen and (max-width: 767px){
       h2{
         font-size: 21px;
       }
        img{
          width: 100px;
        }
      }
    }

    &__itemTwo{
      margin-top: 2rem;
      a{
        padding: 3px;
        //border: 1px solid #f4f4f4;
        border: 1px solid #9999;
        box-sizing: border-box;
        width: 150px;
        border-radius: 5px;
        text-align: center;
        img{
          margin-right: 3px;
        }
        p{
          font-size: 10px !important;
        }
        h4{
          font-size: 12px !important;
        }
      }
    }

    &__itemThree{
      margin-top: auto;
      margin-bottom: auto;
      a{
        width: 300px;
        color: #fff;
        border-radius: 5px;
        border: 1px solid @prep-red;
        padding: 1rem;
        transition: all 0.3s ease;
        &:hover{
          color: @prep-red;
          border-color: #9999;
        }
      }
    }
  }
}