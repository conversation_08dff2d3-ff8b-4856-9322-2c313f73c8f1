@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";

@ws-header-height:64px;
@ws-mainMenu-height:48px;
#app-wrapper{
  padding: 1rem 0.5rem;
  border-bottom: 1px solid #ededed;
  .get-app{
    background: @ws-lightOrange;
    color:@ws-white;
    font-size: 10px;
    padding: 0.3rem 0.5rem;
    outline:0;
    border:none;
    border-radius: 4px;
    text-transform: uppercase;
    font-weight: bold;
  }
  h4{
    font-size:12px;
    margin-bottom: 0;
  }
  p{
    font-size: 10px;
    margin-bottom: 0;
  }
  button{
    border:none;
    background: none;
    outline:0;
    i{
      font-size: 16px;
    }
  }
}
header{
  width:100%;
  z-index: 999;
  background: @ws-white;
  top:0;
  box-shadow:0 4px 4px rgba(0, 0, 0, 0.2), 0 0 rgba(0, 0, 0, 0.1);
&.normalHeader{
  box-shadow: none;
  position: relative;
  .ws-header{
    max-width: 100%;
  }
}
}
.ws-header {
  height:@ws-header-height;
  background:@ws-white;
  div {
    &.mobile-profile {
      display: flex;
      position: absolute;
      z-index: 9999;
      right: 10px;
      @media @iphoneX-landscape{
        display: block !important;
      }
      @media @iPhone,@iPhone6-portrait,@iPhone7-portrait{
        .dropdown-menu-right{
          right:-10px;
          min-width: 353px;
          padding-bottom: 0;
          margin-top: 4px;
          margin-left: auto;
          .media{
            padding-bottom: 0 !important;
            position: relative;
            border-bottom: 1px solid @ws-border;
            .drop-profile{
              width: 42px;
              height:42px;

            }
            .edit-btn{
              position: absolute;
              bottom: 2rem;
              left: 3rem;
              width: 15px;
              height: 15px;
              display: inline-block;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.94);
              box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);

              text-align: center;
              i{
                font-size: 9px;
                position: relative;
                top:1px;
                color:fade(@ws-darkBlack,75%);
              }
            }
          }
          .dropdown-item{
            padding: 1rem 1.5rem;
            border-bottom: 1px solid @ws-border;
            color:@ws-darkBlack;
            font-weight: normal;
            &:first-child{
              border-top: 1px solid @ws-border;
            }
            &:last-child{
              border-bottom: none;
            }
          }
        }
      }
      .nav-item {
        list-style-type: none;
        a {
          @media @iPhone{
            font-size: 10px;
            padding: 0;
          }
          img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
  .navbar-nav {
    .nav-item{
      &.active{
        background-color: @ws-white;
      }
    }
      .nav-link {
        color: @ws-darkBlack;
        font-family: @ws-header-font;
        font-size: @ws-header-fontSize;
        padding-right: 1.5rem;
        padding-left: 1.5rem;
        cursor: pointer;
        white-space: nowrap;
        &:hover {
          color: @ws-lightOrange;
          transition: all 0.5s;
        }
        @media @iPad-portrait {
          padding-right: .5rem;
          padding-left: .5rem;
          font-size: @ws-header-fontSize - 2;
        }
      }
      &.right-menu {
        font-size: @ws-header-fontSize - 2;
        font-family: @ws-header-font;
        li {
          &.notification{
            width: 24px;
            height: 24px;
            display: inline-block;
            border-radius: 50%;
            background: fade(@ws-darkBlack,54%);
            a{
              padding: 0;
              text-align: center;
              cursor: pointer;
            }
            i{
              color:@ws-white;
              font-size: @ws-menu-fontSize;
              margin-top:5px;
            }
          }
          &.dropdown{
            .dropdown-menu{
              min-width:370px;
              padding-bottom: 0;
              margin-top:-5px;
              margin-left: auto;
              .media{
                border-bottom: 1px solid @ws-border;
                position: relative;
                .media-body{
                  p{
                    color:@ws-darkBlack;
                    font-family: @ws-header-font;
                    font-size: @ws-header-fontSize;
                  }
                }
              }
              .dropdown-item{
                padding: 1rem 1.5rem;
                border-bottom: 1px solid @ws-border;
                color:@ws-darkBlack;
                font-weight: normal;
                span{
                  color:@ws-lightOrange;
                }
                &:last-child{
                  border-bottom: none;
                }
              }
              a{
                position: relative;
              }
              .edit-btn {
                width: 20px;
                height: 20px;
                display: inline-block;
                border-radius: 50%;
                background: fade(@ws-white, 94%);
                box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
                position: absolute;
                left:70px;
                bottom: 30px;
                text-align: center;
                @media @iPhone{
                  right: 80px;
                }
                i{
                  color:fade(@ws-darkBlack,74%);
                  font-size: @ws-menu-fontSize - 2;
                }
              }
            }
          }
          a {
            font-weight: @ws-header-fontWeight;
            img{
              width: 46px;
              height: 46px;
            }
          }
          img{
            &.drop-profile{
                width: 72px;
                height: 72px;
            }
          }
          &:first-child {
            a {
              color: @ws-lightOrange;
            }
          }
          &:last-child {
            a {
              color: @ws-darkBlue;
              cursor: pointer;
            }
          }
        }
      }
      @media @iphoneX-landscape {
        &.mr-auto, &.right-menu {
          display: none !important;
        }
      }
    }
    @media @iPhone {
      border-bottom: 1px solid fade(@ws-darkBlack, 10%);
    }
  }
.main-menu{
  height:@ws-mainMenu-height;
  background:@ws-white;
  box-shadow:0 4px 4px rgba(0, 0, 0, 0.2), 0 0 rgba(0, 0, 0, 0.1);
  .navbar-nav{
    .nav-item{
      &:active{
        background: transparent;
      }
      &:first-child{
        .nav-link{
          border-right: 1px solid fade(@ws-darkBlack, 20%);
        }
      }
      &:last-child{
        .nav-link{
          border-left: 1px solid fade(@ws-darkBlack, 20%);
        }
      }
    }
    .nav-link{
      color: @ws-darkBlack;
      font-family: @ws-header-font;
      font-size: @ws-menu-fontSize;
      padding-right:1.5rem;
      padding-left:1.5rem;
      &:hover{
        color:@ws-lightOrange;
        transition: all 0.5s;
        &:after{
          color:@ws-lightOrange;
        }
      }
      @media @iPad-portrait{
        padding-right:.5rem;
        padding-left:.5rem;
      }
    }
  }
  .dropdown-toggle::after{
    color:fade(@ws-darkBlack,80%);
  }
  @media @iphoneX-landscape{
    display: none !important;
  }
}
@media @iphoneX-landscape{
  .mobile-toggle{
    display: flex !important;
    justify-content: flex-end;
  }
  .sidenav{
    display: block !important;
  }
}
@media @iPhone6-landscape,@iPhone7-landscape,@iphoneX-landscape{
  .sidenav{
    .closebtn{
      right:10% !important;
    }
  }
}
@media @iPhone,@iPhone5-landscape,@iPhone6-landscape,@iPhone7-landscape,@iphoneX-landscape{
  .logo{
    position: relative;
    z-index: 9999;
  }
  .sidenav {
    height: 100vh;
    width: 0;
    position: fixed;
    z-index: 999;
    top: 0;
    right: 0;
    background-color: @ws-white;
    overflow-x: hidden;
    transition: 0.5s;
    padding: 0;
    border: 1px solid @ws-border;
    >div{
      margin-top: 4rem;
      li{
         text-align: center;
      }
    }
  }
  .sidenav a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: @ws-menu-fontSize;
    display: block;
    transition: 0.3s;
    i{
      color:@ws-darkBlack;
    }
  }
  .sidenav .closebtn {
    position: absolute;
    top: 0;
    right: 8px;
    font-size: 30px;
    margin-left: 50px;
  }
}
.ws-mob-menu {
  display: none;
  >li{
    >a{
      color: @ws-darkBlue;
      font-size: @ws-menu-fontSize;
      font-family: @ws-header-font;
      padding-bottom: 1rem;
    }
  }
  .sub-menu {
    display: none;
  }
  #mobile-menu {
    padding: 0;
    border-top: 2px solid @ws-border;
    > li {
      list-style-type: none;
      padding: 8px 0px;
      text-align: left;
      background: #ffffff;
      border-bottom: 1px solid @ws-border;
      &.toggle-icon{
        >a{
          &:after{
            content:url('../../images/landingpageImages/arrow-down.svg');
          }
        }
        &.default-icon{
          >a{
            &:after{
              content:url('../../images/landingpageImages/arrow-up.svg');
            }
          }
        }
      }
      > a {
        color: @ws-darkBlack;
        font-size: @ws-menu-fontSize;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        position: relative;
        &:after {
          content: url('../../images/landingpageImages/arrow-down.svg');
          float: right;
          position: absolute;
          top: 0;
          right: 0;
          padding: 8px 16px;
          height: 100%;
          border-left:1px solid @ws-border;
        }
      }
      &:last-child {
        border-bottom: 1px solid @ws-border;
      }
      ul {
        padding: 0;
        li {
          list-style-type: none;
          border-bottom: 1px solid @ws-border;
          margin-left: 2rem;
          padding: 6px 0px;
          &:last-child{
            border:none;
          }
          a{
            color: @ws-darkBlack;
            font-size: @ws-menu-fontSize;
            font-family: @ws-header-font;
            text-align: left;
          }
        }
      }
    }
  }
}

/*Temp hide*/
.notification{
  display: none !important;
}
.eutkarsh{
  header{
    &.normalHeader{
      background: @ws-lightOrange;

    }
    .ws-header{
      background: @ws-lightOrange;
      .navbar-nav{
        .nav-link{
          color:@ws-white;
        }
        &.right-menu{
          li:last-child a{
            color:@ws-white;
          }
        }
      }
    }
  }
}
.mobile-menu{
  position: fixed !important;
  ul{
    width: 100%;
  }
}
