@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";
header,#ws-ebooks,.footer-nav,footer{
  //display: none;
}
.bookTemplate {
  height:100vh;
  .shadowHeader{
    position:sticky;
    position: -webkit-sticky;
    background: #ffffff;
    height: 50px;
    width: 100%;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    z-index: 99;
    top:0;
    @media @iPhone{
      top:48px;
    }
  }
  .ChapterHeader{
    .bookTitle{
      padding: 5px;
      font-family: @ws-header-font;
      font-size: 12px;
      color:fade(@ws-darkBlack,72%);
      margin-bottom: 0;
      @media @iPad-portrait{
       font-size: 10px;
        padding-right: 0;
        padding-top: 0;
      }
    }
  }
  .tab-header {
    .nav-tabs .nav-link{
      border:none;
      border-radius: 0;
    }
    .nav-tabs,.navbar-nav{
      overflow-y: hidden;
      overflow-x: auto;
    }
    @media @iPhone {
      .nav-tabs {
        width: 100%;
        margin-top: 5px;
      }

      .navbar-nav {
        width: 20%;
        justify-content: space-around;
        border-left:1px solid fade(@ws-darkBlack,10%);
      }
    }
    .contentEdit{

      @media @iPhone{
        width: 0;
        border-left: none !important;
      li:last-child{
        //display: none;
      }
      #notesMenu {
        padding-left: 38px;
      }
    }
      i{
        color:fade(@ws-darkBlack,72%);
        @media @iPad-portrait,@iPhone{
          font-size: 21px;
        }
      }
      .dropdown-menu{
        padding-bottom: 0;
        >div{
         padding: 1rem;
          &:last-child{
            padding: 0;
          }
        }
      }
    }
    a {
      &.book-name {
        color: fade(@ws-darkBlack, 72%);
        font-family: @ws-header-font;
        font-size: @ws-menu-fontSize - 2;
      }
      max-width: 250px;
    }
    .navbar{
      padding: .5rem 0;
    }
    .navbar{
      li{
        &.active{
          i{
            color:@ws-lightOrange;
          }
        }
      }
    }

  }
  .menu {
    border-bottom: none;
    @media @iPad-portrait{
      display: flex;
      flex-wrap: nowrap;
      overflow-x:auto ;
      overflow-y: hidden;
    }
    li {
      margin-bottom: 0;
      @media @iPad-portrait,@iPhone{
        padding: 0;
      }
      padding: 0 1rem;
      &:hover {
        a {
          background: transparent;
          border: transparent;
        }
      }
      a {
        white-space: nowrap;
        @media @iPad-portrait,@iPhone{
         font-size:14px;
        }
        color: @ws-darkBlack;
        font-size: @ws-header-fontSize;
        font-family: @ws-header-font;

      }
    }
    &.nav-tabs {
      .nav-item {
        &.show {
          .nav-link {
            color: @ws-lightOrange;
            background: transparent;
            border:none;
            border-bottom: 2px solid @ws-lightOrange;
          }
        }
      }
      .nav-link {
        &.active {
          color: @ws-lightOrange;
          background: transparent;
          border:none;
          border-bottom: 5px solid @ws-lightOrange;
          //padding: 0.9rem 1rem;
        }
      }
    }
  }
  .content-wrapper{
    position: relative;
    .price-wrapper{
      position: sticky;
      position: -webkit-sticky;
      bottom:0;
      z-index: 9;
      @media @iPhone,@iPhone5-landscape{
        width: 100%;
      }
      padding: 0.5rem 1rem;
      .section-btns{
        width:100%;
        padding: 0.5rem 0rem;
        //background: @ws-white;
        @media @iPhone{
          //background: @ws-white;
        }
        a{
          color:@ws-darkBlack;
          &:hover{
            text-decoration: none;
            color:@ws-lightOrange;
          }
          .prev_chap, .next_chap {
            position: relative;
            bottom: -7px;
          }
        }
      }
    }
    .chapterSection{
      position: fixed;
      z-index: 9;
      top:50px;
      @media @iPad-portrait{
        width: 250px;
      }
      @media @iPad-landscape{
        width: 300px;
      }
      @media @iPhone{
        position: fixed;
        z-index: 99;
        top:0;
      }
      a.slide-toggle{
        position: fixed;
        height: 30px;
        width: 85px;
        top: 170px;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #6F6F6F;
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -o-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        z-index: 9;
        &.left{
          left:-27px;
        }
        @media @iPad-portrait{
          left:28%;
        }
        @media @iPhone{
          left:56%;
        }
      }

    }
    #book-sidebar{
      width: 100%;
      background: #f5f5f5;
      height:100vh;
      position: relative;
      padding-bottom: 2rem;
      @media @iPhone{
        width: 100%;
      }
      @media @iPad-portrait,@iPad-landscape{
        width: 250px;
      }
      &.closed {
        a.slide-toggle {

        }
        .side-content{
          display: none;

        }
      }

      .side-content{
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        .backtolibrary{
          color:@ws-darkBlack;
          font-size: 12px;
          display: flex;
          align-items: center;
          i{
            font-size: 12px;
            color:@ws-darkBlack;

          }
        }
        > h2{
          color:fade(@ws-darkBlack,40%);
          font-family: @ws-header-font;
          font-size: @ws-menu-fontSize + 2;
          font-weight: normal;
          padding: 1rem;
          border-bottom: 0.5px solid fade(@ws-darkBlack,20%);
          background: #f5f5f5;
          position: sticky;
          position: -webkit-sticky;
          top:0;
          margin-top: 60px;
          @media @iPhone,@iPhone6-landscape{
            margin-top: 0;
            display: none;
          }
        }
        ol{
          @media @iPhone,@iPhone6-landscape{
            margin: 0 !important;
            padding: 0 !important;
          }
          > li{
            padding: 8px 4px;
            &.chapter-name{
              &.orangeText{
                color:@ws-lightOrange;
                a{
                  color:@ws-lightOrange;
                }
              }
              color:fade(@ws-darkBlack,40%);
              font-size: @ws-header-fontSize;
              font-family: @ws-header-font;
              word-break: break-all;
              @media @iPhone,@iPhone6-landscape{
                border-bottom: 1px solid rgba(68, 68, 68, 0.1);
                margin: 0.5rem 2rem;
              }
              i{
                font-size: 20px;
              }
            }
            a{
              color:@ws-darkBlack;
              font-size: @ws-header-fontSize;
              font-family: @ws-header-font;
              word-break: break-word;
              &:hover{
                text-decoration: none;
                color:@ws-lightOrange;
              }
            }
            ul{
              padding: 10px;
              @media @iPhone,@iPhone6-landscape{
                padding: 7px;
              }
              li{
                color:@ws-darkBlack;
                font-size: @ws-header-fontSize - 2;
                font-family: @ws-header-font;
                list-style-type: disc;
                padding: 12px 0;
                a{
                  color:@ws-darkBlack;
                  font-size: @ws-header-fontSize - 2;
                  font-family: @ws-header-font;
                  &.orangeText{
                    color:@ws-lightOrange;
                  }
                }
              }
            }
          }
        }

      }


      .mobile-title{
        color: rgba(68, 68, 68, 0.4);
        font-family: 'Rubik', sans-serif;
        font-size: 16px;
        font-weight: normal;
        padding: 1rem;
        border-bottom: 0.5px solid rgba(68, 68, 68, 0.2);
        background: #f5f5f5;
        position: sticky;
        position: -webkit-sticky;
        top: 0;
        margin-top: 60px;
        p{
          margin-bottom: 0;
          color:@ws-darkBlack;
        }
      }
    }
  }
  .read-content{
  }
  .export-notes{
    .no-notes-created{
      text-align: center;
    }
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    width: 400px;
    background: @ws-white;
    right: 0;
    top:110px;
    z-index: 99;
    padding-bottom: 2rem;
     @media @iPhone{
       width: 250px;
     }
    .close-notes{
      position: fixed;
      right: calc(400px - 1rem);
      background: #fff;
      width: 35px;
      height: 35px;
      border-radius: 50px;
      border: none;
      outline: 0;
      z-index: 9;
      margin-top: 3rem;
      cursor: pointer;
      @media @iPhone{
        right: calc(250px - 15px);
      }
    i{
      font-size: 18px;
      line-height: 1.5;
    }
    }
    .export-study-set{
      color:@ws-lightOrange;
      font-weight: @ws-header-fontWeight;
      font-size: 14px;
    }
    .notes-creation-header-title{
     color:fade(@ws-darkBlack,72%);
      font-weight: @ws-header-fontWeight;
      font-size: 20px;
    }

  }
}
#overlay {
  position: fixed; /* Sit on top of the page content */
   /* Hidden by default */
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5); /* Black background with opacity */
  z-index: 99; /* Specify a stack order in case you're using a different order for other elements */
  cursor: pointer; /* Add a pointer on hover */
}

#searchVideos {
  h3{
    text-align: center;
    margin-bottom: 4rem;
    font-family: @ws-header-font;
    font-size: 16px;
  }
  .test-pr{
    width: 75%;
    margin: 2rem auto;
  }
  .overlay-pr {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background: rgba(68, 68, 68, 0.5);

    .play-btn-wrapper {
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
  .video-wrapper{
    display: flex;
    flex-wrap: wrap;
    .video-img-wrapper{
      position: relative;
    }
    a{
      &:hover {
        text-decoration: none;
      }
      span{
        color:@ws-darkBlack;
      }
    }
  }
  .play-btn-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    img {
      width: 26px;
      height: 25px;
    }

    span {
      margin-left: 0.5rem;
    }
  }

  .videoTOadd {
    display: flex;
    align-items: center;

    img {
      width: 24px;
      height: 16px;
    }
    color:@ws-darkBlack;
  }

  .pr-flex {
    display: flex;
    justify-content: space-between;
    .dpr-none {
      display: none;
      img{
        width: 24px;
        height: 16px;
        margin-right: 0.3rem;
      }
    }
  }
}
.preview-book-btns{
  .card-wrapper{
    display: flex;
    align-items: center;
    justify-content: space-between;
    //position: fixed;
    bottom:0;
    background: @ws-white;
    box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24);
    z-index: 99;
    padding: 0 1rem;
    .complte-book{
      h4{
        font-size: 10px;
        font-family: @ws-header-font;
        font-weight: 500;
        color:fade(@ws-darkBlack,72%);
      }
      p{
        color:#B72319;
        font-size: 18px;
        font-family: @ws-banner-font;
        margin: 0;
      }
    }
  }
}
.video-url {
  .modal-header{
    h4{
      font-size: 18px;
    }
  }
  #videoForm {
    text-align: center;
    input {
      margin: 0.5rem auto;
      padding: 0.3rem;
    }
  }
}
#flashCardModal {
  .modal-header{
    display: block;
    h4{

    }
    .close{
      padding: 0;
      margin: 0;
    }
  }

  .carousel-indicators{
    display: none;
  }
  .flash-card-slider {
    position: relative;
    max-width: 440px;
    margin: 0 auto;
  }

  .flash-card-slider .carousel-inner {
    max-width: 440px;
    margin: 40px auto 0;
  }

  .flash-card-slider .item {
    min-height: 316px;
    padding: 24px;
  }

  .flash-card-slider .carousel-control {
    top: auto;
    background: #1F419B;
    opacity: 1;
    width: 24px;
    height: 24px;
    border-radius: 100%;
    color:@ws-white;
    &:focus{
    background:#1F419B !important;
    }
  }

  .flash-card-slider .carousel-btn-disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65;
  }

  .flash-card-slider .carousel-control-wrapper {
    width: 208px;
    margin: 0 auto;
    float: none;
    display: flex;
    justify-content: space-around;
  }

  .flash-card-slider .card-number {
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    color: rgba(68, 68, 68, 0.74);
  }

  .flip-container {
    background: #FFFFFF;
    -webkit-perspective: 1000;
    -moz-perspective: 1000;
    -o-perspective: 1000;
    perspective: 1000;
  }

  .flip .flipper {
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
  }

  .flip-container, .front, .back {
    width: 400px;
    min-height: 216px;
    @media @iPhone{
      width:100%;
    }
  }

  .flipper {
    min-height: 216px;
    background: transparent;
    box-sizing: border-box;
    border-radius: 4px;
    -webkit-transition: 0.6s;
    -webkit-transform-style: preserve-3d;
    -moz-transition: 0.6s;
    -moz-transform-style: preserve-3d;
    -o-transition: 0.6s;
    -o-transform-style: preserve-3d;
    transition: 0.6s;
    transform-style: preserve-3d;
    position: relative;
  }

  .front, .back {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -o-backface-visibility: hidden;
    backface-visibility: hidden;
    top: 0;
    left: 0;
    position: relative;
    text-align: center;
    border: 0.5px solid rgba(189, 189, 189, 0.55);
    border-radius: 4px;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    height: 216px;
    overflow: hidden;
  }

  .front {
    position: absolute;
    z-index: 2;
  }

  .front span {
    display: flex;
  }

  .back {
    -webkit-transform: rotateY(180deg);
    -moz-transform: rotateY(180deg);
    -o-transform: rotateY(180deg);
    transform: rotateY(180deg);
    position: absolute;
  }

  .front .name {
    font-size: 16px;
    text-shadow: none;
    color: #000;
    padding: 0 16px;
    display: flex;
    align-items: center;
    height: 100%;
    overflow: auto;
    justify-content: center;
  }

  .back-logo {
    position: absolute;
    top: 40px;
    left: 90px;
    width: 160px;
    height: 117px;
  }

  .back-title {
    color: #000;
    text-align: center;
    font-size: 16px;
    padding: 0 16px;
    display: flex;
    justify-content: center;
    height: 100%;
    overflow: auto;
    padding-top: 40px;
    padding-bottom: 20px;
  }

  .flip-card-btn {
    width: 112px;
    position: absolute;
    left: 0;
    right: 0;
    display: block;
    margin: 0 auto;
    font-size: 16px;
    bottom: -16px;
    text-align: center;
    color: #FFFFFF;
    background: #1F419B;
    padding: 8px 0;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 63px;
    z-index: 5;
   &:focus{
     background: #1F419B !important;
     color:@ws-white;
   }
  }

  .flip-card-btn:hover {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none;
  }

  .flip-card-btn:active {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none;
  }

  .flip-card-btn:focus {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none;
  }

  .flip-card-btn > i {
    font-size: 16px;
    vertical-align: top;
  }

  .btn-flip {
    transform: rotateY(180deg);
  }

  .back p {
    position: absolute;
    bottom: 40px;
    left: 0;
    right: 0;
    text-align: center;
    padding: 0 20px;
    font-family: arial;
    line-height: 2em;
  }

  .flash-card-slider {
    position: relative;
    max-width: 440px;
    margin: 0 auto;
  }

  .flash-card-slider .carousel-inner {
    max-width: 440px;
    margin: 40px auto 0;
  }

  .flash-card-slider .item {
    min-height: 316px;
    padding: 24px;
  }

  .flash-card-slider .carousel-control {
    top: auto;
    background: #1F419B;
    opacity: 1;
    width: 24px;
    height: 24px;
    border-radius: 100%;
  }

  .flash-card-slider .carousel-btn-disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65;
  }

  .flash-card-slider .carousel-control-wrapper {
    width: 208px;
    margin: 0 auto;
    float: none;
  }

  .flash-card-slider .card-number {
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    color: rgba(68, 68, 68, 0.74);
  }
}
.comment-by-user {
  width: 100%;
  font-family: Montserrat;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.84);
  background-color: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  padding: 16px;
  margin: 0;
  margin-top: 8px;
}
.created-note-by-user {
  background-color: rgba(251, 243, 173, 0.74);
}
.notes-created-by-user {
  display: inline-block;
  width: 95%;
  padding-bottom: 16px;
  margin-left: 24px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24);
  p{
    margin-bottom: 0;
  }
}
.highlight-by-user {
  background-color: rgba(33, 150, 83, 0.24);
  font-family: inherit;
}
.app_in_app {
  .notes-creation-header {
    z-index: 99 !important;
  }
}

.notes-creation-header{
  border-bottom: 1px solid fade(@ws-darkBlack,20%);
  background: @ws-white;
  position: sticky;
  position: -webkit-sticky;
  top:0;
  padding: 1rem 0;
  p{
    margin-bottom: 0;
  }
}
.notes-list-wrapper{
  padding: 0;
  .export-btn-wrapper{
    display: flex;
    position: fixed;
    bottom: 0;
    width: 400px;
    text-align: center;
    background: @ws-white;
    justify-content: center;
    a{
      color:@ws-white;
      display: block;
      padding: 0.5rem;
      background: @ws-gradient-start;
      border-radius: 4px;
      width: 200px;
      &:hover{
        text-decoration: none;
      }
      &:focus{
        background:@ws-gradient-start;
      }
    }
  }
  .notes-list-item{
    display: flex;
    align-items: center;
    padding: 1rem;
  }
}
.score-btn{
  color:fade(@ws-darkBlack,70%);
  &:hover{
    color:@ws-lightOrange;
  }
}
.quiz-practice-btn{
  margin-bottom: 0 !important;
}

.bgScore{
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  color: @ws-white;
  .exercise-scores{
    border: 1px solid;
    border-radius: 4px;
    min-height: 75px;
    min-width: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

  .quiz-modal-body{
    //overflow-x: scroll;
    overflow-y: scroll;
  }
#analyticsid{
  .bt-quiz{
    border-bottom: 1px solid;
    margin-bottom: 2rem;
    align-items: center;
  }
}
.total-question-num{
  margin-bottom: 0;
}
#htmlContent{
  img{
    max-width: 100%;
  }
  table{
    width: 100% !important;
    max-width: 100%;
  }
}
.textEffects{
  position: absolute;
  right: 5rem;
  background: #fff;
  width: 32px;
  height: 42px;
  text-align: center;
  border-left: 1px solid #ededed;
  @media @iPhone{
    right:50px;
    width: 38px;
    height: 40px;
  }
  @media @iPhone6-portrait{
    right:50px;
    top:8px;
  }
  .dropdown-menu{
    position: absolute;
    top: 3rem;
    left: -10rem;
  }
}
.color-modes-wrapper {
  .color-mode-list-item {
    width: 65px;
    height: 62px;
    &.white {
      background: #F2F2F2;
    }
    &.sepia {
      background: #D4C79F;
    }
    &.grey {
      background: #5A5A5C;
    }
    &.black {
      background: #000000;
    }
    &.active{
      border-bottom: 2px solid @ws-darkOrange;
    }
  }
}
.quiz-item-wrapper {
  .quiz-buttons {
    //display: flex;
    //align-items: center;
  }
}
.study-set-wrapper-continer {
  margin-top: 40px;
  margin-bottom: 40px;
  counter-reset: study-set-counter; }
.study-set-main {
  background-color: #f8f8f8;
  padding: 0;
  margin-bottom: 32px;
  counter-increment: study-set-counter;
  position: relative;
  display: flex;
  &:hover .term-counter:before {
    content:url("../../images/landingpageImages/delete.svg");
    color: #F05A2A;
    opacity: 1;
    position: absolute;
    text-align: center;
    bottom: auto;
    left: 2rem;
    right: auto;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.2;
    cursor: pointer;
    z-index: 2;display: flex;
    align-items: center;
    justify-content: center;
    @media @iPhone{
      left: 0;
    }
  }
}
//.study-set-main:first-child:hover .term-counter:before {
//  color: #000;
//  font-family: "Montserrat", sans-serif;
//  content: counter(study-set-counter); }
.term-counter:before {
  position: absolute;
  content: counter(study-set-counter);
  text-align: center;
  bottom: auto;
  left: 2rem;
  right: auto;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.2;
  width: 3.375rem;
  opacity: 0.5;
  cursor: pointer;
  z-index: 2;display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
@media @iPhone{
  left:0;
}
}
.question-row-bordered-right{
  border-bottom: 2px solid @ws-gradient-start;
}
.question-row-bordered-wrong{
  border-bottom: 2px solid #B72319;
}
.answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers{
  width: 100px;
  height: 100px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
  padding: 34px 35px 36px 34px;
  margin: 0 auto;
  margin-top: 8px;
  border-radius: 16px;
}
.answer-summary .score-summary .wrong-answers {
  background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%);
}
.answer-summary .score-summary .skipped-answers {
  color: #444444;
  background: #fff;
  border: 2px solid #444444;
}
.score-container {
  //min-height: 525px;
  //max-height: 525px;
  overflow:auto;
  @media @iPhone{
    min-height: 100%;
    max-height: 100%;
    height: 400px;
  }
}
.suggestions-for-user{
  padding: 1rem;
}
.row-heading{
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}
.clearfix{
  display: block;
}
.mcq-question-div {
  margin: 0 auto;
  @media @iPhone{}
}
.skipped-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #000;
  background-color: #fff;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #000;
  border-radius: 8px;
}
.grey-bg {
  background: #5A5A5C;
}
.sepia-bg {
  background: #D4C79F;
}
.black-bg {
  background: #000000;
}
.black-bg,.grey-bg{
  a,p,h1,h2,h3,h4,h5,h6,span,i{
    color: #FFFFFF !important;
  }
  .notes-creation-header {
    p.notes-creation-header-title {
      color: rgba(68, 68, 68, 0.72) !important;
    }
    a.export-study-set {
      color: @ws-lightOrange !important;
    }
  }
  .export-notes {
    p {
      color: #212529 !important;
    }
    .close-notes i {
      color: #212529 !important;
    }
  }
  .pr-back-btn {
    color: #444444 !important;
    i {
      color: #444444 !important;
    }
    @media @iPhone {
      color: #ffffff !important;
      i {
        color: #ffffff !important;
      }
    }
  }
}
#formatMenu{
  a{
    cursor: pointer;
  }
}

#htmlreadingcontent {
  margin-top: 2rem;
  table {
    @media @iPhone {
      width:100% !important;
    }
  }
}

.video-url .modal-body form, .web-url .modal-body form, .upload-url .modal-body form{
  margin-top: 2rem;
}
.video-url .modal-body input:first-child, .web-url .modal-body input:first-child, .upload-url .modal-body input:first-child {
  margin-bottom: 2rem;
}
.video-url .modal-body input, .web-url .modal-body input, .upload-url .modal-body input {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.72);
  width: 280px;
  outline: 0;
  padding: 10px;
}
.upload-url .modal-body input {
  margin: 0 auto;
  border-radius: 0;
}
.video-url .modal-footer button:last-child, .web-url .modal-footer button:last-child, .upload-url .modal-footer button:last-child {
  color: #F79420;
}
.video-url .modal-footer button, .web-url .modal-footer button, .upload-url .modal-footer button {
  border: none;
}
#prev-section,#next-section{
  display: none;
}
object {
  //width: 100% !important;
}
#jpedal{
  width: 100% !important;
}
#htmlreadingcontent{
  padding: 0 !important;
  #jpedal{
    overflow: unset !important;
    //overflow-x:auto!important;
  }
}
#htmlContent{
  margin-top: 0rem;
}
.hasScrolled{
  .bookTemplate{
    .content-wrapper{
      #book-sidebar{
        .side-content{
          >h2{
            margin-top: 0;
            transition: all 0.3s;
          }
          .backtolibrary{
            color:@ws-white;
            i{
              color:@ws-white;
            }
          }
        }
        .mobile-title{
          @media @iPhone,@iPhone6-landscape{
            margin-top: 0;
            transition: all 0.3s;
            background: @ws-lightOrange;
            p{
              color:@ws-white;
            }
          }
        }
      }
    }
    .export-notes{
      top:50px;
      transition: all 0.1s;
    }
  }

  .mobChapname {
    background: @ws-lightOrange;
    p{
      color:@ws-white;
    }
    #mobChapname {
      color:@ws-white;
      margin-bottom: 0;
    }
    i{
      color:@ws-white;
      font-size: 12px;
    }
    span{
      font-size: 12px;
      color:@ws-white;
      white-space: nowrap;
    }
  }
}

.all-container{
  #android-menu{
    &:after{
      display: none;
    }
    i{
      top: 9px;
      right: 5px;
      position: relative;
    }
  }
.container-wrapper{
  margin: 0 auto;
  border: 1px solid #ededed;
  border-radius: 4px;
  margin-top: 2rem;
  width:600px;
  min-height: 100px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  @media @iPhone{
    margin-top:0;
    border:none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
  .dropdown-menu{
    @media @iPhone{
      left: -4rem !important;
    }
  }
  .d-flex{
    p{
      margin: 0.5rem 1rem;
      color: rgba(68, 68, 68, 0.6);
      position: relative;
      top: 5px;
    }
  }
  @media @iPhone{
    width: 100%;
  }
  .media{
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    i {
      width: 50px;
      height: 50px;
      background: darkred;
      font-size: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 2rem;
      color: #fff;
      border-radius: 50px;
      @media @iPhone{
        margin:0 1rem;
      }
      &.yellow{
        background: #f1c40f;
      }
     &.blue{
       background: #2980b9;
     }
      &.green{
        background:#27ae60;
      }
&.violet{
  background: #9b59b6;
}
      &.yt {
        background: #e74c3c;
        &:before {
          margin-left: 0 !important;
        }
      }
    }
    .date{
      font-size: 12px;
      color:fade(@ws-darkBlack,40%);
      display: block;
      padding-bottom: 10px;
    }
    .title{
     font-weight: normal;
    }
    .readnow{
      text-transform: uppercase;
      color:@ws-lightOrange;
    }
  }
  .logo{
    width: 18px;
    height: 18px;
    z-index: 9 !important;
  }
}

  // Listen Now Styles
  a.listen-btn {
    padding-left: 10px;
    margin-left: 10px;
    border-left: 1px solid #000;
    //color: @ws-lightOrange;
    &:hover {
      //color: @ws-lightOrange;
    }
  }
  .adjusted-logo {
    margin-top: -18px;
  }
}

.mobChapname {
  color: rgba(68, 68, 68, 0.4);
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
  font-weight: normal;
  padding: 1rem;
  background: @ws-white;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  width: 100%;
  z-index: 99;
  border-bottom: 1px solid fade(@ws-darkBlack,10%);
  #mobChapname {
     margin-bottom: 0;
    color:@ws-darkBlack;
  }
  i{
    color:@ws-darkBlack;
    font-size: 12px;
  }
  span{
    font-size: 12px;
    color:@ws-darkBlack;
    white-space: nowrap;
  }

}
#study-set-wrapper-container{
  .pr-back-btn{
    z-index: 1;
  }
}
#all{
  //.container{
  //  padding: 0;
  //}
}
.bookShadow{
  height: 100%;
  border-radius: 0 3px 3px 0;
  box-shadow: inset 8px 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
  &::after{
    content: '';
    position: absolute;
    top: 0;
    left: 5px;
    bottom: 0;
    width: 2px;
    background: rgba(0, 0, 0, 0.1);
    box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3)
  }
  >img{
    border-radius: 0 3px 3px 0;
    box-shadow: inset 4px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
  }
}

#PlayAudiOnlyModal {
  #LoadingAudio {
    position:absolute;;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background-color:#FFF;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: .3rem;
    z-index: 10;
  }
  .LoadingImage {
    border: 7px solid #f3f3f3;
    border-radius: 50%;
    border-top: 7px solid @ws-lightOrange;
    width: 50px;
    height: 50px;
    margin: 15px auto;
    -webkit-animation: spin 1s linear infinite; /* Safari */
    animation: spin 1s linear infinite;
  }
}
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.prev_chap {
  transform: rotate(-180deg);
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
}

#all {
  h3{
    text-align: center;
    margin-bottom: 4rem;
    font-family: @ws-header-font;
    font-size: 16px;
  }
  .test-pr{
    width: 75%;
    margin: 2rem auto;
  }
  .overlay-pr {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background: rgba(68, 68, 68, 0.5);

    .play-btn-wrapper {
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
  .video-wrapper{
    display: flex;
    flex-wrap: wrap;
    .video-img-wrapper{
      position: relative;
    }
    a{
      &:hover {
        text-decoration: none;
      }
      span{
        color:@ws-darkBlack;
      }
    }
  }
  .play-btn-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    img {
      width: 26px;
      height: 25px;
    }

    span {
      margin-left: 0.5rem;
    }
  }

  .videoTOadd {
    display: flex;
    align-items: center;

    img {
      width: 24px;
      height: 16px;
    }
    color:@ws-darkBlack;
  }

  .pr-flex {
    display: flex;
    justify-content: space-between;
    .dpr-none {
      display: none;
      img{
        width: 24px;
        height: 16px;
        margin-right: 0.3rem;
      }
    }
  }
}
