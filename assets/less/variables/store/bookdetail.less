@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

.book-details{
  margin-top: 2rem;
  padding-bottom: 4rem;
  .test-wrapper{
    &:hover{
      box-shadow: none;
      background: none;
    }
  }
  .breadcrumb{
    background: none;
    li{
      font-size: @ws-menu-fontSize - 2;
      color:fade(@ws-darkBlack,40%);
      padding: 0 2px;
      font-family: @ws-header-font;

      &.active{
        color:@ws-darkBlack;
        font-weight: @ws-header-fontWeight;
      }
      a{
        color:fade(@ws-darkBlack,40%);
        font-size: @ws-menu-fontSize - 2;
        font-family: @ws-header-font;
      }
    }
  }
  .topSchoolBooks {

    >a{
      &:hover{
        text-decoration: none;
      }
    }

    .image-wrapper {
      margin: 0 auto;
      >a{
        &:hover{
          text-decoration: none;
        }
      }
      width: 132px;
      height: 165px;
      position: relative;
      z-index: 99;
      img {
        width: 132px;
        height: 165px;
        position: relative;
        border-radius: 4px;
        box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      }
      h3 {
        position: absolute;
        font-size: @ws-menu-fontSize - 4;
        font-weight: @ws-header-fontWeight;
        color: @ws-white;
        background-color: @ws-lightOrange;
        padding: 7px 14px;
        bottom: 32px;
        left: -6px;
        margin-bottom: 0;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        &:after {
          content: ' ';
          position: absolute;
          width: 0;
          height: 0;
          left: 0;
          top: 100%;
          border-width: 2px 3px;
          border-style: solid;
          border-color: @ws-darkOrange @ws-darkOrange transparent transparent;
        }
      }
    }

  }
}
.content-wrappers {
  h1 {
    font-size: @ws-header-fontSize + 4;
    font-family: @ws-header-font;
    color: @ws-darkBlack;
    font-weight: @ws-header-fontWeight;
    margin-bottom: 0;
  }
  .author-name {
    font-size: 14px;
    color: fade(@ws-darkBlack, 60%);
    span {
      font-family: @ws-header-font;
      font-weight: @ws-header-fontWeight;
      color: @ws-darkBlack;
    }
  }
  .chapt-bk {
    font-family: @ws-header-font;
    color: fade(@ws-darkBlack, 72%);
    text-transform: uppercase;
    font-size: 10px;
  }
  .offer-price, .original-price {
    color: @ws-red;
    font-family: @ws-banner-font;
    font-weight: @ws-header-fontWeight;
  }
  div {
    &.d-flex {
      > div {
        margin-right: 2rem;
      }
    }
  }
  @media screen and (max-width: 767px) {
    h1.preview-book-name {
      margin-top: 20px;
    }
  }
}
.preview-book-btns{
  margin-left: 1rem;
  .btn-book-preview{
    border:1px solid #ededed;
    color:@ws-lightOrange !important;
    border-radius: 4px;
    text-transform: uppercase;
    width: 160px;
    height: 38px;
    font-size: 14px;
    margin: 1rem auto;
  }
  .btn-book-buy{
    background: @ws-blue-start;
    width: 160px;
    height: 40px;
    color:@ws-white !important;
    font-size: 14px;
    margin: 1rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &:focus {
      background: @ws-blue-start !important;
    }
  }
  #buyNow {
    background: @ws-lightOrange;
    &:focus {
      background: @ws-lightOrange !important;
    }
  }

}
.preview-book-desc{
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
  background: @ws-white;
  padding: 2rem 0;
  border-radius: 4px;
  margin-top: -2rem;
  @media screen and (max-width: 767px) {
    margin-top: 0;
  }
}
.showrank-modal{
  .modal-header{
    justify-content: center;
    button{
      padding: 0;
      margin: 0;
      position: absolute;
      right: 25px;
      top: 25px;
    }
  }
  .modal-footer{
    border: none;
  }
  .modal-body{
    padding: 0;
    .table thead th{
      font-size: 12px;
      font-weight:@ws-header-fontWeight;
      color:fade(@ws-darkBlack,70%);
    }
    tr{
      &.user-active{
        td{
          color:@ws-lightOrange !important;
        }
      }
      td{
        &:first-child{
          font-size: 18px;
          font-weight:@ws-header-fontWeight;
          color:fade(@ws-darkBlack,70%);
          font-family: @ws-banner-font;
        }
        &:nth-child(2){
          font-size: 14px;
          font-weight:normal;
          color:@ws-darkBlack;
          font-family: @ws-header-font;
        }
        &:last-child{
          font-size: 14px;
          font-weight:bold;
          color:@ws-darkBlack;
          font-family: @ws-banner-font;
        }
      }
    }
  }
  .content-rankWrapper{
    background: #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    margin-top: 1rem;
    .profile{
      width:92px;
      height:92px;
      border-radius: 50%;
      border: 4px solid #FFFFFF;
      box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
      img{
        width: 84px;
        height: 84px;
        border-radius: 50%;
      }
    }
    .user-rank{
      text-align: center;
      font-weight: @ws-header-fontWeight;
      font-family: @ws-banner-font;
      font-size: 34px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      &:before{
        content: url('../../../images/landingpageImages/badge.svg');
          width: 34px;
      }
    }
    .yr-head{
      font-size: 8px;
      font-weight: @ws-header-fontWeight;
      font-family: @ws-header-font;
      color:fade(@ws-darkBlack,40%);
      text-transform: uppercase;
      text-align: center;
      margin: 0;
      margin-top: 8px;
    }
    .no-ques{
      font-size: @ws-menu-fontSize;
      font-weight: @ws-header-fontWeight;
      font-family: @ws-header-font;
      color:@ws-darkBlack;
      text-align: center;

    }
    .rank-head{
      font-size: 12px;
      font-weight: @ws-header-fontWeight;
      font-family: @ws-header-font;
      color:fade(@ws-darkBlack,40%);
      text-transform: uppercase;
      text-align: center;
      margin: 0;
      margin-top: 8px;
    }
    .total-students{
      font-size: 10px;
      font-weight: normal;
      font-family: @ws-header-font;
      color:fade(@ws-darkBlack,40%);
      text-transform: uppercase;
      text-align: center;
    }

  }
}
