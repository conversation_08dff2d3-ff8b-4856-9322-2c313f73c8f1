@import "../responsive.less";
.test-gen-box-main {
  position: relative;
  //max-width: 352px;
  margin: 0 auto;
  z-index: 2;
  //margin-left: 358px;

}
.test-gen-modal-body {
  .test-background-image {
    background: url('../../images/landingpageImages/testgen-bg.png') no-repeat center;
    width: 100%;
    position: absolute;
    top: 0;
    height: 100%;
    opacity: 0.1;
    background-size: contain;
  }
}
.test-gen-main {
  position: relative;
  width: 100%;
  margin-top:30px;
  margin-bottom:30px;
  z-index: 2;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  .test-background-image{
    background: url('../../images/landingpageImages/testgen-bg.png') no-repeat center;
    width: 100%;
    position: absolute;
    top: 0;
    height: 100%;
    opacity: 0.1;
    background-size: contain;
  }
}
.test-gen-box-main .test-gen-box {
  //height: 200px;
  background-color: #fff;
  padding: 24px 24px;
  width: 352px;
  margin-bottom: 30px;
  //margin-left: 358px;
  margin-top: 30px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  //margin-right: -46px;
  p{
    font-family: 'Rubik', sans-serif;
  }
  @media @iPhone{
    width:260px;
    margin: 1rem auto;
  }
}
.test-gen-box-main .test-gen-box .btn-info {
  //float: right;
  margin: 0 auto;
  display: block;
  width:80px;
  &:focus {
    background-color: #138496 !important;
  }
}
#error_message {
  max-width: 500px;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 18px;
  text-align: left;
  color: #DF4B41;
  padding: 0 8px;
  &i{
   line-height: 25px;
  }
  .modal-footer{
    .material-icons{}
    font-size: 14px;
  }
}
#questionumber-containter .question-number-help-wrapper .dropdown-toggle::after {
  display: none;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid;
  border-right: .3em solid transparent;
  border-bottom: 0;
  border-left: .3em solid transparent;
}

.modal-body{
  .containers{
      font-weight: normal;

  }
}
.test-gen-modal{
  .modal-footer .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 15px;
    line-height: 2;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
  }
}
#mcqquestionsection{
  .next-btn {
    height: auto;
    font-size: 14px;
    color: #fff;
    background: #028EDB;
    padding: 8px;
    border: 0;
  }
  .submit-quiz-btn {
    background: #FFFFFF;
    border: 1.5px solid rgba(68, 68, 68, 0.2);
    box-sizing: border-box;
    border-radius: 2px;
    color: #028EDB;
    margin: 0 auto;
    float: none;
  }
}
.eutkarsh {
  #mcqquestionsection {
    .next-btn {
      height: auto;
      font-size: 14px;
      color: #fff;
      background:@ws-darkOrange;
      padding: 8px;
      border: 0;
    }

    .submit-quiz-btn {
      background: #FFFFFF;
      border: 1.5px solid rgba(68, 68, 68, 0.2);
      box-sizing: border-box;
      border-radius: 2px;
      color: @ws-darkOrange;
      margin: 0 auto;
      float: none;
    }
  }
  #content-data-studyset .add-notesPr > a {
    height: 44px;
    background: @ws-darkOrange;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 600px;
    color: #ffffff;
    width: 160px;
    border: none;
    display: flex;
    align-items: center;
    float: right;
    justify-content: center;
  }
}