@import "ws_color_theme.less";
@import "ws_fonts.less";


article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

html{
  background:@ws-white;
  font-family: @ws-header-font;
  color:@ws-white !important;
}
::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: fade(@ws-darkBlack,30%);
  opacity: 1; /* Firefox */
  font-family: @ws-header-font;
  font-size: @ws-menu-fontSize;
}

:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: fade(@ws-darkBlack,30%);
  font-family: @ws-header-font;
  font-size: @ws-menu-fontSize;
}

::-ms-input-placeholder { /* Microsoft Edge */
  color:fade(@ws-darkBlack,30%);
  font-family: @ws-header-font;
  font-size: @ws-menu-fontSize;
}

.owl-dots,.owl-prev{
  display: none;
}
.owl-next{
  background-color:@ws-white !important;
  width: 40px;
  height: 80px;
  right: -1px;
  top: 25%;
  border-radius: 4px 0 0 4px;
  position: absolute;
  -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  outline: 0;
  &:focus{
    outline:0;
  }
  span{
    color:fade(@ws-darkBlack,70%);
    font-size: 32px;
  }
}

body {

}
.modal{
  z-index: 999;
}
.modal-open {
  overflow: auto;
}
input[type='text']{
  border:1px solid @ws-border;
  border-radius: 4px;
  font-family: @ws-header-font;
}
.material-icons-new {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-white {
  webkit-filter: contrast(4) invert(1);
  -moz-filter: contrast(4) invert(1);
  -o-filter: contrast(4) invert(1);
  -ms-filter: contrast(4) invert(1);
  filter: contrast(4) invert(1);
}
.custom-select-ws {
  position: relative;
  font-family: Arial;
}
.custom-select-ws select {
  display: none; /*hide original SELECT element:*/
}


.select-selected {
  background-color: @ws-white;
  color:@ws-darkBlack;
  padding: 8px 2px;
  border: none;
  border-bottom: 1px solid fade(@ws-caret,12%);
  cursor: pointer;
  user-select: none;
  font-family: @ws-header-font;
  font-size: @ws-menu-fontSize;
  font-weight: @ws-header-fontWeight;
  &:after{
    position: absolute;
    content: "";
    top: 16px;
    right: 10px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: fade(@ws-caret,50%) transparent transparent transparent;
  }
  &.select-arrow-active:after{
    border-color:transparent transparent fade(@ws-caret,50%) transparent;
    top: 10px;
  }
}

.select-items{
  position: absolute;
  background-color: @ws-white;
  top: 125%;
  left: 0;
  right: 0;
  z-index: 99;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  div{
    color:@ws-darkBlack;
    padding: 8px 16px;
    //border: 1px solid transparent;
    //border-color: transparent transparent rgba(0, 0, 0, 0.1) transparent;
    cursor: pointer;
    user-select: none;
    &:first-child{
      border-top-right-radius: 6px;
      border-top-left-radius: 6px;
    }
    &:last-child{
      border-bottom-right-radius: 6px;
      border-bottom-left-radius: 6px;
    }
    &:hover{
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}
/*hide the items when the select box is closed:*/
.select-hide {
  display: none;
}
.same-as-selected {
  background-color: transparent;
}
.btn{
  font-family: @ws-header-font;
  font-weight: @ws-header-fontWeight;
  font-size: @ws-menu-fontSize;
}
.btn:focus,.btn:active {
  outline: none !important;
  box-shadow: none;
}
.mobile-bottom-menu-wrapper{
  display: none;
}
p,a,button{
  font-family: @ws-header-font;
  font-size: @ws-menu-fontSize;
}
.email-error{
  p{
    max-width: 100%;
  }
}
.modal-open{
  padding-right: 0 !important;
}
.nav-link{
  color:@ws-darkBlack;
  font-family: @ws-header-font;
}
.owl-item{
  a{
    &:hover{
      text-decoration: none;
    }
  }
}
#drift-widget-container {
  iframe {
    @media @iPhone {
      bottom: 4rem !important;
    }
  }
}
.hidden{
  display: none;
}

.loader {
  margin: 5% auto 30px;
  &.book {
    border: 4px solid @ws-darkBlue;
    width: 60px;
    height: 45px;
    position: relative;
    perspective: 150px;
    background: @ws-lightOrange;
    .page {
      display: block;
      width: 30px;
      height: 45px;
      border: 4px solid @ws-darkBlue;
      border-left: 1px solid @ws-darkOrange;
      margin: 0;
      position: absolute;
      right: -4px;
      top: -4px;
      overflow: hidden;
      background: @ws-darkOrange;
      transform-style: preserve-3d;
      -webkit-transform-origin: left center;
      transform-origin: left center;
    }
  }
}





.book .page:nth-child(1) {
  -webkit-animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.6s infinite;
  animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.6s infinite;
}

.book .page:nth-child(2) {
  -webkit-animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.45s infinite;
  animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.45s infinite;
}

.book .page:nth-child(3) {
  -webkit-animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.2s infinite;
  animation: pageTurn 1.2s cubic-bezier(0, .39, 1, .68) 1.2s infinite;
}


/* Page turn */

@-webkit-keyframes pageTurn {
  0% {
    -webkit-transform: rotateY( 0deg);
    transform: rotateY( 0deg);
  }
  20% {
    background: @ws-lightOrange;
  }
  40% {
    background: @ws-lightOrange;
    -webkit-transform: rotateY( -180deg);
    transform: rotateY( -180deg);
  }
  100% {
    background: @ws-lightOrange;
    -webkit-transform: rotateY( -180deg);
    transform: rotateY( -180deg);
  }
}

@keyframes pageTurn {
  0% {
    transform: rotateY( 0deg);
  }
  20% {
    background: @ws-lightOrange;
  }
  40% {
    background: @ws-lightOrange;
    transform: rotateY( -180deg);
  }
  100% {
    background: @ws-lightOrange;
    transform: rotateY( -180deg);
  }
}


/* Dots */

@-webkit-keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}

@keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}
.loading-icon{
  position: fixed;
  width: 100%;
  height: 100%;
  top:0;
  background: fade(@ws-darkBlack,90%);
  z-index: 9999;
  overflow: hidden;
}
.load-wrapper{
  position: absolute;
  top: 35%;
  width: 100%;
}
#tab-head{
  white-space:nowrap;
}
a:focus{
  //background: transparent !important;
}
.slick-prev{
  background: #ffffff;
  width: 40px;
  height: 80px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  left: -41px;
}
.slick-initialized .slick-slide{
  a{
    &:hover{
      text-decoration: none;
    }
  }
}
.slick-next{
  background: @ws-white;
  width:40px;
  height:80px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
  right:-12px;
  @media @iPhone{
    right:0;
  }
  &:before{
    color:fade(@ws-darkBlack,70%);
    font-family: @ws-header-font;
    content: url("../../images/landingpageImages/right-arrow.svg");
    width:7px;
    height:12px;
  }
  &:hover,:focus{
    opacity: 1;
    background: @ws-white;
  }
}
.slick-prev{
  @media @iPhone{
    left: 0;
    z-index: 9;
  }
  &:before{
    color:fade(@ws-darkBlack,70%);
    font-family: @ws-header-font;
    content: url("../../images/landingpageImages/left-arrow.svg");
    width:7px;
    height:12px;
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
  }
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}

.input-error-tooltip-inner,.email-error-text{
  color:@ws-red !important;
  font-size: 12px !important;
  max-width: 100% !important;
  padding: 0;
  margin: 0;
}
#divi3{
  display:none;
}
.utkarsh{
  .logo{
    width: 90px;
    height: 60px;
  }
  }
@media (min-width: 576px) {
  .filter.d-sm-blocks {
    display: block;
  }

  .filter.d-nones {
    display: none ;
  }
}
//.loading-icon {
//  width: 100%;
//  height: 100%;
//  background-color: rgba(68, 68, 68, 0.64);
//  position: fixed;
//  top: 0;
//  right: 0;
//  bottom: 0;
//  left: 0;
//  z-index: 9999; }
//.loader-wrapper {
//  width: 139px;
//  height: 65px;
//  background-color: #FFFFFF;
//  position: relative;
//  top: 50% !important;
//  -webkit-transform: translateY(-50%);
//  -moz-transform: translateY(-50%);
//  -ms-transform: translateY(-50%);
//  -o-transform: translateY(-50%);
//  transform: translateY(-50%);
//  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
//  margin: 0 auto;
//  border-radius: 4px; }
//
//.loader,
//.loader:before,
//.loader:after {
//  border-radius: 50%;
//  width: 2.5em;
//  height: 2.5em;
//  -webkit-animation-fill-mode: both;
//  animation-fill-mode: both;
//  -webkit-animation: load7 1.8s infinite ease-in-out;
//  animation: load7 1.8s infinite ease-in-out; }
//
//.loader {
//  color: #F05A2A;
//  font-size: 9px;
//  margin: 0 auto;
//  position: relative;
//  text-indent: -9999em;
//  -webkit-transform: translateZ(0);
//  -ms-transform: translateZ(0);
//  transform: translateZ(0);
//  -webkit-animation-delay: -0.16s;
//  animation-delay: -0.16s; }
//
//.loader:before,
//.loader:after {
//  content: '';
//  position: absolute;
//  top: 0; }
//
//.loader:before {
//  left: -3.5em;
//  -webkit-animation-delay: -0.32s;
//  animation-delay: -0.32s; }
//
//.loader:after {
//  left: 3.5em; }
//
//@-webkit-keyframes load7 {
//  0%,
//  80%,
//  100% {
//    box-shadow: 0 2.5em 0 -1.3em; }
//  40% {
//    box-shadow: 0 2.5em 0 0; } }
//@keyframes load7 {
//  0%,
//  80%,
//  100% {
//    box-shadow: 0 2.5em 0 -1.3em; }
//  40% {
//    box-shadow: 0 2.5em 0 0; } }

#book-read-material{
  .tab-content>.tab-pane{
    min-height: calc(100vh - 150px);
  }
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px; }

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out; }

.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s; }

.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0; }

.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s; }

.loader:after {
  left: 3.5em; }

@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }

body .videoPlays{
  display: none;
}
