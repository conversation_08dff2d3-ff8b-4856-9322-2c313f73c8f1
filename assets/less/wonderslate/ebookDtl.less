@import "../variables/responsive.less";
@import "color.less";
@import "common.less";

@mobile-screen: ~"only screen and (max-width: 767px)";
@tab-screen: ~"only screen and (min-width: 768px) and (max-width: 991px)";
@laptop-screen: ~"only screen and (min-width: 992px) and (max-width: 1199px)";

.ebook_detail {
  .container-fluid {
    width: 90%;
    @media only screen and (max-width: 767px) {
      width: 100%;
    }
  }
  .image_wrapper {
    .book_image {
      width: 165px;
      height: 225px;
      position: relative;
      z-index: 10;
      margin: 0 auto;
      .bookShadow {
        img {
          width: 100%;
          height: 225px;
          border-radius: 3px 0 0 3px;
        }
        &::after {
          width: 3px;
        }
        .uncoverdetail {
          height: 225px;
          padding: 10px;
        }
        .book_price {
          position: absolute;
          z-index: 10;
          color: #ffffff;
          background-color: #27AE60;
          padding: 5px 15px;
          bottom: 32px;
          left: -6px;
          text-align: left;
          line-height: normal;
          margin-bottom: 0;
          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
          -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          .offer_price {
            font-family: @theme_oldfont !important;
            font-size: 14px;
            display: block;
            font-weight: 500;
            //margin-bottom: -7px;
          }
          .list_price {
            font-family: @theme_oldfont !important;
            font-size: 11px;
          }
          &::after {
            content: ' ';
            position: absolute;
            width: 0;
            height: 0;
            left: 0;
            top: 100%;
            border-width: 2px 3px;
            border-style: solid;
            border-color: @price_bg @price_bg transparent transparent;
          }
        }
      }
    }
    .book_image_bottom {
      margin-top: -12px;
      position: relative;
      z-index: 11;
      img {
        width: 100%;
      }
    }
    .book_buttons {
      @media @mobile-screen {
        //position: fixed;
        //bottom: 5px;
        //width: 100%;
        //z-index: 999;
        //left: 0;
        //right: 0;
      }
      .col {
        padding: 0 5px;
        &:first-child {
          a {
            border-color: @outline-btn;
            color: @outline-btn;
            background-color: @white;
          }
        }
        &:last-child {
          a {
            border-color: @outline-btn;
            background: @theme-gradient;
            color: @white;
          }
        }
        a {
          width: 100%;
          height: 50px;
          box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
          font-family: @theme_font;
          line-height: 2.5;
          &:hover {
            box-shadow: 0 0.2rem 1rem rgba(0,0,0,.15) !important;
          }
        }
      }
    }
  }
  .book_info {
    .book_description {
      h4 {
        font-weight: bold;
        font-size: 22px;
      }
      .book-desc {
          color: @text-color;
          font-size: 14px;
          font-weight: 300;
        p {
          //font-size: 14px;
          //font-weight: 300;
        }
        a.exp {
          font-weight: normal;
          font-style: normal;
          font-size: 15px;
        }
      }
    }
    .chapter_lists {
      .dropdown {
        border-radius: 5px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
      }
      button {
        height: 40px;
        /*border-color: @outline-btn;
        color: @outline-btn;*/
        background-color: @white;
        span {
          font-weight: bold;
          display: inline-block;
          width: 95%;
          img {
            padding-right: 10px;
          }
        }
        &:after {
          color: @theme;
          vertical-align: 0.15em;
        }
      }
      .dropdown-menu {
        width: 100%;
        padding: 0;
        transform: translate3d(0px, 40px, 0px) !important;
        max-height: 330px;
        overflow-y: scroll;
        z-index: 99;
        span {
          white-space: pre-wrap;
          color: @theme;
          padding-top: 10px;
          padding-bottom: 10px;
          &:first-child {
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
          }
          &:last-child {
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
          }
          &:hover {
            //color: @theme;
            //background-color: transparent;
          }
          &:active {
            background-color: transparent;
          }
        }
      }
    }
    .book_contains {
      .card {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        border: none;
        height: 100%;
        p {
          font-size: 12px;
          color: @text-color;
          img {
            padding-right: 5px;
          }
        }
      }
    }
    .book_adding {
      .card {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        border: none;
        height: 100%;
        p {
          font-size: 12px;
          color: @text-color;
          img {
            padding-right: 5px;
          }
        }
      }
    }
  }

}
.popular_searches {
  height: fit-content;
  h4 {
    color: @purple;
    font-size: 20px;
  }
  .bg-light {
    background: #F7F7F7;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.14);
    .popular_search_lists {
      //overflow-x: auto;
      .slick-track {
        margin-left: 0;
        margin-right: 0;
      }
      .slick-prev {
        left: -41px;
        &:hover {
          background-color: #ffffff;
        }
        &:active {
          background-color: #ffffff;
        }
        &:focus {
          background-color: #ffffff;
        }
        @media screen and (max-width: 1024px) {
          left: 0;
          z-index: 9;
        }
      }
      .slick-next {
        right: -41px;
        &:hover {
          background-color: #ffffff;
        }
        &:active {
          background-color: #ffffff;
        }
        &:focus {
          background-color: #ffffff;
        }
        @media screen and (max-width: 1024px) {
          right: 0;
          z-index: 9;
        }
      }
      .slick-arrow {
        height: 70px;
      }
      .img-wrapper {
        padding: 15px;
        width: 155px;
        margin: 0 auto;
        border-radius: 5px;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
        .bookShadow {
          margin-bottom: 10px;
          //height: 175px;
          img {
            width: 100%;
            height: 165px;
          }
        }
      }
      a:hover {
        .img-wrapper {
          //box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
          .uncover p {
            text-decoration: none;
          }
        }
        .content-wrapper {
          p {
            text-decoration: underline;
          }
        }
      }
      .content-wrapper {
        p {
          font-size: 12px;
          color: @text-darkcolor;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
      .uncover {
        padding: 5px;
        //height: 175px;
        p {
          font-size: 12px;
        }
      }
    }
  }
}

.book-publisher-name{
  text-transform: uppercase;
  color: #ABABAB !important;
}
@media only screen and (max-width: 767px) {
  .shapemobile {
    display: none;
    &.backMenu {
      display: flex !important;
      i {
        //left: 30px !important;
      }
    }
  }
}
