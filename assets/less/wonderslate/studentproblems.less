@import "../variables/responsive.less";
@import "color.less";
@import "common.less";

@theme_oldfont: '<PERSON><PERSON><PERSON>', sans-serif;
@theme_font: 'Poppins', sans-serif;

.student-problems{
  .features {
    .row {
      padding-left: 15px;
      .card {
        border-radius: 10px;
        height: 152px;
        max-width: 200px;
        color: #fff;
        border: none;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);

        @media @extraSmall, @small {
          width: 100%;
          max-width: 100%;
        }

        &:nth-child(1) {
          background: radial-gradient(142.42% 142.42% at 8.23% 0%, #D585F6 0%, #8031BA 61.17%) !important;
        }

        &:nth-child(2) {
          background: radial-gradient(106.28% 106.45% at 0% 0%, #3ED8CF 0%, #068B82 99.48%) !important;
        }

        &:nth-child(3) {
          background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%) !important;
        }

        &:nth-child(4) {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, #557300 66.95%) !important;
        }

        &:nth-child(5) {
          background: radial-gradient(67.7% 68.99% at 24.68% 24.68%, #F24CE1 0%, #A90098 91.9%) !important;
        }

        &:nth-child(6) {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #66C3FD 5.08%, #297AD6 66.95%) !important;
        }

        &:nth-child(7) {
          background: radial-gradient(106.28% 106.45% at 0% 0%, #3ED8CF 0%, #068B82 99.48%) !important;
        }

        &:nth-child(8) {
          background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%) !important;
        }

        &:nth-child(9) {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, #557300 66.95%) !important;
        }

        &:nth-child(10) {
          background: radial-gradient(142.42% 142.42% at 8.23% 0%, #D585F6 0%, #8031BA 61.17%) !important;
        }



      }
    }
  }


}