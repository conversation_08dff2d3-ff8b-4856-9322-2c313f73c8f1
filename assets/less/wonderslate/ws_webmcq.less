@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

// MCQ Page Styles (Only Wonderslate)
body.guest-mode {
  #quizQuestionSection {
    .sub-header {
      top: 140px;
      @media @extraSmallDevices,@smallDevices {
        top: 120px;
      }
    }
    .mt-fixed {
      .que-side-menu {
        top: 230px;
      }
    }
    .result-menu {
      top: 130px !important;
      @media @extraSmallDevices,@smallDevices {
        top: 110px !important;
      }
    }
  }
  &.fixed-navbar {
    #quizQuestionSection {
      .sub-header {
        top: 55px;
        @media @extraSmallDevices,@smallDevices {
          top: 120px;
        }
      }
      .mt-fixed {
        .que-side-menu {
          top: 150px;
        }
      }
      .result-menu {
        top: 55px !important;
        @media @extraSmallDevices,@smallDevices {
          top: 110px !important;
        }
      }
    }
  }
}

#learnInformationSection {
  .btn-starts {
    font-weight: @semi-bold;
    color: @white;
    box-shadow: 0 2px 4px @gray-light-shadow;
  }
}

//
#quizInformationSection {
  .card {
    box-shadow: 0 2px 4px @gray-light-shadow;
  }
  .language {
    p {
      &:before {
        margin-right: 10px;
      }
    }
  }
  .btn-starts {
    font-weight: @semi-bold;
    color: @white;
    box-shadow: 0 2px 4px @gray-light-shadow;
  }
}

// Questions Section Styles
#quizQuestionSection {
  .overlay-container {
    position: fixed;
    background-color: @black;
    z-index: 9991;
  }
  .sub-header {
    top: 70px;
    transition: all 0.35s linear;
    box-shadow: 0 1px 7px lighten(@light-gray,30%);
    border-bottom: 1px solid @theme-primary-color;
    position: sticky;
    @media @extraSmallDevices,@smallDevices {
      top: 60px;
    }
    @media @mediumDevices {
      top: 55px;
    }
    .svg-timer {
      @media @extraSmallDevices,@smallDevices {
        padding: 0;
      }
      .tim-wrapper {
        .normal-time {
          position: relative;
          #pause {
            position: absolute;
            width: 28px;
            height: 28px;
            left: 0;
            border-radius: 50px;
            &::before {
              left: 5px;
              position: relative;
              right: 0;
              top: 0;
            }
            &::after {
              top: 9px;
              left: 10px;
              width: 8px;
            }
          }
        }
        .sectiontime-wrapper {
          margin-left: 10px;
          .timeLeft {
            color: @gray;
            font-weight: @medium;
            line-height: 1.2;
          }
          .timer {
            font-weight: @medium;
            font-size: 14px;
          }
        }
      }
    }
    .options {
      border-color: lighten(@light-gray,30%);
      @media @extraSmallDevices,@smallDevices {
        padding: 0;
      }
      p {
        span {
          &.totalquestion {
            padding-left: 5px;
          }
        }
      }
      #firstScreenMenuWrapper {

      }
    }
    .submitWrapper {
      .bg-wsTheme {
        background: @orange !important;
        color: @white !important;
        box-shadow: 0 2px 4px @gray-light-shadow;
        text-transform: uppercase;
        font-weight: normal;
        letter-spacing: .5px;
      }
    }
    .total-time-wrapper {
      display: flex;
      .total-test-time {
        width: 60px;
      }
    }
  }
  .result-menu {
    top: 69px !important;
    transition: all 0.35s linear;
    box-shadow: 0 1px 7px lighten(@light-gray,30%);
    border-bottom: 1px solid @theme-primary-color;
    @media @extraSmallDevices,@smallDevices {
      top: 50px !important;
    }
    @media @mediumDevices {
      top: 55px !important;
    }
    > div {
      height: 50px;
    }
    h2 {
      font-weight: @semi-bold;
    }
  }
  .mt-fixed {
    margin-top: 0;
    padding-top: 0;
    > .container {
      @media @largeDevices {
        max-width: 90%;
      }
    }
    .que-side-menu {
      position: sticky;
      top: 180px;
      height: 100%;
      margin-top: 30px;
      border-radius: 5px;
      border: none;
      box-shadow: 1px 1px 4px lighten(@light-gray,30%);
      transition: all 0.35s linear;
      @media @extraSmallDevices,@smallDevices,@mediumDevices,(min-width: 992px) and (max-width: 1024px) {
        position: fixed;
        top: 0 !important;
        margin-top: 0 !important;
        border-radius: 7px 0 0 7px;
        box-shadow: 0px -1px 10px @black;
        z-index: 9991;
        .close-menu {
          width: 24px;
          height: 24px;
          top: 10px;
          right: 10px;
          span {
            width: 10px;
            height: 10px;
          }
        }
      }
      .tab-wrappers {
        position: relative;
        width: 100%;
        top: 1rem;
        height: auto;
        @media @extraSmallDevices,@smallDevices,@mediumDevices,(min-width: 992px) and (max-width: 1024px) {
          top: 2rem;
        }
        .container {
          padding: 0;
        }
        .indicator {
          margin-top: 0;
          @media @extraSmallDevices,@smallDevices,@mediumDevices,(min-width: 992px) and (max-width: 1024px) {
            justify-content: start !important;
            .answered {
              padding-right: 50px;
            }
          }
          p {
            align-items: center;
          }
        }
        .tab {
          padding-bottom: 1rem;
        }
        .nav-tabs {
          justify-content: space-around;
          background-color: transparent;
          li {
            width: 48%;
            text-align: center;
            a {
              background-color: lighten(@light-gray,35%);
              transition: all 0.2s linear;
              color: @black;
              border: 0;
              border-bottom: 2px solid transparent;
              opacity: 0.5;
              padding: 5px;
              &:hover, &.active {
                opacity: 1;
              }
            }
          }
        }
        .tab-content {
          .grid {
            #collapseOne {
              padding-bottom: 20px;
              max-height: 400px;
              overflow-y: scroll;
              @media @extraSmallDevices,@smallDevices,@mediumDevices,(min-width: 992px) and (max-width: 1024px) {
                max-height: 100%;
                height: calc(80vh - 15px);
              }
            }
            .que-wrapper {
              justify-content: start;
              .que-no {
                margin: 0.3rem 0.75rem 0.75rem 0;
                @media @extraSmallDevices,@smallDevices,@mediumDevices {
                  margin: 0.3rem 0.5rem 0.5rem 0;
                }
                a {
                  width: 30px;
                  height: 30px;
                  display: flex !important;
                  justify-content: center;
                  align-items: center;
                  border-radius: 50%;
                }
                + .que-no {
                  a:first-child {
                    display: none !important;
                  }
                }
              }
            }
          }
          .list {
            .que-wrapper {
              max-height: 400px;
              overflow-y: scroll;
              @media @extraSmallDevices,@smallDevices,@mediumDevices,(min-width: 992px) and (max-width: 1024px) {
                max-height: 100%;
                height: calc(80vh - 15px);
                padding-bottom: 20px;
              }
              .onclickScrollsList {
                align-items: start !important;
                .align-self-start {
                  padding: 0;
                  margin: 0 !important;
                }
                &:last-child {
                  padding-bottom: 0;
                }
              }
              .que-no-list {
                margin-right: 0.75rem;
                margin-bottom: 0.25rem;
                cursor: pointer;
              }
              .question {
                width: auto;
                cursor: pointer;
                p {
                  width: auto;
                }
              }
            }
          }
        }
      }
    }
    #question-block {
      .question-wrapper {
        margin-top: 30px;
        border-radius: 5px;
        box-shadow: 1px 1px 7px lighten(@light-gray,25%);
        @media @extraSmallDevices,@smallDevices {
          margin-top: 20px;
        }
        p {
          color: @black;
        }
        .question {
          font-weight: @medium;
        }
        .border-start p {
          border-color: lighten(@light-gray,25%);
        }
        .options-string {
          label {
            border-color: lighten(@light-gray,35%);
            @media @extraSmallDevices,@smallDevices {
              min-height: 50px;
            }
          }
          &.active {
            border: none;
            label {
              border-color: #2EBAC6;
              border-width: 3px;
            }
            .answer {
              font-weight: @medium;
            }
          }
        }
      }
      .btn-starts {
        padding: 7px 25px;
        font-weight: @semi-bold;
        text-transform: uppercase;
        margin-top: 10px;
        color: @white;
        box-shadow: 0 2px 4px @gray-light-shadow;
      }
    }
    #answer-block {
      padding-top: 4rem;
      .practice-score-container {
        border-radius: 5px;
        justify-content: center;
        align-items: center;
      }
      .answer-summary {
        background-color: @white;
        border-radius: 5px;
        .score-summary {
          .correct-answers,.wrong-answers,.skipped-answers {
            font-family: @primary-font;
          }
        }
        .accuracy-summary {
          > div > div > span + p {
            color: @gray;
          }
        }
      }
      .analysis {
        box-shadow: 0 3px 4px @gray-light-shadow;
        padding: 1rem 0 0;
        border-radius: 7px;
        h2 {
          font-weight: @semi-bold;
        }
        #graphicAnalysis {
          margin-top: 10px;
          svg {
            border-radius: 5px;
            text {
              font-family: @primary-font;
            }
          }
        }
      }
      .suggestions-for-user {
        .suggested-topics {
          color: @dark-gray;
          padding: 0;
        }
      }
    }
    #scrolltoTab {
      margin-top: 2rem;
      .nav-tabs {
        background: lighten(@light-gray,35%);
      }
    }
    .tab-content {
      .validateMsg {
        text-align: center;
        padding: 10px;
        font-family: @primary-font;
      }
    }
    .question-box {
      background-color: @white;
      border-radius: 5px;
      box-shadow: 1px 1px 7px lighten(@light-gray,25%);
      padding-bottom: 0.25rem;
      width: 98%;
      margin: 2rem auto 0;
      > .question {
        border-color: lighten(@light-gray,25%) !important;
      }
      .ans-neutral {
        border-color: lighten(@light-gray,35%) !important;
      }
      .your-answer {
        @media @extraSmallDevices,@smallDevices {
          min-height: 60px;
        }
        .wrong-answer-by-user {
          font-size: 14px;
        }
        .choice {
          line-height: 1.5;
        }
      }
    }
    .directions,.passage,.correct-answer-explanation {
      font-family: @primary-font;
      margin-bottom: 0;
      @media @extraSmallDevices,@smallDevices {
        padding: 0;
      }
    }
    .showVideobtn {
      outline: 0 !important;
    }

    // Modal styles inside this page only
    .modal-body {
      .checkmark {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        &::after {
          left: 6px;
          top: 2px;
        }
      }
    }
    .modal-footer {
      button {
        box-shadow: 0 2px 4px @gray-light-shadow;
        &.submit {
          background: @orange !important;
          color: @white !important;
        }
      }
    }

    // Modal Styles in MCQs Page
    #submit-test {
      .modal-header {
        h1 {
          font-weight: @semi-bold;
          margin: 0 !important;
        }
      }
      .modal-body {
        .circle {
          span {
            margin-top: 0.75rem !important;
            font-weight: @semi-bold;
          }
          p {
            font-size: 15px;
          }
        }
      }
    }
    #report-que {
      .modal-content {
        width: 100%;
        border: 1px solid rgba(0,0,0,.2);
        border-radius: 0.3rem;
      }
      .modal-header {
        padding: 1rem;
        h4 {
          font-size: 15px;
          color: @black;
          margin: 0;
          i {
            font-size: 20px;
          }
        }
        .close {
          font-weight: normal;
          position: absolute;
          top: 10px;
          right: 10px;
          outline: 0;
        }
      }
      .modal-body {
        background: #f8f8f8;
        .letusknow {
          border: none;
          width: 100%;
          height: auto;
          textarea {
            border: 1px solid rgba(68, 68, 68, 0.2);
            width: 100%;
            height: 100px;
            border-radius: 3px;
            padding: 7px;
            font-size: 14px;
            resize: unset;
            &:focus {
              border-color: @black;
            }
          }
        }
      }
      .modal-footer {
        padding: 1rem;
        button {
          background: @orange !important;
          color: @white !important;
          padding: 6px 20px;
        }
      }
    }
    #videoContent {
      .close {
        outline: 0;
      }
    }
  }
}

.fixed-navbar {
  #quizQuestionSection {
    .mt-fixed {
      .que-side-menu {
        top: 160px;
      }
    }
  }
}

footer .footer-fluid {
  @media @extraSmallDevices,@smallDevices {
    padding-bottom: 0;
  }
}