@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

//Sweet alert styles
.sweet-overlay {
  background-color: @loader-color !important;
}
.sweet-alert {
  h2 {
    background: @theme-primary-color;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-flex !important;
    justify-content: center;
    font-size: 26px !important;
    margin: 0 0 10px !important;
    color: transparent !important;
    @media @extraSmallDevices, @smallDevices {
      font-size: 24px !important;
    }
  }
  p {
    color: @black !important;
    font-size: 15px !important;
    @media @extraSmallDevices, @smallDevices {
      font-size: 14px !important;
    }
  }
  button {
    font-size: 15px !important;
    font-weight: @regular !important;
    @media @extraSmallDevices, @smallDevices {
      font-size: 14px !important;
      padding: 7px 20px !important;
      margin: 15px 5px 0 5px !important;
    }
    &.cancel {
      background-color: transparent !important;
      color: @secondary-btn;
      border: 1px solid @secondary-btn;
    }
  }
}