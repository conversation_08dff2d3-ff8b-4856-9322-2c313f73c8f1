@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

.homepage-new {
  .institutes-area {
    #instituteLists {
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      h6 {
        font-weight: @regular;
        @media @largeDevices, @extraLargeDevices {
          font-size: 1.15rem;
        }
      }
      .institutes-logo {
        opacity: 1;
      }
      .institute-list {
        padding: 0 1rem 0 0;
        -ms-flex: 0 0 49%;
        flex: 0 0 49%;
        max-width: 49%;
      }
      .flex-fill {
        &:nth-child(odd) {
          padding: 0 0.5rem 0 0;
        }
        &:nth-child(even) {
          padding: 0 0 0 0.5rem;
        }
      }
    }
  }
}