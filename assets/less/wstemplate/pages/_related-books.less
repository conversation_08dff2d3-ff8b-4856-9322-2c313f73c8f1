@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Related & Best Seller Books Styles
.popular_searches {
  .popular_search_lists {
    height: 250px;
    @media (max-width: 375px){
      margin-left: 4% !important;
    }
    @media (min-device-width: 376px) and (max-device-width: 768px) {
      margin-left: 8% !important;
    }
  }
  .bg-light {
    box-shadow: 0 2px 10px @gray-light-shadow;
  }
  .slick-track {
    width: 1900px !important;
    margin-left: 0;
    margin-right: 0;
    .slick-slide {
      width: 190px !important;
    }
  }
  .img-wrapper {
    padding: 15px;
    width: 155px;
    margin: 0 auto;
    border-radius: 5px;
    transition: all 0.2s linear;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    img {
      width: 100%;
      height: 165px;
    }
  }
  .bookShadow {
    margin-bottom: 10px;
  }
  a {
    &:hover {
      .uncover p {
        text-decoration: none;
      }
      .content-wrapper p {
        text-decoration: underline;
      }
    }
  }
  .content-wrapper {
    p {
      font-size: 12px;
      color: @dark-gray;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      line-height: normal;
    }
  }
  .uncover {
      height: 165px;
    display: flex;
    align-items: center;
    justify-content: center;
    p {
      font-size: 12px;
    }
  }
  #show-more, #bestSeller-show-more {
    font-size: 13px;
    &:hover {
      text-decoration: underline;
    }
  }
}

body.whitelabel_ws .popular_searches .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  height: fit-content ;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}