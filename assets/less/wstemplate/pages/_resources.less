@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Resources Styles
.all-container {
  .container-wrapper {
    min-height: auto;
    width: 600px;
    margin-top: 1rem;
    @media @extraSmallDevices, @smallDevices {
      width: 100%;
    }
    div > .media {
      width: 100%;
      padding: 0;
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      transition: all 0.2s linear;
      -webkit-transition: all 0.2s linear;
      -moz-transition: all 0.2s linear;
      .media-body {
        padding: .5rem 0;
      }
      .readnow {
        display: flex;
        align-items: center;
        padding: 0 1rem;
        width: 100%;
        &:hover{
          text-decoration: none;
        }
      }
      p {
        color: @light-gray;
        font-size: 12px;
      }
      .title {
        padding: 0 10px;
        color: @black;
        font-size: 14px;
        font-weight: @regular;
        a {
          color: @black;
        }
      }
      .box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 42px;
        height: 52px;
        border-radius: 5px;
        margin: 0.5rem 0;
        i {
          width: 32px;
          height: 24px;
          margin: 0 auto;
          display: flex;
        }
        p {
          line-height: 1;
          font-size: 9px;
          margin: 0;
          text-align: center;
          top: 0;
          color: @white;
          font-weight: @semi-bold;
        }
        &.blue {
          background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);
          i {
            background: url("../../images/ws/pdf.svg") center center no-repeat;
          }
        }
        &.green {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgb(85 115 0 / 0.8) 76.95%);
          i {
            background: url("../../images/ws/link.svg") center center no-repeat;
          }
        }
        &.yellow {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);
          i {
            background: url("../../images/ws/flashcard.svg") center center no-repeat;
          }
        }
        &.pink {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgb(183 7 206 / 0.9) 76.95%);
          i {
            background: url("../../images/ws/video.svg") center center no-repeat;
          }
        }
        &.lightgreen {
          background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);
          i {
            background: url("../../images/ws/notes.svg") center center no-repeat;
          }
        }
        &.violet {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
          i {
            background: url("../../images/ws/mcq1.svg") center center no-repeat;
          }
        }
        &.darkgreen {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);
          i {
            background: url("../../images/ws/mindmap.svg") center center no-repeat;
          }
        }
      }
    }
  }
}