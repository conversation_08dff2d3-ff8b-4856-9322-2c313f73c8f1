@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// eBooks Page Styles
.ebooks {
  .store-list-layout {
    @media @extraSmallDevices, @smallDevices {
      flex-direction: column;
    }
  }
  #searchResults {
    @media @extraSmallDevices, @smallDevices {
      padding: 0;
    }
  }
  #content-data-books-ebooks {
    //width: 96%;
    @media @extraSmallDevices, @smallDevices {
      //width: 100%;
      padding: 0;
    }
    .fadein-animated {
      border-radius: 10px;
    }
  }
  .books-list {
    .topSchoolBooks {
      width: auto;
      display: flex;
      justify-content: start;
      margin: initial;
      .image-wrapper {
        width: 155px;
        height: 200px;
        margin-right: 20px;
        @media @extraSmallDevices, @smallDevices {
          width: 100px;
          height: 130px;
          margin-right: 15px;
        }
        .bookShadow {
          height: 100%;
        }
        img {
          width: 100%;
          height: 100%;
        }
        .uncover {
          width: 100%;
          height: 100%;
          p {
            line-height: normal;
            font-size: 12px;
          }
        }
      }
      .content-wrapper {
        margin-top: 0;
        display: flex;
        //align-items: center;
        justify-content: space-between;
        flex-direction: column;
        .left-div {
          display: flex;
          flex-direction: column;
          align-self: start;
        }
        .right-div {
          .stars-outer {
            display: inline-block;
            position: relative;
            &:before {
              content: "\f005 \f005 \f005 \f005 \f005";
              font: var(--fa-font-regular);
              color: @yellow;
            }
          }
          .stars-inner {
            position: absolute;
            top: 0;
            left: 0;
            white-space: nowrap;
            overflow: hidden;
            width: 0;
            &:before {
              content: "\f005 \f005 \f005 \f005 \f005";
              font: var(--fa-font-solid);
              color: @yellow;
            }
          }
          .ratings {
            color: @light-gray;
            font-weight: normal;
          }
        //  text-align: center;
        //  margin-left: 15px;
        //  padding-left: 15px;
        //  min-height: 120px;
        //  border-left: 1px dashed #EEE;
        //  display: flex;
        //  flex-direction: column;
        //  align-items: center;
        //  justify-content: center;
        //  @media @extraSmallDevices, @smallDevices {
        //    text-align: left;
        //    margin-left: 0;
        //    padding-left: 0;
        //    min-height: auto;
        //    border: none;
        //    align-items: start;
        //  }
        }
        @media @extraSmallDevices, @smallDevices {
          display: block;
        }
        h6 {
          overflow: inherit;
          overflow: unset;
          text-overflow: unset;
          -webkit-line-clamp: unset;
          -webkit-box-orient: unset;
          font-size: 16px;
          @media @extraSmallDevices, @smallDevices {
            //font-weight: normal;
            font-size: 13px;
          }
        }
        .book-publisher-name {
          text-transform: none;
          margin-bottom: 10px;
          font-size: 14px;
          @media @extraSmallDevices, @smallDevices {
            font-size: 12px;
          }
        }
        .add_to_cart_btn {
          margin: 5px 0 0;
          bottom: 0;
          width: 150px;
          @media @extraSmallDevices, @smallDevices {
            margin: 0;
            width: 125px;
            padding: 2px;
          }
        }
      }
    }
    .badge-overlay {
      position: relative;
      width: 65px;
      height: auto;
      left: unset;
      top: unset;
      margin: 0 0 5px;
      @media @extraSmallDevices, @smallDevices {
        width: 55px;
        //margin: 0 auto 5px 0;
      }
    }
    .badge {
      border: 1px dashed #b8dcbb;
      background: transparent;
      //background: #e7ffe9;
      color: #00A510;
      width: auto;
      height: auto;
      border-radius: 4px;
      line-height: normal;
      font-weight: 500;
      font-size: 12px;
      padding: 3px;
      //box-shadow: 0 1px 2px #ddd;
      @media @extraSmallDevices, @smallDevices {
        font-size: 11px;
      }
    }
  }

  h1 {
    font-size: 40px;
    @media @mediumDevices {
      font-size: 30px;
    }
    @media @extraSmallDevices, @smallDevices {
      font-size: 20px;
    }
  }

  .share-ebooks-page {
    width: 35px;
    height: 35px;
    cursor: pointer;
    i {
      font-size: 20px;
    }
  }

  //.global-search {
  //  input[type="text"] {
  //    position: relative;
  //    z-index: 10;
  //    padding-left: 20px;
  //    padding-right: 55px;
  //  }
  //  button {
  //    position: relative;
  //    z-index: 10;
  //    width: 48px;
  //    height: 48px;
  //    margin-left: -48px;
  //    .material-icons {
  //      font-size: 24px;
  //      line-height: 1.5;
  //    }
  //  }
  //}

  .ebooks_filter {
    background: @white;
    //width: 96%;
    //margin: 0 auto;
    border-radius: 7px;
    box-shadow: 0 1px 5px 0 rgb(0 0 0 / 10%), 0 1px 10px 0 rgb(0 0 0 / 5%);
    position: sticky;
    top: 77px;
    @media @extraSmallDevices, @smallDevices {
      position: relative;
      top: unset;
      background: transparent;
    }
    .filter-icon-show-hide {
      position: absolute;
      top: -12px;
      right: 10px;
    }
    select {
      font-style: italic;
      font-size: 14px;
      height: 45px;
      background: #FAFAFA;
      color: @light-gray;
      box-sizing: border-box;
      border: 2px solid @light-gray;
      box-shadow: 0 0 10px @gray-light-shadow;
      @media @extraSmallDevices {
        font-size: 13px;
        height: 40px;
      }
      &:hover, &:focus {
        border-color: @warning-btn;
        color: @black;
      }
      &.background-bg {
        background-image: linear-gradient(@warning-btn, @warning-btn);
        border-color: @warning-btn;
        color: @black;
        font-style: normal;
        font-weight: @regular;
      }
      option {
        font-style: normal;
        font-weight: @regular;
      }
    }
    .hiddenFilters {
      display: none;
    }
  }

  #infiniteLoading {
    .loading-div {
      p {
        position: relative;
        letter-spacing: 1px;
        color: #2C3E50;
        font-size: 15px;
      }
      img {
        width: 120px;
        margin-top: -55px;
      }
      .alert {
        font-size: 14px;
      }
    }
  }
  #resetFilter {
    color: @light-gray;
    border: 0;
    padding: 0;
    font-weight: normal;
  }
}
.reach-us {
  .card-modifier {
    flex-direction: unset;
    .reach-us-message {
      h1 {
        font-size: 45px;
        margin-bottom: 10px;
        opacity: 0.7;
        @media @mediumDevices {
          font-size: 35px;
        }
        @media @extraSmallDevices, @smallDevices {
          font-size: 30px;
        }
      }
      h5 {
        opacity: 0.7;
        font-weight: @light;
      }
      img {
        margin-top: 15px;
        @media @extraSmallDevices, @smallDevices, @mediumDevices {
          width: 100%;
        }
      }
    }
    .reach-us-form {
      max-width: 400px;
      min-height: 350px;
      max-height: 350px;
      margin-top: -7rem;
      border-radius: 20px;
      box-shadow: 0 0 10px @gray-light-shadow;
      background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #ffd000 0%, #ff5700 100%);
      @media @extraSmallDevices, @smallDevices {
        min-height: 320px;
        max-height: 320px;
        margin-top: 2rem;
        margin-bottom: -4rem;
      }
      .form-control-modifier {
        font-size: 14px;
      }
      .send-btn {
        background: #FF9901;
        border-color: @white;
        box-shadow: 0 -4px 10px @gray-light-shadow;
        transition: all 0.2s linear;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -o-transition: all 0.2s linear;
        &:hover {
          border-color: transparent;
        }
      }
    }
  }
}
.view-more{
  margin: 0 auto;
  display: flex;
  color: @white !important;
  text-align: center;
  background: #F79420 !important;
  border: none;
  width: 200px;
  height: 40px;
  border-radius: 50px;
  display: block;
}
.wonderslate_main {
  .ebooks {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
    #content-data-books-ebooks {
      margin-top: 0 !important;
    }
    #searchResults {
      padding-left: 0;
      padding-right: 0;
      //@media @extraSmallDevices, @smallDevices, @mediumDevices {
      //  padding-left: 15px;
      //  padding-right: 15px;
      //}
    }
    .banner-ws {
      img {
        border-radius: 0 !important;
      }
    }
    .mobile{
      display: none;
      @media @extraSmallDevices, @smallDevices {
        display: flex;
        margin-top: 60px !important;
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;

      }

    }
    .desktop{
      @media @extraSmallDevices, @smallDevices {
        display: none;
      }
    }
    .section-headerless{
      margin-top: 50px;
      margin-right: 0;
      margin-left: 0;
    }
    .section-with-header{
      margin-top: 60px !important;
    }
    .card {
      border-radius: 16px;
      border: none;
      padding: 25px;

      @media @mediumDevices {
        padding: 20px;
        div {
          flex-direction: column-reverse;
          justify-content: center !important;
          text-align: center;
        }
      }
      .text-and-shop {
        align-self: flex-end;
        padding-right: 10px;
        @media @mediumDevices{
          margin-top: 10px;
          padding-right: 0;
          width: 100%;
        }
        p {
          font-family: @primary-font;
          font-style: normal;
          font-weight: @bold;
          font-size: 24px;
          line-height: 31px;
          color: @black;
        }

        button {
          width: 122px;
          height: 34px;
          border: none;
          border-radius: 16px;
          text-align: center;
          @media @mediumDevices {
            width: 100px;
          }
          a{
            color: @black !important;
          }
        }

      }
      img{
        min-height: 180px;
        min-width: 130px;
        height: 180px;
        width: 130px;
        @media @extraSmallDevices,@smallDevices {
          width: 150px;
          height: 200px;
        }
        @media @mediumDevices{
          height: 100px;
          width: 80px;
          margin: 0 auto;
        }
      }
    }
    .new-release {
      background: linear-gradient(132.37deg, #5FD0E2 17.22%, rgba(95, 208, 226, 0) 130.82%, #5FD0E2 130.82%);
      button{
        background: #65C0CE;
      }
    }
    .book-of-day{
      background: linear-gradient(145.24deg, #B2DCBC 38.27%, rgba(178, 220, 188, 0) 123.5%);
      button{
        background-color: #86B792;
      }
    }
    .trending-now{
      background: linear-gradient(134.65deg, #FACED6 5.62%, rgba(250, 206, 214, 0) 122.6%);
      button{
        background-color: #FEB8C5;
      }
    }
    .shop-now-cards{

      @media @extraSmallDevices,@smallDevices,@mediumDevices {
        margin-bottom: 20px;
      }
      @media @mediumDevices{
        padding: 0 10px !important;
      }
    }

    .ebooks_filter {
      margin-left: auto;
      margin-right: auto;
      //padding-left: 0;
      //padding-right: 0;
      //padding-top: 0 !important;
      //margin-top: 0 !important;
      //background: transparent;
      box-shadow: none;
      @media @extraSmallDevices,@smallDevices {
        width: 100%;
        padding-left: 0;
        padding-right: 0;
        padding-top: 0 !important;
      }
      select {
        height: 40px !important;
        border-radius: 50px;
        background: @white;
        border-width: 1px;
        border-color:#F79420;
        box-shadow: none;
        &:hover, &:focus {
          border-color: #F79420;
        }
        &.background-bg {
          background-image: none;
          background-color: #F79420;
          border-color: #F79420;
        }
      }
    }
  }
}