@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Order Management Page Styles
.order-management {
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
  form {
    label {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: @medium;
    }
    select, input {
      &:focus {
        outline: 0;
        box-shadow: none;
        border-color: @theme-primary-color;
      }
      &.input-error {
        border-color: @red;
      }
    }
    .btn {
      font-weight: @semi-bold;
      width: 150px;
    }
    .form-inline {
      @media @extraSmallDevices, @smallDevices {
        display: block;
      }
    }
    .alert {
      p {
        font-size: 13px;
      }
    }
  }


}