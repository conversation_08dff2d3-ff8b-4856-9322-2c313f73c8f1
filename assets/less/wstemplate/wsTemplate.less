/* WS Template Styles */

// Components
@import "components/alerts.less";
@import "components/buttons.less";
@import "components/cards.less";
@import "components/data_tables.less";
@import "components/datepicker.less";
@import "components/dropdowns.less";
@import "components/form_elements.less";
@import "components/form_errors.less";
@import "components/modals.less";
@import "components/tables.less";
@import "components/typography.less";
@import "components/toggleswitch.less";
@import "components/chart.less";
@import "components/sweet_alerts.less";

// Admin
@import "admin/admin.less";

// Common
@import "common/preloader.less";
@import "common/page_common.less";

// Pages Styles
@import "pages/_books-list.less"; // Books list section (e.g. Store books)
@import "pages/_related-books.less"; // Related and best seller books section
@import "pages/_resources.less"; // Resources (e.g. Notes, MCQ, Flashcards)

@import "pages/ebooks.less"; // eBooks store page
@import "pages/ebook_detail.less"; // eBook detail page
@import "pages/myhome.less"; // My home or Dashboard page, institute home page

@import "pages/mylibrary.less"; // My library page
@import "pages/myactivity.less"; // My activity page
@import "pages/mymaterials.less"; // My materials page
//@import "pages/flashcardHome.less";  //folder page
@import "pages/aboutus";  //aboutUs page
@import "pages/faq";  //FAQ page

@import "pages/nextexam.less"; // Nextexam page
@import "pages/doubts.less"; // Doubts page

@import "pages/mytracker.less"; // My tracker or Trophy room page
@import "pages/mytodo.less"; // My to-do page

@import "pages/index.less"; // Homepage (Landing Page)
//@import "pages/indexNew.less";
@import "pages/information.less"; // Information page
@import "pages/access_code.less"; // Access code page
@import "pages/contact.less"; // Contact page
@import "pages/digital_library.less"; // Digital Library page
@import "pages/edit_profile.less"; // Edit profile page
@import "pages/academy.less"; //Academy Page

@import "pages/self_service.less"; // Self service/Customer support page
@import "pages/publisher.less"; // Publisher page
@import "pages/purchaseOrder.less"; // Book purchase order page

@import "pages/institute_index.less"; // Institute Landing Page
@import "pages/store_integration.less"; // eBooks Store Integration Page
@import "pages/download_apps.less"; // Download Apps Page

@import "pages/leaderboard.less"; // Leaderboard Page
@import "pages/progress_report"; // Progress Report Page
@import "pages/favouriteMcqs";
@import "pages/printbooks"; // Print Books Page

//data-tables
@import "components/dataTables.less";