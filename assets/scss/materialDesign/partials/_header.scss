.sticky-header {
  position: relative;
  z-index: 999;
}
.fixed-header {
  position: fixed;
  top: -90px;
  width: 100%;
  z-index: 9999;
  transition: 0.3s top cubic-bezier(.3, .73, .3, .74);
}
.slideDown {
  top: 0;
}
.wonderslate-navbar {
  background-color: $white-color;
  line-height: normal;
  padding: 12px 24px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  z-index: 3;
  .navbar-container {
    padding-left: 0;
    padding-right: 0;
    .navbar-nav.header-menus {
      margin-left: 100px;
    }
    .nav.navbar-nav.navbar-right {
      li {
        margin-right: 8px;
      }
      li:last-child {
        margin-right: 0;
      }
    }
    .login-btn {
      font-size: $font-size-base;
      color: $violet-color;
      font-weight: 500;
      padding: 11px 25px;
      border-radius: 4px;
    }
    .signup-btn {
      font-size: $font-size-base;
      display: block;
      text-align: center;
      font-weight: 500;
      color: $white-color;
      background: $btn-gradient-background;
      letter-spacing: 0.01em;
      padding: 11px 25px;
      border-radius: 4px;
      &:hover {
        box-shadow: $book-shadow-hover;
      }
    }
  }
  .navbar-brand {
    width: 153px;
    height: 40px;
    background: url('../images/wonderslate/logo.svg');
    background-position: center;
    background-size: 100% 100%;
    display: block;
    text-indent: -9999999px;
  }
  ul {
    a {
      font-size: $font-size-large;
      font-weight: 300;
      color: $font-color-light-grey;
      padding-top: 9px;
      &:hover {
        background-color: $white-color;
      }
    }
    a.active {
      font-weight: 500;
      color: $white-color;
      background: $menu-gradient-background;
      padding: 11px 15px;
      box-shadow: 1px 0px 0px rgba(68, 68, 68, 0.04);
      border-radius: 4px;
    }
  }
}
.user-logged-in {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #fff !important;
  padding: 0 !important;
  img {
    max-width: 100% !important;
    height: auto;
    border: 1px solid #BDBDBD;
    border-radius: 100%;
  }
}
.profile-dropdown {
  min-width: 330px;
  left: auto;
  right: -8px !important;
  padding: 24px;
  border-top-right-radius: 4px !important;
  border-top-left-radius: 4px !important;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  overflow-wrap: break-word;
  &:before {
    content: "";
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    position: absolute;
    top: -10px;
    right: 16px;
    z-index: 10;
  }
  &:after {
    content: "";
    border-bottom: 12px solid #ccc;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    position: absolute;
    top: -12px;
    right: 14px;
    z-index: 9;
  }
  a {
    padding: 0;
    &.text-zoom {
      width: 60px;
      height: 40px;
      font-size: $font-size-large;
      font-weight: bold;
      padding: 6px 22px;
      border: 2px solid $grey-border;
      border-radius: $border-radius;
      transform: matrix(-1, 0, 0, 1, 0, 0);
      margin-right: 20px;
    }
  }
  li {
    display: inline-block;
  }
}
.user-image {
  position: relative;
  width: 72px;
  height: 72px;
  float: left;
  margin-right: 24px;
  text-align: center;
  img {
    max-width: 100%;
    height: auto;
    border: 1px solid #BDBDBD;
    border-radius: 100%;
  }
  .user-edit-profile {
    color: $white-color;
    font-size: 8px;
    position: absolute;
    bottom: 2px;
    left: 4px;
    padding: 8px 8px;
    background: rgba(0, 40, 71, 0.64);
    text-decoration: none;
    width: 90%;
    margin-bottom: 0;
    border-bottom-left-radius: 32px;
    border-bottom-right-radius: 32px;
    &:hover {
      color: $white-color;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none;
    }
    &:focus {
      color: $white-color;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none;
    }
    &:active {
      color: $white-color;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none;
    }
  }
}
.logged-in-user-details {
  float: left;
  max-width: 166px;
  .loggedin-user-name {
    font-weight: 300;
    .user-name {
      font-weight: 500;
      text-transform: capitalize;
    }
  }
  .loggedin-user-mobile {
    font-weight: 300;
  }
  .loggedin-user-email {
    @extend .loggedin-user-mobile;
  }
}
.user-orders {
  clear: both;
  float: left;
  width: 100%;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid rgba(68, 68, 68, 0.2);
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
  a {
    color: #444;
    &:hover {
      color: #444;
      text-decoration: none;
    }
    &:active {
      color: #444;
      text-decoration: none;
    }
    &:focus {
      color: #444;
      text-decoration: none;
    }
  }
}
.user-logout {
  clear: both;
  float: left;
  width: 100%;
  margin-top: 16px;
  p {
    font-weight: 300;
    color: #000;
    margin: 0;
    a {
      font-weight: 400;
      color: $orange-color;
    }
  }
}