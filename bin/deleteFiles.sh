#!/bin/bash

# - Copyright (c) 2018 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;
HOME=/u01/ws; export HOME;

CLASSPATH=$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

explorefolder() {
for d in $1/*; do
  if [ -d "$d" ]; then
    #echo exploring "$d" ....
    explorefolder $d
  else

    if [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *pdf  && `echo $d | tr '[:upper:]' '[:lower:]'` == */books/* ]]
    then
        f=`echo "$d" | sed 's/.pdf/.ws/g'`

        if [ -f "$f" ]
        then
            echo "$f" exists so deleting
            echo "      $d (`ls -lh $d |  awk '{ print $5}'`)"
            rm "$d"
        fi
    elif [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *zip && `echo $d | tr '[:upper:]' '[:lower:]'` == */books/* ]]
    then
        f=`echo "$d" | sed 's/.zip/.ws/g'`

        if [ -f "$f" ]
        then
            echo "$f" exists so deleting
            echo "      $d (`ls -lh $d |  awk '{ print $5}'`)"
            rm "$d"
        fi
    elif [[ `echo $d | tr '[:upper:]' '[:lower:]'` == *epub && `echo $d | tr '[:upper:]' '[:lower:]'` == */books/* ]]
    then
        f=`echo "$d" | sed 's/.epub/.ws/g'`

        if [ -f "$f" ]
        then
            echo "$f" exists so deleting
            echo "      $d (`ls -lh $d |  awk '{ print $5}'`)"
            rm "$d"
        fi
    fi
  fi
done
}

if [ -z "$1" ]
  then
    echo "Syntax ./deleteFiles.sh <path>"
    exit
fi

echo `date`
explorefolder $1
java -classpath $CLASSPATH email "<EMAIL>" "WonderSlate delete done!" "WonderSlate delete done!";
echo `date`