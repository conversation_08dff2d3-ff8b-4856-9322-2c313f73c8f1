import java.io.PrintStream;
import java.io.Serializable;
import java.util.*;
import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;

public class email implements Serializable {
    public email() {
        host = "email-smtp.us-west-2.amazonaws.com";
		//port = 25;
        //port = 465;
        port = 587;
        initialize();
    }

    public email(String s) {
        host = s;
        initialize();
    }

    public void initialize() {
        //_from = "<EMAIL>";
        _from = "E-Utkarsh <<EMAIL>>";
        debug = false;

    	props = System.getProperties();
        props.put("mail.smtp.host", host);
    	props.put("mail.transport.protocol", "smtp");
    	props.put("mail.smtp.port", port); 
    	props.put("mail.smtp.starttls.enable", "true");
    	props.put("mail.smtp.auth", "true");		
		
		//SMTP_USERNAME = "AKIAI6TF3KG4B4FQ642A";  
		//SMTP_PASSWORD = "Amq1jjmbtFv7/DlSBkfgMsotvU64H9cSsrINPaPN9awL";		
		SMTP_USERNAME = "AKIAI32UO6R4M6FWO3PA";  
		SMTP_PASSWORD = "Avj45f/D+pZGwf32J+lky63JliZAivY9j0HcAsHa86kN";	        
		
        if(debug) props.put("mail.debug", host);
		
		
		session = Session.getDefaultInstance(props, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(SMTP_USERNAME, SMTP_PASSWORD);
			}	
		});

        session.setDebug(debug);
    }

    public void SendMail(String s, String s1, String s2) throws Exception {
        String as[] = {
            s
        };
		
        doSend(as, _from, s1, s2);
    }

    public void SendMail(String s, String s1, String s2, String s3) throws Exception {
        String as[] = {
            s
        };
		
        doSend(as, s1, s2, s3);
    }

    public void SendMail(String as[], String s, String s1) throws Exception {
        doSend(as, _from, s, s1);
    }

    public void SendMail(String as[], String s, String s1, String s2) throws Exception {
        doSend(as, s, s1, s2);
    }

    private void SendMailWithAttachments(String s, String s1, String s2, String s3) {
        String as[] = {
            s
        };
		
        String as1[] = {
            s3
        };
		
        doSendWithAttachment(as, s1, s2, as1);
    }

    private void SendMailWithAttachments(String as[], String s, String s1, String s2) {
        String as1[] = {
            s2
        };
		
        doSendWithAttachment(as, s, s1, as1);
    }

    private void SendMailWithAttachments(String s, String s1, String s2, String as[]) {
        String as1[] = {
            s
        };
		
        doSendWithAttachment(as1, s1, s2, as);
    }

    private void SendMailWithAttachments(String as[], String s, String s1, String as1[]) {
        doSendWithAttachment(as, s, s1, as1);
    }

    private void doSend(String as[], String s, String s1, String s2) throws Exception {
        if(s == null || "".equals(s))
            _from = "E-Utkarsh <<EMAIL>>";
        else
            _from = s;
		
		
		Transport transport=null;
        MimeMessage mimemessage=null;        
		
        try {           
            // Create a transport.
            transport = session.getTransport();
            mimemessage = new MimeMessage(session);
            mimemessage.setFrom(new InternetAddress(_from));
            for(int j = 0; j < as.length; j++)
                mimemessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(as[j]));

            mimemessage.setSubject(s1);
            mimemessage.setSentDate(new Date());
            mimemessage.setText(s2);
			
			// Connect to Amazon SES using the SMTP username and password you specified above.
            if(!transport.isConnected()) transport.connect(host, port, SMTP_USERNAME, SMTP_PASSWORD);	
            
            transport.sendMessage(mimemessage,mimemessage.getAllRecipients());
        } catch (javax.mail.MessagingException e) {
            try {
                if(!transport.isConnected()) transport.connect(host, port, SMTP_USERNAME, SMTP_PASSWORD);
                // send the message
                transport.send(mimemessage);                    
            } catch(Exception e1) {
                e1.printStackTrace();
            }   
        } catch(Exception e) {
            e.printStackTrace();
        }
    }

    private void doSendWithAttachment(String as[], String s, String s1, String as1[]) {       
        Transport transport=null;
        MimeMessage mimemessage=null;
        	
        try {
            // Create a transport.
            transport = session.getTransport();            
            
            mimemessage = new MimeMessage(session);
            mimemessage.setFrom(new InternetAddress(_from));
            for(int i = 0; i < as.length; i++)
                mimemessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(as[i]));

            mimemessage.setSubject(s);
            mimemessage.setText(s1);

			// create the Multipart and its parts to it
            Multipart mp = new MimeMultipart();
            MimeBodyPart mbp = new MimeBodyPart();
            FileDataSource fds;
            mbp.setText(s1);
            mp.addBodyPart(mbp);

            // create and fill the first message part
            for(int j = 0; j < as1.length; j++) {
                // create  message part
                mbp = new MimeBodyPart();
                // attach the file to the message
                fds = new FileDataSource(as1[j]);
                mbp.setDataHandler(new DataHandler(fds));
                mbp.setFileName(fds.getName());
                mp.addBodyPart(mbp);
            }

            // add the Multipart to the message
            mimemessage.setContent(mp);
            // set the Date: header
            mimemessage.setSentDate(new Date());
            			
			if(!transport.isConnected()) transport.connect(host, port, SMTP_USERNAME, SMTP_PASSWORD);
            // send the message
            transport.send(mimemessage);
        } catch (javax.mail.MessagingException e) {
            try {
                if(!transport.isConnected()) transport.connect(host, port, SMTP_USERNAME, SMTP_PASSWORD);
                // send the message
                transport.send(mimemessage);                    
            } catch(Exception e1) {
                e1.printStackTrace();
            }                    
        } catch(Exception e) {
            e.printStackTrace();
        }
    }

    private static void usage() {
        System.out.println("usage: java email <to> <subject> <msgText> ");
    }

    public static void main(String args[]) throws Exception {
        if(args.length < 3){
            usage();
            System.exit(1);
        }
		
        email email1 = new email();
        if(args.length > 3) {
            email1.SendMailWithAttachments(args[0], args[1], args[2], args[3]);
        } else
            email1.SendMail(args[0], args[1], args[2]);
    }

    public void SendMailWithOptions(String as[], String as1[], String as2[], String s, String s1, String s2) throws Exception {
        doSendWithOptions(as, as1, as2, s, s1, s2);
    }

    private void doSendWithOptions(String as[], String as1[], String as2[], String s, String s1, String s2) throws Exception {
        if(s == null || "".equals(s))
            _from = "E-Utkarsh <<EMAIL>>";
        else
            _from = s;
        StringBuffer stringbuffer = new StringBuffer();
        for(int i = 0; i < as.length; i++)
            stringbuffer.append(as[i]).append(", ");

        stringbuffer.delete(stringbuffer.length() - 2, stringbuffer.length());
        try
        {
            MimeMessage mimemessage = new MimeMessage(session);
            mimemessage.setFrom(new InternetAddress(_from));
            for(int j = 0; j < as.length; j++)
                mimemessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(as[j]));

            for(int k = 0; k < as1.length; k++)
                mimemessage.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(as1[k]));

            for(int l = 0; l < as2.length; l++)
                mimemessage.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(as2[l]));

            mimemessage.setSubject(s1);
            mimemessage.setSentDate(new Date());
            mimemessage.setText(s2);
			transport.connect(host,port, SMTP_USERNAME, SMTP_PASSWORD);
            transport.send(mimemessage);
        } catch(Exception e) {
            e.printStackTrace();
            System.out.println();
        }
    }

    private String host;
	private int port;
    private String _from;
    private boolean debug;
    private Properties props;
    private Session session;
	private Transport transport;
	private String SMTP_USERNAME;
	private String SMTP_PASSWORD;
}