<g:render template="/wonderpublish/loginChecker"></g:render>


<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-6 main text-center' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <h4><strong>Email Verification</strong></h4>
                <div class="form-inline p-4">
                    <div class="flex_st1 col-12 p-0">
                        <div class="form-group">
                            <input type="texts" class="form-control col-5" name="userEmail" id="userEmail"  placeholder="Enter email" style="width: 300px;"/>

                            <button class="btn btn-primary col-2 ml-3"  onclick="emailSubmit();" style="margin:10px auto;">VERIFY</button>
                        </div>
                        <div id="batchUsers"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<script>

    function emailSubmit(){
        var userEmail = document.getElementById("userEmail").value;
        <g:remoteFunction controller="admin" action="emailWhitelist"  params="'userEmail='+userEmail"    onSuccess = "emailStatus(data)"/>

    }

    function emailStatus(data){
        if(data.status=="ok"){
            document.getElementById("batchUsers").innerHTML= "Email whitelisted successfully!";
            document.getElementById("batchUsers").style.color = "forestgreen";
        }else if(data.status=="already done")
        {
            document.getElementById("batchUsers").innerHTML = "This email is already whitelisted.";
            document.getElementById("batchUsers").style.color = "red";
        }else if(data.status=="not found"){
            document.getElementById("batchUsers").innerHTML = "No user found with this email.";
            document.getElementById("batchUsers").style.color = "red";
        }
    }

</script>


</body>
</html>