<div class="category-content d-flex justify-content-center" id="menuBoard">
<div class="normalHeader w-100">
    <nav class="ws-header container navbar navbar-expand-md navbar-default">
        <div class="d-none">
        <a class="navbar-brand" href="/books/index?mode=browse"><img class="logo" src="${assetPath(src: 'landingpageImages/wslogo_main.svg')}" alt="wonderslate"></a>
        </div>
        <g:render template="/books/mainBoardtabs"></g:render>
        <div class="d-none">

        <g:render template="/books/loginmenu"></g:render>

        <g:render template="/books/mobileMenus"></g:render>
         </div>
    </nav>
</div>

</div>


<script type="text/javascript">
function findOffset(element) {
    var position=element.getBoundingClientRect().top;
    return position;
}

window.onload=function (ev) {
    var content=document.getElementById('menuBoard');
    var menuPosition=findOffset(content);
    var header=document.querySelector('header');
    window.onscroll=function () {
        var bodyScrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        if((bodyScrollTop + 64) > menuPosition){
            content.classList.add('position-fixed');
            content.style.top='0';
            content.style.marginTop='0';
            header.classList.remove('position-sticky');
        }
        else{
            content.classList.remove('position-fixed');
            content.style.top='unset';
            header.classList.add('position-sticky');
            content.style.marginTop='2.5rem';
        }

    }
}
</script>