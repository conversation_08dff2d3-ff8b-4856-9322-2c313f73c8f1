<%@ page import="com.wonderslate.institute.InstituteIpAddress; com.wonderslate.data.UtilService; com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"Wolters Kluwer India"%></title>
    <meta name="description" content="Wolters Kluwer India">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'wolterskluwer/favicon.ico')}" type="image/x-icon">

    <meta name="theme-color" content="#2EBAC6" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    %{--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-rc.2/css/materialize.min.css">--}%
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link href="https://fonts.googleapis.com/css?family=Merriweather:400,700|Rubik:400,500" rel="stylesheet">
    <asset:stylesheet href="landingpage/sageEvidya.css"/>
    <asset:stylesheet href="landingpage/wolterKluwer.css" async="true"/>
    <asset:stylesheet href="landingpage/sageStyle.css" async="true"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css"/>

</head>
<%session['siteId'] = new Integer(20);%>


<body class="eutkarsh evidya" data-spy="scroll" data-target=".ws-header" data-offset="50">
<sec:ifNotLoggedIn>
%{--<g:render template="/funlearn/signupNew"></g:render>--}%
</sec:ifNotLoggedIn>

<header class="sageEvidya">
    <%if(showHeader==null||"true".equals(showHeader)){%>
    <div class="ws-menu-start">
        <nav class="ws-header navbar navbar-expand-md navbar-default">
            <a href="/wolterskluwer/store"><span class="logo"><img  src="${assetPath(src: 'wolterskluwer/logo.jpg')}" alt="eVidya"></span></a>
            <div class="d-sm-none mobile-profile">
                <sec:ifNotLoggedIn>
                    <li class="nav-item">
                        <a class="nav-link login">LOGIN & SIGN UP</a>
                    </li>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <li class="nav-item">
                        <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                    <%}%>
                                    <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </div>
            %{--<a class="mobile-toggle d-md-none" onclick="openNav()"><i class="material-icons">menu</i></a>--}%
            <ul class="navbar-nav mr-auto d-none d-md-flex">
                <li class="nav-item">
                    <a class="nav-link estore" href="/evidya/store">Store</a>
                </li>
                <% if(session['userdetails']!=null||(showLibrary)){%>
                <li class="nav-item">
                    <a class="nav-link estore" href="/wolterskluwer/library">Library</a>
                </li>
                <%}%>
                <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                                Publishing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                                <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tabs</a>
                                <a class="dropdown-item" href="/wonderpublish/manageExams">Manage Exams</a>
                                <a class="dropdown-item" href="/institute/isbnKeyword">Manage Isbn/Keywords</a>
                                %{--                     <a href="/institute/isbnKeyword">Manage Isbn/Keywords</a>--}%


                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">
                                Digital Marketing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/header/index">Header Management</a>
                                <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">Print books</a>
                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_FINANCE">
                        <li class="nav-item">
                            <a href="/publishing-sales" class="nav-link">Sales</a>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                                Admin
                            </a>
                            <div class="dropdown-menu">
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                    <a class="dropdown-item" href="/institute/admin">Institute Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/libAdmin">Institute/Library Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/usageReport">Usage Report</a>
                                </sec:ifAllGranted>
                            </div>
                        </li>
                    </sec:ifAnyGranted>

                </sec:ifLoggedIn>

            </ul>
            <ul class="navbar-nav right-menu d-none d-md-flex align-items-center mt-2 mt-md-0">
                %{--<li class="nav-item">--}%
                %{--<a class="nav-link" href="https://play.google.com/store/apps/details?id=com.utkarshnew.android" target="_blank">DOWNLOAD APP</a>--}%
                %{--</li>
                <li class="nav-item">
                    <a class="nav-link">Wishlist</a>
                </li>--}%
                <li class="nav-item browse">
                    <a class="nav-link">Browse</a>
                    <div class="browseMenu">
                        <div class="row">
                            <div class="col-6">
                                <h4>Browse by Subject</h4>

                                <div class="mt-3 d-flex align-items-center subHeader">
                                    <div class="circle1">
                                        <img src="${assetPath(src: 'sage/Management_Icon.svg')}" class="img-responsive" alt="">
                                    </div>
                                    <h4><a href="/wolterskluwer/store?grade=Business"> Management </a></h4>

                                </div>

                                <div class="mt-3 d-flex align-items-center subHeader">
                                    <div class="circle1">
                                        <img src="${assetPath(src: 'sage/Social_Sciences_Icon.svg')}" class="img-responsive" alt="">
                                    </div>
                                    <h4> <a href="/wolterskluwer/store?grade=Social Science">Social Science</a> </h4>

                                </div>
                            </div>
                            <div class="col-6">
                                <h4>Browse by Language</h4>
                                <ul class="language">
                                    <li><a href="/wolterskluwer/store?language=English">English</a></li>
                                    <li><a href="/wolterskluwer/store?language=Hindi">Hindi</a></li>
                                    <li><a href="/wolterskluwer/store?language=Marathi">Marathi</a></li>
                                    <li><a href="/wolterskluwer/store?language=Bengali">Bengali</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>

                <sec:ifNotLoggedIn>

                    <li class="nav-item">
                        <a class="nav-link login-btn" id="evidyaLogin">Login</a>
                    </li>
                    <div class="evidyaLogin">
                        <div class="evidyaloginWrapper" >
                            <p>Log-in to access your personalized dashboard for additional features. </p>
                            <form method="post" action="/login/authenticate">
                                <label>Email</label>
                                <input type="text" class="form-control" id="email" name="username" required>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="password" name="password" required>
                                <a href="javascript:showForgetPassword();" class="mt-4 frgtPassword">Forgot Password?</a>

                                <input type="submit" id="sign-in" class="btn btn-login" value="Log In">
                            </form>
                            <div id="loginFailedEvidya" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>
                            <button class="btn btn-create" onclick="javascript:createAccount();">Create a new account</button>
                            <button type="button" class="googleLogin sign-google" id="googleSignInButton"  onclick="googleSignInCalled();">Login with Google</button>


                        </div>
                        <div class="evidyaSignup" style="display: none;">
                            <h3 class="text-center">Sign Up </h3>
                            <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
                                <label>Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <label class="mt-2">Phone Number</label>
                                <input class="form-control" type="text" name="mobile" id="mobile" required maxlength="10" minlength="10">
                                <div class="input-error-tooltip" style="display: none;">
                                    <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
                                </div>
                                <label class="mt-2">Email</label>
                                <input class="form-control" type="email" id="username" name="username" required>
                                <div class="email-error" id="emailexists" style="display: none;">
                                    <p class="email-error-text">This email is already taken. Please sign in.</p>
                                </div>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="signup-password" name="password" required>
                                <label class="mt-2">State</label>
                                <select class="form-control" name="state" id="state" required>
                                    <option value=""></option>
                                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                    <option value="Assam">Assam</option>
                                    <option value="Bihar">Bihar</option>
                                    <option value="Chandigarh">Chandigarh</option>
                                    <option value="Chhattisgarh">Chhattisgarh</option>
                                    <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                    <option value="Goa">Goa</option>
                                    <option value="Gujarat">Gujarat</option>
                                    <option value="Haryana">Haryana</option>
                                    <option value="Himachal">Himachal Pradesh</option>
                                    <option value="Jammu & Kashmir">Jammu & Kashmir</option>
                                    <option value="Jharkhand">Jharkhand</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Kerala">Kerala</option>
                                    <option value="Lakshadweep">Lakshadweep</option>
                                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Manipur">Manipur</option>
                                    <option value="Meghalaya">Meghalaya</option>
                                    <option value="Mizoram">Mizoram</option>
                                    <option value="Nagaland">Nagaland</option>
                                    <option value="Odisha">Odisha</option>
                                    <option value="Puducherry">Puducherry</option>
                                    <option value="Punjab">Punjab</option>
                                    <option value="Rajasthan">Rajasthan</option>
                                    <option value="Sikkim">Sikkim</option>
                                    <option value="Tamil Nadu">Tamil Nadu</option>
                                    <option value="Telangana">Telangana</option>
                                    <option value="The Government of NCT of Delhi">The Government of NCT of Delhi</option>
                                    <option value="Tripura">Tripura</option>
                                    <option value="Uttarakhand">Uttarakhand</option>
                                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                                    <option value="West Bengal">West Bengal</option>
                                </select>
                                <input type="hidden" name="email">
                                <input type="hidden" name="otp_finished" id="otp_finished">
                                <input type="button" id="signup" onclick="javascript:formSubmit();" class="btn btn-lg continue mt-4" value="Continue">
                                <a onclick="javascript:loginContinue();" class="mt-4 loginContinue">Already have an Account?Click here to login</a>
                            </g:form>
                        </div>
                        <div class="forgotPassword" style="display: none;">
                            <h3>Reset Password</h3>
                            <p class="mt-4">To recieve a link to reset your password, please enter your eVidya account email address.</p>
                            <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                                <label class="mt-2">Email</label>
                                <input class="form-control"  type="text" name="username" id="fPemail" required>
                                <span id="showEmailerror" style="color:red;
                                display: block;
                                margin: 7px;"></span>
                                <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset mt-2" value="Send reset link">
                                <a id="back-login" class="btn btn-back mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </g:form>
                        </div>
                        <div id="reset-password-completed" style="display: none;">
                            <div class="text-center">
                                <p>Password reset link sent.</p>
                                <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from  <%=siteName%> within a couple of minutes, please check your spam folder.</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="reset-google-paswd" style="display: none">
                            <div class="text-center">
                                <p>Password reset not possible!</p>
                                <p>We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="account-exists" style="display: none">
                            <div class="text-center" style="margin-top: 6rem;">
                                <p class="notRegister">This Email is not registered.<br> Please signup!</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackSingup();">Back to Signup</a>
                            </div>
                        </div>
                    </div>

                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <li class="nav-item notification">
                        <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                    <%}%>
                                    <span class="edit-btn"><i class="material-icons">edit</i></span>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </ul>
        </nav>

    </div>
    <%}%>
</header>



<% if (!hideBottomIcons) { %>
<ul class="mobile-bottom-menu-wrapper hidden-md hidden-sm hidden-lg">
    <sec:ifNotLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="estore mobile-bottom-menu-item-link mobile-store">Store</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="javascript:showSignUpModal();" class="mobile-bottom-menu-item-link mobile-library">Library</a>
            Library
        </li>

    </sec:ifNotLoggedIn>

    <sec:ifLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="mobile-bottom-menu-item-link mobile-store">Store</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="/wlibrary/library" class="mobile-bottom-menu-item-link mobile-library">Library</a>
            Library
        </li>

    </sec:ifLoggedIn>
</ul>
<% } %>
<div class="modal" id="signInSuccess">
    <div class="modal-dialog  modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">


            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="color:#233982;font-size: 16px;">Signed in Successfully</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                %{--                <button type="button" class="btn btn-danger" data-dismiss="modal">Continue</button>--}%
            </div>

        </div>
    </div>
</div>



<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";


    function createAccount(){
        $('.evidyaloginWrapper').hide();
        $('.evidyaSignup').show();
    }
    function loginContinue() {
        $('.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
    }
</script>

<script src="https://apis.google.com/js/api:client.js"></script>
<script>
    var otpReg = "${otpReg}";

    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function formSubmit() {
        if (validateSignUp()) {
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
            checkUsernameExists();
        }
    }

    function validateSignUp(){
        var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';

        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                // $("#"+flds[i]).css('border', 'none');

            }
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }
        }

        return allFilled;
    }

    function checkUsernameExists() {
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            $('#connecting-div').show();

            if(otpReg=="true") {
                $('#loginSignup').modal('hide');
                $('#otp-mobile').val($('#mobile').val());
                $('#otp-email-txt').text($('#username').val());
                $('#otp-email-txt1').text($('#username').val());
                $('#otp-mobile1').text($('#otp-mobile').val());
                $('#otp-next').modal('show');
            } else {
                document.adduser.submit();
            }

            $('#sign-up-div').hide();
        } else {
            $('#loginSignup').modal('show');
            document.getElementById('emailexists').style.display = 'block';
        }
    }

    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {

                if(siteId==20){
                    document.getElementById('showEmailerror').innerHTML='Please enter valid Email address';
                }
                else {
                    return false;
                }
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            // $('#loginSignup').modal('show');
            // $('#loginSignup').attr('data-page', 'reset-completed');
            $('.forgotPassword').hide();
            $('#reset-password-completed').show();
            $('#fp-user-email').html("“"+userEmail+"”");
        } else if("Google"==data.status) {
            $('.forgotPassword').hide();
            $('#reset-google-paswd').hide();
            $('#fp-user-email1').html("“"+userEmail+"”");
        }
        else if("Fail"==data.status){
            $('.forgotPassword').hide();
            $('#account-exists').show();

        }
    }

    <%  if(params.loginFailed=="true"&&!"20".equals(""+session["siteId"])) {%>
    $(window).on('load',function() {
        $('#loginSignup').modal('show');
    });
    <%  } %>
    function showForgetPassword() {
        $('.evidyaloginWrapper').hide();
        $('.forgotPassword').show();
    }
    function evidyabackSingup(){
        $('#account-exists').hide();
        $('.evidyaSignup').show();

    }

    function logoutClicked() {
        document.cookie = '';
        document.cookie = 'currentUrl' + "=" + window.location.href + "; path=/";
        window.location.href ="/logoff";
    }

</script>

