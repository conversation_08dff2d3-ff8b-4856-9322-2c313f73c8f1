<%
  String requestURL = request.getRequestURL().toString();
  String servletPath = request.getServletPath();
  String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));
  session.setAttribute("servername", appURL);
%>

<div class="modal fade register-login-modal" id="registration-modal" tabindex="-1" role="dialog" aria-labelledby="registration-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">  
      <div class="modal-body">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h2 class="modal-label">Sign Up to Wonderslate</h2>
        <div class="social-login-btns">
          <p class="easy-social-login">Easy Login</p>
          <div class="social-btn">
            <button type="button" class="social-login-btn google">Google</button>
            <button type="button" class="social-login-btn facebook">Facebook</button>
          </div>
          <div class="email-login">
            <p class="using-email">or using email</p>
          </div>
          <div class="user-login">
            <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off" class="login-form">
              <input type="text" class="form-control input-email" name="name" id="name" name="name" placeholder="Your Name" required>
              <input type="email" class="form-control input-password" name="username" id="username" placeholder="Your Email Address" required>
              <input type="password" name="password" id="password" class="form-control input-password" placeholder="Choose Password"></input>
              <input type="text" name="mobile" id="mobile" class="form-control input-password" placeholder="Your Mobile No"></input>
              <div class="form-group">
                <input type="submit" id="signup" class="btn btn-block login-btn" value="SIGN UP">
              </div>
              <div class="form-group forgot-password-group">
                <p class="create-account">Already have an account? <a href="#login-modal" data-toggle="modal" data-dismiss="modal">Login</a></p>
                <p class="terms-and-conditions">By Signing Up you agree to our <a href="#">Terms and Conditions</a></p>
              </div>
            </g:form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.getElementById('signup').addEventListener('click', formSubmit);
var flds = new Array (
  'name',
  'username',
  'password',
  'mobile'
);
function formSubmit() {
  if (validate()) {
    document.adduser.username.value = document.adduser.username.value.toLowerCase();
    document.adduser.email.value = document.adduser.username.value.toLowerCase();
    checkUsernameExists();
  }
}

function validate(){
  var allFilled=true
  $('.alert').hide();
  document.getElementById('emailerror').style.display = 'none';
  document.getElementById('emailexists').style.display = 'none';
  for (i=0; i<flds.length; i++) {

      if( !$("#"+flds[i]).val() ) {
          //actual code to check all fields needs to be entered. use the array of fields
          $("#"+flds[i]).siblings('.bar').addClass('iColor');
          $("#"+flds[i]).siblings('.labe').addClass('labelAlert');
          allFilled = false;
      } else {
        $("#"+flds[i]).siblings('.bar').removeClass('iColor');
        $("#"+flds[i]).siblings('.labe').removeClass('labelAlert');
    }
  }
  if(!allFilled) {
    $('.alert').show();
  } else {
    var email = $("#username").val();
    var atpos = email.indexOf("@");
    var dotpos = email.lastIndexOf(".");
      if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
          document.getElementById('emailerror').style.display = 'block';
          return false;
        }
      }
    return allFilled;
}

    function checkUsernameExists(){
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);'
                params="'username='+username" />
    }
    function userNameExistsResult(exists){
        "0"==exists?document.adduser.submit():(document.getElementById('emailexists').style.display = 'block');

    }

</script>