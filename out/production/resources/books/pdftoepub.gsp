<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/mobile.css" media="screen"/>
<asset:stylesheet href="wonderslate/desktop.css" media="only screen and (min-width: 1020px)"/>
<link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>

<style>
.pagetitle{
    margin-bottom: 2rem !important;
}
.pagetitle-sub{
    font-size: 18px;
    width: 75%;
    margin: 0 auto;
}
.pagetitle-span{
    color: orange;
}
.mod h3{
    font-size: 45px;
}
.benefits-ul li{
    margin-bottom: 3rem !important;
}
.mod{
    padding: 20px 0 !important;
}
.prep_explain .steps{
    background: #fff;
    color: #000;
}
.wrap.logos .col.in-view img{
    height: 133px !important;
}
@media (max-width: 768px) {
    .wrap.logos .col.in-view img{
        height: 88px !important;
    }
}
</style>

<body class="prep_capsule">
<div class="container">


    <section class="mod prep_content" id="content" data-bgcolor="#282D2D">
        <div class="wrap">
            <div class="col phone" data-scroll>
                <img src="${assetPath(src: 'products/step-11.png')}" data-src="${assetPath(src: 'products/step-11.png')}">
            </div>
            <div class="col feature" data-scroll>
                <h3 class="animate-title"><span class="animate-line line2"><span> PDF to EPUB Conversion Service</span></span></h3>
                <p class="animate-text">We provide best in class PDF to EPUB conversion. We work with more than 200+ satisfied publishers. </p>
                <p class="animate-text">We convert both English and Hindi PDFs.</p>
            </div>
        </div>
        <div class="word single-word word-content" data-scroll data-scroll-direction="horizontal" data-scroll-speed="-6" data-scroll-target="#content"></div>
    </section>

    <section class="mod prep_coach" id="coach" data-bgcolor="#263238">
        <div class="wrap">
            <div class="col feature" data-scroll>
                <h3 class="animate-title"><span class="animate-line line2"><span>Benefits of Converting to EPUB</span></span></h3>
                <p class="animate-text"><b>Enhanced Reading Experience on All Devices:</b> EPUB files are designed for responsive, flowable content, ensuring a superior reading experience on a wide range of devices, including iPads, iPhones, and popular eBook readers.</p>
                <p class="animate-text"><b>Safety & Security:</b> Your file security is our top priority. We ensure that your files are not accessible on our servers for longer than necessary. Your PDFs and EPUB files are automatically removed from our system after a short time.</p>
                <p class="animate-text"><b>No Installation Required:</b> You can convert PDF to EPUB without installing any software or burdening your system. The entire conversion process is handled on our servers, making it accessible from any device with an internet connection.</p>
                <p class="animate-text"><b>Compatibility with Common Systems:</b> Our service works seamlessly under all common operating systems and web browsers, with no special system requirements.</p>
                <p class="animate-text"><b>Formatting Preservation:</b> We take care to ensure that your converted EPUB files maintain the original formatting, including tables, images, and special characters. Your content will look exactly as intended.</p>
            </div>
        </div>
        <div class="word single-word word-coach" data-scroll data-scroll-direction="horizontal" data-scroll-speed="6" data-scroll-target="#coach"></div>
    </section>


    <section class="mod prep_explain" data-bgcolor="#282D2D">
        <div class="">
            <h3 class="animate-title text-center mb-3">Why EPUB Is Ideal for <span class="pagetitle-span">Publishers</span></h3>
            <div class="col steps mx-auto w-75" data-scroll>
                <ul class="benefits-ul">
                    <li><i class="material-icons-round">check</i> Enhanced Compatibility: EPUB files are versatile and compatible with a wide range of eReaders and devices, allowing publishers to reach a broader audience.</li>
                    <li><i class="material-icons-round">check</i> Dynamic Content: EPUB supports interactive and multimedia elements, making it ideal for educational materials, textbooks, and digital magazines.</li>
                    <li><i class="material-icons-round">check</i> Improved User Experience: Readers can customize the font size, layout, and other settings, enhancing the overall reading experience.</li>
                    <li><i class="material-icons-round">check</i> Easy Updates: EPUBs can be easily updated or revised, ensuring that readers always have access to the latest content.</li>
                    <li><i class="material-icons-round">check</i> Search Engine Optimization (SEO): EPUBs are search engine-friendly, making it easier for readers to discover your content online.</li>
                </ul>
            </div>
        </div>
    </section>

    <section>
        <div class="mx-auto w-75">
            <p style="font-size: 20px;line-height: 40px">
               Unlock the benefits of EPUB and join the growing community of publishers who are making the switch to this superior eBook format. Contact us today to start converting your PDFs to EPUB and discover a world of possibilities.</p>
        </div>
    </section>

    <section class="mod prep_contact">
        <div class="wrap" style="flex-direction: column">
            <div class="title_wrapper">
                <h1> Pricing: Per Page Rs.15 + GST</h1>
                <p style="text-align: center">Contact Us to learn more about our PDF to EPUB Conversion Service and how it can empower your digital publishing journey.</p>
            </div>
        </div>
    </section>

    <section class="mod prep_contact">
        <div class="wrap" style="flex-direction: column">
            <div class="title_wrapper">
                <h1>Contact Us</h1>
                <p style="text-align: center">Any question? Just write us a message!</p>
            </div>
            <div class="wrapper animate__animated animate__fadeInLeft" style="box-shadow: 0 1px 5px; !important;background: transparent !important;">
                <div class="company-info">
                    <h3 class="infoCard" style="font-size: 35px  !important;">Contact Information</h3>
                    <p class="infoText">Fill up the form and our team will get back to you within 24 hours.</p>
                    <ul class="ulList">
                        <li><a href="mailto:<EMAIL>"><i class="fa fa-envelope"></i><EMAIL></a></li>
                        <li><a href="tel:+91-8088443860"><i class="fa fa-phone"></i>+91-8088443860</a></li>
                        <li><a href="https://wa.me/918088443860"><i class="fa-brands fa-square-whatsapp"></i>+91-8088443860</a></li>
                    </ul>
                </div>
                <div class="contact">
                    <form id="contactForm">
                        <p>
                            <label>Name</label>
                            <input type="text" name="name" id="uname" required>
                        </p>
                        <p>
                            <label>Email address</label>
                            <input type="email" name="email" id="qemail" required>
                        </p>
                        <p>
                            <label>Phone Number</label>
                            <input type="text" name="phone" id="phone" required>
                        </p>
                        <p>
                            <label>Company</label>
                            <input type="text" name="company" id="company">
                        </p>
                        <p class="full">
                            <label>Message</label>
                            <textarea name="message" rows="5" id="message"></textarea>
                        </p>
                        <p class="full">
                            <button type="submit" id="askQuery">Send Message</button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <section class="mod prep_footer" data-bgcolor="#263238" style="visibility: hidden">
        <div class="wrap">
            <div class="col copyright" data-scroll>
                <p>&copy; <span id="copyrightYear"></span> PrepCapsule - <a href="https://www.wonderslate.com/" target="_blank">Wonderslate</a></p>
            </div>
            <div class="col links" data-scroll>
                <p>Phone : <a href="tel:+91-8088443860">+91-8088443860</a></p>
                <p>Email : <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>
    </section>

</div>

<!--------  REPORT SUCCESS MODAL --------->
<div class="modal fade report-success__modal modal-modifier" id="report-success">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" onclick="window.location.reload()" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <h5 class="mt-3">Message Sent Successfully.</h5>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" onclick="window.location.reload()" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">OK</button>
                </div>
            </div>

        </div>
    </div>
</div>
%{--<g:render template="/books/footer_new"></g:render>--}%
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
<asset:javascript src="wonderslate/vendors.min.js"/>
<script>
    $(document).ready(function() {
        // DEFER IMAGE LOAD
        var imgDefer = document.getElementsByTagName('img');
        for (var i=0; i<imgDefer.length; i++) {
            if(imgDefer[i].getAttribute('data-src')) {
                imgDefer[i].setAttribute('src',imgDefer[i].getAttribute('data-src'));
            }
        }
        // STYLE INDIVIDUAL LINES & LETTERS
        $(".button").lettering('words');
        $(".animate-title, .animate-text").lettering('lines');
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.6.1/ScrollTrigger.min.js"></script>
<asset:javascript src="locomotive-scroll.min.js"/>
<script>

    // CURRENT YEAR
    var currentDate = new Date();
    $("#copyrightYear").html(currentDate.getFullYear());

    window.addEventListener("load", function () {
        gsap.registerPlugin(ScrollTrigger);

        const pageContainer = document.querySelector(".container");
        pageContainer.setAttribute("data-scroll-container", "");

        const scroller = new LocomotiveScroll({
            el: pageContainer,
            smooth: true,
            getSpeed: true,
            getDirection: true,
            repeat: false,
            smoothMobile: false,
            class: 'in-view',
            // lerp: .08
        });

        scroller.on("scroll", function (e) {
            document.documentElement.setAttribute("data-direction", e.direction);
        });

        scroller.on("scroll", ScrollTrigger.update);

        ScrollTrigger.scrollerProxy(pageContainer, {
            scrollTop(value) {
                return arguments.length ? scroller.scrollTo(value, 0, 0) : scroller.scroll.instance.scroll.y;
            },
            getBoundingClientRect() {
                return {
                    left: 0,
                    top: 0,
                    width: window.innerWidth,
                    height: window.innerHeight
                };
            },
            pinType: pageContainer.style.transform ? "transform" : "fixed"
        });

        if($(window).width() > 1080) {

            // HORIZONTAL SCROLLING
            let horizontalSections = document.querySelectorAll(".horizontal-scroll");

            horizontalSections.forEach(horizontalSection => {
                let pinWrap = horizontalSection.querySelector(".pin-wrap");
                let pinWrapWidth = pinWrap.offsetWidth;
                let horizontalScrollLength = pinWrapWidth - window.innerWidth;
                gsap.to(pinWrap, {
                    scrollTrigger: {
                        scroller: "[data-scroll-container]",
                        scrub: true,
                        trigger: horizontalSection,
                        pin: true,
                        start: "top top",
                        end: () => `+=${pinWrapWidth}`,
                        invalidateOnRefresh: true
                    },

                    x: -horizontalScrollLength,
                    ease: "none"
                });

            });
        }

        // COLOR CHANGER
        const scrollColorElems = document.querySelectorAll("[data-bgcolor]");
        scrollColorElems.forEach((colorSection, i) => {
            const prevBg = i === 0 ? "" : scrollColorElems[i - 1].dataset.bgcolor;
            const prevText = i === 0 ? "" : scrollColorElems[i - 1].dataset.textcolor;

            ScrollTrigger.create({
                trigger: colorSection,
                scroller: "[data-scroll-container]",
                start: "top 50%",
                onEnter: () => gsap.to("body", {
                    backgroundColor: colorSection.dataset.bgcolor,
                    color: colorSection.dataset.textcolor,
                    overwrite: "auto"
                }),

                onLeaveBack: () => gsap.to("body", {
                    backgroundColor: prevBg,
                    color: prevText,
                    overwrite: "auto"
                })
            });

        });

        ScrollTrigger.addEventListener("refresh", () => scroller.update());

        ScrollTrigger.refresh();
    });

    var askQuery = document.getElementById('contactForm');
    var uname = document.getElementById('uname');
    var email = document.getElementById('qemail');
    var mobile = document.getElementById('phone');
    var company = document.getElementById('company');
    var query = document.getElementById('message');

    askQuery.addEventListener('submit',function (e){
        e.preventDefault();

        email = email.value;
        mobile = mobile.value;
        company = company.value;
        query = query.value;
        uname = uname.value;
        <g:remoteFunction controller="creation" action="sendQuery" params="'name='+uname+'&email='+email+'&mobile='+mobile+'&company='+company+'&query='+query" onSuccess="querySent(data)" />
    })

    function querySent(data){
        $('#report-success').modal('show');
        document.getElementById('uname').value = ""
        document.getElementById('qemail').value = ""
        document.getElementById('phone').value = ""
        document.getElementById('company').value = ""
        document.getElementById('message').value = ""
    }
</script>