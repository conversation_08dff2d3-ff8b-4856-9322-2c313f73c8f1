<%
    String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String siteName = grailsApplication.config.grails.appServer.mainSiteName;
%>

<asset:stylesheet href="register.css"/>
<asset:stylesheet href="animatedInput.css"/>
<% if(g.cookie(name: 'siteName')!=null && g.cookie(name: 'siteName')=="sage") { %>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="registerModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content" id="registerModalContent">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close"> <span aria-hidden="true">&times;</span></button>
                </div>
                <div id="login" style="display: none">

                <form method="post" action="${resource(file: 'j_spring_security_check')}">

                <div class="row">
                <!--<div class="col-md-1"></div>-->
               <div class="col-md-12 vcenter">

                   <h3 class="omb_authTitle text-center">Login</h3><br>

              <!--     <div class="row">
                               <div class="row omb_row-sm-offset-3 omb_socialButtons">
                                   <div class="col-md-1"></div>
                                   <div class="col-md-5">
                                    <oauth:connect provider="facebook" id="facebook-connect-link" class="btn btn-lg btn-block omb_btn-facebook">
                                           <i class="fa fa-facebook"></i>
                                           <span class="fbText"><div class="verticalLine">Facebook</div></span>
                                       </oauth:connect><br>
                                   </div>

                                   <div class="col-md-5">
                                   <oauth:connect provider="google" id="google-connect-link" class="btn btn-lg btn-block omb_btn-google">
                                           <i class="fa fa-google-plus"></i>
                                           <span class="gText"><div class="verticalLine">Google+</div></span>
                                       </oauth:connect>
                                   </div>
                                   <div class="col-md-1"></div>
                               </div>

                           </div>

                   <h3 class="omb_authTitle text-center">OR</h3> -->


                   <br>
                   <div class="row" id="inputIcon">
                       <div class="col-md-1"></div>
                       <i class="fa fa-envelope" aria-hidden="true" style="float:left"></i>
                         <div class="col-md-10">

                           <div class="group">

                               <input class="inputMaterial" type="text" name="j_username" value="" required>
                                <span class="highlight"></span>
                                <span class="bar"></span>
                                <font>Username</font>
                           </div>
                       </div>
                       <div class="col-md-1"></div>
                   </div>
                       <br>
                   <div class="row" id="inputIcon">
                       <div class="col-md-1"></div>
                       <i class="fa fa-lock" aria-hidden="true" style="float:left; margin-right: 6px"></i>
                       <div class="col-md-10">


                           <div class="group">

                               <input class="inputMaterial" type="password" name="j_password" id="autoF" value="" required>
                               <span class="highlight"></span>
                               <span class="bar"></span>
                               <font>Password</font>
                           </div>
                       </div>
                       <div class="col-md-1"></div>
                   </div>
<!--                   <div class="alert alert-warning col-sm-12 text-left" style="display: none;">
                                ** Please complete required fields marked in red.
                            </div>-->

                   <div class="row">
                       <div class="col-md-2"></div>
                       <div class="col-md-8 red" id="loginFailed">Login failed. Please try again</div>
                   </div>

                   <div class="row">
                       <div class="col-md-2"></div>
                       <div class="col-md-4 remember">
                           <!--<label class="checkbox">-->
                               <input type="checkbox" name="_spring_security_remember_me">&nbsp;&nbsp;Remember Me
                           <!--</label>-->
                       </div>
                       <div class="col-md-4 forgot" style="float: right; margin-top: 16px"> <a href="javascript:forgotPassword();" >Forgot password?</a>
                       </div>
                       <div class="col-md-2"></div>
                   </div>
                   <br>
                   <div class="row">
                       <div class="col-md-1"></div>
                       <div class="col-md-10">
                           <button class="btn btn-lg btn-primary btn-block logIn" type="submit"><b>LOGIN</b></button>
                           <span class="help-block"></span>
                       </div>
                       <div class="col-md-1 vcenter"></div>
                   </div>



              <!--      <h3 class="omb_authTitle text-center account">Don't have an account? <a href="javascript:showregister('signup');">Signup now</a></h3>
                    <hr> -->
                    <div class="row">
                        <div class="col-md-12 text-center">By continuing, I agree to <a href="javascript:showTandC();" style="color: #00a68f">Terms & Conditions</a></div>
                   </div>

                   <div class="row" style="display:none;" id="tandc">
                       <div class="col-md-7">
                            <g:render template="/funlearn/tandc"></g:render>
                       </div>
                       <div class="col-md-10 col-md-offset-1" style="text-align:center;">
                            <button type="button" class="btn btn-primary TandChide">Hide T&C</button>
                       </div>


                    </div>

                    </div>

                    </div>

                </form>
                </div>

            <div id="signup" style="display: none">

               <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">

               <div class="row">
                 <div class="col-md-12 vcenter">

                   <h3 class="omb_authTitle text-center">Sign Up</h3><br>
                   <div class="row" style="display: none" id="signupbuy"><div class="col-md-11 col-md-offset-1 orange">Please register /login before you make the purchase.<br><br></div></div>
                   <div class="row" id="inputIcon">
                       <div class="col-md-1"></div>
                       <i class="fa fa-user" aria-hidden="true" style="float:left; margin-right: 3px"></i>
                       <div class="col-md-10">
                           <div class="group">
                               <input class="inputMaterial" type="text" name="name" id="name" required>
                                <span class="highlight"></span>
                               <span class="bar"></span>
                               <font class="labe">Name</font>
                           </div>
                       </div>
                       <div class="col-md-1"></div>
                   </div>

                   <br>

                   <div class="row" id="inputIcon">
                       <div class="col-md-1"></div>
                       <i class="fa fa-envelope" aria-hidden="true" style="float:left"></i>
                         <div class="col-md-10">
                           <div class="group">
                               <input class="inputMaterial" type="email" name="username" id="username" required>
                               <span class="highlight"></span>
                               <span class="bar"></span>
                               <font class="labe">Username (email address)</font>
                           </div>
                           <div class="red" style="display: none" id="emailerror">** Please enter valid email address</div>
                           <div class="red" style="display: none" id="emailexists">** This email is already registered.</div>
                       </div>
                       <div class="col-md-1"></div>
                    </div>

                    <br>

                    <div class="row" id="inputIcon">
                       <div class="col-md-1"></div>
                       <i class="fa fa-lock" aria-hidden="true" style="float:left; margin-right: 6px"></i>
                       <div class="col-md-10">
                           <div class="group">
                               <input class="inputMaterial" type="password" name="password" id="password" required>
                               <span class="highlight"></span>
                               <span class="bar"></span>
                               <font class="labe">Password</font>
                           </div>
                       </div>
                       <div class="col-md-1"></div>
                   </div>
                     <br>

                     <div class="row" id="inputIcon">
                         <div class="col-md-1"></div>
                         <i class="fa fa-mobile" aria-hidden="true" style="float:left; margin-right: 6px"></i>
                         <div class="col-md-10">
                             <div class="group">
                                 <input class="inputMaterial" type="text" name="mobile" id="mobile" required>
                                 <span class="highlight"></span>
                                 <span class="bar"></span>
                                 <font class="labe">Mobile</font>
                             </div>
                         </div>
                         <div class="col-md-1"></div>
                     </div>
                   <div class="alert alert-warning col-sm-12 text-left" style="display: none;">
                                <div class="col-md-2"></div>
                                <div class="col-md-8">
                                ** Please complete required fields marked in red.</div>
                   </div>
                   <br>

                   <div class="row">

                        <div class="col-md-12 text-center" style="margin-left: 25px">
                            <div class="roundedOne" style="display:inline-block; margin: 8px">
                                    <input type="checkbox" id="roundedOne" name="check" checked />
                                    <label for="roundedOne"></label>
                            </div>

                            By continuing, I agree to <a href="javascript:showTC();" style="color: #00a68f">Terms & Conditions</a> of Wonderslate
                        </div>
                   </div>
                   <br>

                   <div class="row">
                       <div class="col-md-1"></div>
                       <div class="col-md-10">
                           <button class="btn btn-lg btn-primary btn-block logIn" id="signup" type="button" onclick="javascript:formSubmit()"><b class="reg">SIGNUP</b></button>
                           <span class="help-block"></span>
                       </div>
                       <div class="col-md-1 vcenter"></div>
                   </div>

                   <h3 class="omb_authTitle text-center account">Have an account? <a href="javascript:showregister('login');">Login now</a></h3>

                   <div class="row" style="display: none" id="TC">
                       <div class="col-md-7">
                            <g:render template="/funlearn/tandc"></g:render>
                       </div>
                       <div class="col-md-10 col-md-offset-1" style="text-align:center;">
                            <button type="button" class="btn btn-primary TandChide">Hide T&C</button><br><br>
                       </div>
                   </div>
                 </div>
                </div>
                <input type="hidden" name="email">
             </g:form>
            </div>
            <div id="forgotPassword" style="display: none">
            <g:form name="forgotpassword" url="[action:'forgottenPassword',controller:'creation']"  method="post" autocomplete="off">
                <div class="row">
                   <div class="col-md-2"></div>
                   <div class="col-md-8"><h4>Forgotten password</h4></div>
                   </div>
                <div id="emailidnf" class="row" style="display: none">
                    <div class="col-md-2"></div>
                    <div class="col-md-8 red">This email is not registered. Please try again</div>
                </div>
               <div class="row">
                   <div class="col-md-2"></div>
                   <div class="col-md-8">
                       <div class="input-group">
                           <span class="input-group-addon"><i class="fa fa-user"></i></span>
                           <input type="text" class="form-control" name='email' id="email" placeholder="enter your email address">
                       </div>
                       <div class="red" style="display: none" id="foremailerror">** Please enter valid email address</div>
                       <span class="help-block"></span>
                   </div>
               </div>
               <div class="row">
                   <div class="col-md-4"></div>
                   <div class="col-md-4">
                       <button class="btn btn-lg btn-primary btn-block" type="button" onclick="javascript:formFPSubmit()">Submit <span id="loader" style="display: none"><i class="fa fa-spinner fa-spin"></i> </span></button>
                       <span class="help-block"></span>

                   </div>
                   <div class="col-md-4 vcenter"></div>
               </div>
                </g:form>
            </div>
            <div id="fpsuccess" style="display: none;">
                <div class="row">
                    <div class="col-md-10 col-md-offset-2" id="fpsuccessmsg"></div>
                </div>
            </div>



           </div>
        </div>
    </div>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>


    var flds = new Array (
            'name',
            'username',
            'password',
            'mobile'
    );
    function formSubmit() {
        if (validate()) {
            $("#signupbuy").hide();
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
            checkUsernameExists();
        }


    }

    function validate(){
        var allFilled=true
        $('.alert').hide();
        document.getElementById('emailerror').style.display = 'none';
        document.getElementById('emailexists').style.display = 'none';
        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).siblings('.bar').addClass('iColor');
                $("#"+flds[i]).siblings('.labe').addClass('labelAlert');
                allFilled = false;
            }
            else{
                $("#"+flds[i]).siblings('.bar').removeClass('iColor');
                $("#"+flds[i]).siblings('.labe').removeClass('labelAlert');
            }

        }
        if(!allFilled){
            $('.alert').show();

        }else{
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");
            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                document.getElementById('emailerror').style.display = 'block';
                return false;
            }
        }

        return allFilled;

    }

    function checkUsernameExists(){
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);'
                params="'username='+username" />
    }

    function userNameExistsResult(exists){
        "0"==exists?document.adduser.submit():(document.getElementById('emailexists').style.display = 'block');

    }


    function showTandC(){
    $("#tandc").toggle(600);
    };

    function showTC(){
    $("#TC").toggle(600);
    };

    $('.TandChide').click(function() {
        $('#tandc').toggle(600);
        $('#TC').toggle(600);
    });

    function forgotPassword(){
       $("#login").hide(1000);
       $("#forgotPassword").show(1000);
    }

    function formFPSubmit(){
        document.getElementById('foremailerror').style.display = 'none';
        $("#emailidnf").hide();
        if( !$("#email").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        }
        else{
            var email = $("#email").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");
            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                document.getElementById('foremailerror').style.display = 'block';
                return false;
            }else{
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
                params="'email='+email" />
            }
        }

    }

    function displayFPResults(data){
        $("#loader").hide(1000);
        if("Fail"==data.status) {
            $("#emailidnf").show(1000);
        }
        else{
            var htmlStr = "We have sent you an email at <b>"+data.email+"</b> with the instructions to reset your password.";
            document.getElementById("fpsuccessmsg").innerHTML = htmlStr;
            $("#forgotPassword").hide(1000);
            $("#fpsuccess").show(1000);
        }

    }



    $(window).load(function(){
		$(".col-3 input").val("");

		$(".input-effect input").focusout(function(){
			if($(this).val() != ""){
				$(this).addClass("has-content");
			}else{
				$(this).removeClass("has-content");
			}
		})
	});

    $('#signup').click(function () {
        if (!$('#roundedOne').is(':checked')) {
            alert('To continue you have to accept terms & conditions !');
            return false;
        }
    });


</script>

<% } %>