<g:render template="/funlearn/mainheader"></g:render>


<div class="section topsection" >
    <div class="container">
        <div class="row">
            <div class="col-md-1">

            </div>
            <div class="col-md-11">
                <h4>Content Approval</h4>
            </div>
        </div>


        <div id="approveItems">

        </div>

    </div>

</div>

<g:render template="/funlearn/footer"></g:render>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="moment.min.js"/>
<script>

    var approvalItemsList;

    function getItemsForApproval(){
        <g:remoteFunction controller="creation" action="getItemsForApproval"  onSuccess='initializeApprovalItems(data);'/>
    }

    getItemsForApproval();

    function initializeApprovalItems(data){
        var data1 = data.results;
        var length = data1.length;
        var itemStr="";
        for(var i =0; i < length; ++i) {
            var item = data1[i];
            itemStr+= "<div class='row'> <div class='col-md-1'> </div> <div class='col-md-2'>"+"<a href='approveContent.gsp?mode=approve&topicId="+item.topicId+"'>" +item.topicName +"</a>"+"</div> <div class='col-md-2'>"
                        +item.name
            +"</div> <div class='col-md-2'>"
            +moment(item.dateCreated).format("DD-MMM-YY")
            +"</div> <div class='col-md-2'>"
            +item.resType
             +"</div> <div class='col-md-3'> </div></div>";
            console.log(itemStr);



        }


        document.getElementById('approveItems').innerHTML = itemStr;

    }


</script>

</body>