
<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>${resourceType}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,400i,500,600,700|Work+Sans:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Exo:300,400" rel="stylesheet">

    <style>
    @media only screen and (max-width: 767px) {
        .main-wrapper {
            padding: 30px !important;
        }
        .main-wrapper table {
            width: 100% !important;
        }
        .download-apps {
            display: block !important;
        }
        .download-apps table.app-links td {
            padding-top: 0 !important;
        }
        .download-apps table td a {
            margin-bottom: 7px !important;
        }
        .download-apps table {
            width: 100% !important;
        }
        .download-apps table br {
            display: none !important;
        }
        .seperator {
            border-bottom: 1px solid #eeeeee !important;
        }
        .seperator img {
            display: none !important;
        }
    }
    </style>

</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())

%>

<body>

<div style="background: #f1f1f1; padding: 30px 15px;">
    <div class="main-wrapper" style="max-width:700px;margin:0 auto;font-family: 'Poppins', sans-serif;font-size: 16px;color: #000;background: #ffffff;padding: 40px;border-radius: 10px;">
        <div>
            <table style="width: 140px;margin-left: -5px;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/ws/wslogo_email.png" alt="wonderslate-logo" width="160" height="auto" style="width: 160px;height: auto;margin-right: 8px;">
                    </td>
                </tr>
            </table>
            <table style="width: 100%;margin-top: 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 18px; margin-bottom: 1.5rem;margin-top: 0;">
                            Hello <span style="color: #8B2BA3;font-weight: bold;">${name}!</span>
                        </p>
                        <p style="color: #8B2BA3;font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1.5rem;">
                            ${message}
                        </p>

                        <%if(resLink.equals("")){%>
                        <%}else{%>
                            <%if(resType=='Reference Web Link'){%>
                                <a href="${resLink}" target="_blank" style="font-family: 'Poppins', sans-serif; font-weight: normal;font-size: 14px;border: 1px solid #9B51E0;box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);border-radius: 5px;padding: 10px 25px;display: inline-block;color: #9B51E0;text-decoration: none;margin-bottom: 1rem;"
                                   onMouseOver="this.style.background='#9B51E0';this.style.color='#FFFFFF';"
                                   onMouseOut="this.style.background='#FFFFFF';this.style.color='#9B51E0'" >
                                    View Your ${resType}
                                </a>
                            <%}else{%>
                                <a href="${serverURL}${resLink}" target="_blank" style="font-family: 'Poppins', sans-serif; font-weight: normal;font-size: 14px;border: 1px solid #9B51E0;box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);border-radius: 5px;padding: 10px 25px;display: inline-block;color: #9B51E0;text-decoration: none;margin-bottom: 1rem;"
                                   onMouseOver="this.style.background='#9B51E0';this.style.color='#FFFFFF';"
                                   onMouseOut="this.style.background='#FFFFFF';this.style.color='#9B51E0'" >
                                    View Your ${resType}
                                </a>
                            <%}%>
                        <%}%>


                    </td>
                </tr>
            </table>
            <table class="seperator" style="width: 100%;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <img src="${serverURL}/assets/ws/horizontal-separator-line.png" width="100%" height="2" style="width: 100%;height: 2px;">
                    </td>
                </tr>
            </table>
            <table style="width: 100%;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 13px; margin-bottom: 1rem;">
                            ${clientName} enables you to experience our next-gen smart eBooks where you can read, practice and analyze all your curriculum books in smarter way.
                        </p>
                    </td>
                </tr>
            </table>
            <table class="seperator" style="width: 100%;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/ws/horizontal-separator-line.png" width="100%" height="2" style="width: 100%;height: 2px;">
                    </td>
                </tr>
            </table>
            <div class="download-apps" style="display: flex; align-items: center;">
                <table style="width: 50%;">
                    <tr>
                        <td style="width:100%;padding-top: 1rem;display: flex;">
                            <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 13px; margin-bottom: 1rem;">
                                Take your smart eBooks with you.</br>
                                Download ${clientName} App from Google Play Store now.
                            </p>
                        </td>
                    </tr>
                </table>
                <table class="app-links" style="width: 50%;">
                    <tr>
                        <td style="width:50%;text-align: center; padding-top: 1.5rem;">
                            <a href="https://apps.apple.com/in/app/wonderslate/id1438381878" target="_blank">
                                <img src="${serverURL}/assets/ws/icon-mail-appstore.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                            <a href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">
                                <img src="${serverURL}/assets/ws/icon-mail-playstore.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
            <table style="width: 80%;margin: 0.5rem auto 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; text-align: center; line-height: 16px; font-size: 10px; margin-top: 1rem; margin-bottom: 0;">
                            This email has been sent to you by <%=(grailsApplication.config.grails.appServer.siteName=="e-Utkarsh")?"e-Utkarsh Innovations Private Limited, Jodhpur, Rajasthan":"Wonderslate Technologies Pvt Ltd. Wonderslate Technologies Pvt Ltd, No. 107, NGEF Layout, 4th Main Road, Sanjayanagar, Bangalore KA 560094, INDIA"%>.
                        </p>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
</body>
</html>