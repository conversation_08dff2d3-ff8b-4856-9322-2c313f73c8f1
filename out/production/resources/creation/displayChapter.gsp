<%--
  Created by IntelliJ IDEA.
  User: Anand
  Date: 25-Sep-15
  Time: 11:37 AM
--%>

<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wonderslate</title>
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="${request.contextPath}/css/bootstrap.min.css">
    <link rel="stylesheet" href="${request.contextPath}/css/style.css">
</head>
<body>
<nav class="navbar navbar-default navbar-fixed-top">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <img class="logo-img" src="${request.contextPath}/images/logo.png">
            </div>
        </div>
    </div>
</nav>


<g:if test="${chapterDetails!=null}">
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="row ">
                <div class="col-md-12">
                    Details
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th>Type</th>
                    <th>Name</th>
                    <th>Created By</th>
                    <th>Link</th>
                    <th>UseType</th>
                </tr>
                </thead>
                <% String fileLink =""; %>
                <g:each in="${chapterDetails}" var="p" status="i">
                    <%  println "p[4] is "+p[4];
                        if('file'.equals(p[4])) {
                            println "entered if"
                            fileLink = request.contextPath+"/creation/download?id="+p[5];
                            println "file link="+fileLink
                        }%>
                    <tr>
                        <td>${p[0]}</td>
                        <td>${p[1]}</td>
                        <td>${p[2]}</td>
                        <g:if test = "${'file'.equals(p[4])}">
                            <td><a href="${fileLink}">Download File</a></td>
                        </g:if>
                        <g:else>
                            <td>${p[3]}</td>
                        </g:else>
                        <td>${p[3]}</td>
                        <td>${p[4]}</td>

                    </tr>
                </g:each>
            </tbody>
            </table>
        </div>
    </div>
</g:if>


<g:javascript src="jquery-1.11.2.min.js"/>
<g:javascript src="bootstrap.min.js"/>
</body>
</html>