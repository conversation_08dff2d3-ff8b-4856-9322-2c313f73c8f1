
    <button type="button" class="btn btn-primary create-btn" data-toggle="modal" data-target="#myModal">
        Add Class <i class="material-icons">add</i>
    </button>

    <!-- The Modal -->
    <div class="modal fade" id="myModal" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">

                    <form action="" class="needs-validation" novalidate>
                        <div class="form-group">
                            <input type="text" class="form-control" id="classname" placeholder="Enter Class Name" name="classname" required>
                            <div class="valid-feedback">Valid.</div>
                            <div class="invalid-feedback">Please fill out this field.</div>
                            <br>
                            <select class="form-control" id="section">
                                <option value="">Select section</option>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                                <option value="D">D</option>
                                <option value="E">E</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>


                </div>



            </div>

        </div>

    </div>

        <table class="table mt-3 display responsive nowrap" id="classTable">
            <thead class="">
            <tr class="head">

                <th>Class Name</th>
                <th>Section</th>
                <th>Edit</th>
                <th>Delete</th>
                <th>Class Name</th>
            </tr>
            </thead>
            <tbody>


            </tbody>
        </table>


