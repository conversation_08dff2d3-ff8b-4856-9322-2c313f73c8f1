<button type="button" class="btn btn-primary create-btn" data-toggle="modal" data-target="#addstudent">
    Add Students <i class="material-icons">add</i>
</button>

<!-- The Modal -->
<div class="modal fade" id="addstudent" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title"></h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">

                <form action="" class="needs-validation" novalidate>
                    <div class="form-group">
                        <select class="form-control" id="section">
                            <option value="">Select Class</option>
                            <option value="Class1">Class 1</option>
                            <option value="Class2">Class 2</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <select class="form-control" id="section1">
                            <option value="">Select section</option>
                            <option value="A">A</option>
                            <option value="B">B</option>
                            <option value="C">C</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <select class="form-control" id="teacher">
                            <option value="">Select Teacher</option>
                            <option value="A">Teacher1</option>
                            <option value="B">Raj</option>
                            <option value="C">krish</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" id="studentname" placeholder="Enter Name" name="studentname" required>
                    </div>

                    <button type="submit" class="btn btn-primary">Submit</button>
                </form>

            </div>
        </div>
    </div>
</div>


    <table class="table mt-3 display responsive nowrap" id="studentsTable">
        <thead class="">
        <tr class="head">
            <th>Class</th>
            <th>Section</th>
            <th>Teachers</th>
            <th>Student</th>
            <th>Edit</th>
            <th>Delete</th>
        </tr>
        </thead>
        <tbody>


        </tbody>
    </table>

