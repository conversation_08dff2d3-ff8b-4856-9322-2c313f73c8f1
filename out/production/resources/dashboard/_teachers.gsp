<button type="button" class="btn btn-primary create-btn" data-toggle="modal" data-target="#addteacher">
    Add Teachers<i class="material-icons">add</i>
</button>

<!-- The Modal -->
<div class="modal fade" id="addteacher" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title"></h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">

                <form action="/action_page.php" class="needs-validation" novalidate>
                    <div class="form-group">
                        <select class="form-control" id="getClassName" onchange="javascript:getSection(this);">
                            <option value="">Select Class</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <select class="form-control" id="section">
                            <option value="">Select section</option>
                            <option value="A">A</option>
                            <option value="B">B</option>
                            <option value="C">C</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" id="teachername" placeholder="Enter Name" name="teachername" required>
                        <div class="valid-feedback">Valid.</div>
                        <div class="invalid-feedback">Please fill out this field.</div>
                    </div>

                    <button type="submit" class="btn btn-primary">Submit</button>
                </form>

            </div>
        </div>
    </div>
</div>
<div class="mt-3">
    <table class="table mt-3 display responsive nowrap" id="teachersTable">
        <thead class="">
        <tr class="head">
            <th>Class</th>
            <th>Section</th>
            <th>Teachers</th>
            <th>Edit</th>
            <th>Delete</th>
        </tr>
        </thead>
    </table>
</div>
