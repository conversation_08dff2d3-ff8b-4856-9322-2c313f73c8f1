<%@ page import="com.wonderslate.institute.InstituteIpAddress; com.wonderslate.data.UtilService; com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"SAGE | e-Bouquet"%></title>
    <meta name="description" content="SAGE | e-Bouquet">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'etexts/favicon.ico')}" type="image/x-icon">

    <meta name="theme-color" content="#1E4598" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp&display=swap" async>
%{--    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">--}%
    <asset:stylesheet href="landingpage/sageEvidya.css"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
    <asset:stylesheet href="landingpage/sageEbouquet.css"/>

    <style>
    .btco-hover-menu .show > .dropdown-toggle::after{
        transform: rotate(-90deg);
    }
    .btco-hover-menu ul li {
        position:relative;
        border-left: 1px solid white;
    }
    .btco-hover-menu ul li.dropdown:last-child {
        border-left: none;
    }
    .btco-hover-menu ul li:first-child {
        border-left: none;
    }
    .btco-hover-menu ul ul li {
        position:relative;
    }
    .btco-hover-menu ul ul li:hover> ul {
        display:block;
    }
    .btco-hover-menu ul ul ul {
        position:absolute;
        top:0;
        left:-100% !important;
        min-width:220px;
        display:none;
    }
    .btco-hover-menu ul li.login_button {
        border-right: none;
    }

    </style>

</head>
<%session['siteId'] = new Integer(24);%>


<body class="eutkarsh evidya etexts ebouquet" data-spy="scroll" data-target=".ws-header" data-offset="50">

<sec:ifNotLoggedIn>
%{--<g:render template="/funlearn/signupNew"></g:render>--}%
</sec:ifNotLoggedIn>

<header class="sageEvidya">
    <%
        if(params.tokenId==null){%>
    <div class="ebouquet-logo text-center p-3 pt-5">
        <a href="/ebouquet/index"><span class="logo"><img src="${assetPath(src: 'ebouquet/logo.png')}" alt="SAGE e-Bouquet"></span></a>
    </div>
    <div class="ws-menu-start">
        <nav class="ws-header container-fluid navbar navbar-expand-md navbar-default btco-hover-menu">
            <div class="d-none mobile-profile justify-content-center">
                <%if(session["userdetails"]==null){%>
                <li class="nav-item">
                    <a class="nav-link login" id="evidyaLogin1">LOGIN & SIGN UP</a>
                </li>
                <%}else{%>

                <li class="nav-item">
                    <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                        <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="rounded-circle bg-white">
                        <%}%>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <div class="media p-3">
                            <a href="/creation/userProfile">
                                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="mr-3 rounded-circle drop-profile bg-white">
                                <%}%>
                                <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                            </a>
                            <div class="media-body">
                                <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                            </div>
                        </div>
                        <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                        %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                        <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                    </div>
                </li>
                <%}%>
                <li class="nav-item right">
                    <div id="toggle" class="right">
                        <span></span><span></span><span></span><span></span>
                    </div>
                    <div id="menu">
                        <ul id="menu-left-menu" class="menu">
                            <li><a href="/ebouquet/store">DISCOVER</a></li>
%{--                            <li><a href="/ebouquet/howItWorks">HOW IT WORKS</a></li>--}%
                            <li><a href="/ebouquet/requestDemo">REQUEST A DEMO</a></li>
                            <li><a href="/ebouquet/contact">SUPPORT</a></li>
                            <% if(session['userdetails']!=null && (showLibrary)){%>
                            <li><a href="/library">MY <span style="font-size: 28px;">e-bouquet</span></a>
                            <%}%>
                            </li>
                        </ul>
                    </div>
                </li>
            </div>

            <ul class="navbar-nav right-menu d-flex d-md-flex align-items-center mx-auto">

                <li class="nav-item">
                    <a class="nav-link estore" href="/ebouquet/store">DISCOVER</a>
                </li>

%{--                <li class="nav-item">--}%
%{--                    <a class="nav-link" href="/ebouquet/howItWorks">HOW IT WORKS</a>--}%
%{--                </li>--}%
                <li class="nav-item">
                    <a class="nav-link" href="/ebouquet/requestDemo">REQUEST A DEMO</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/ebouquet/contact">SUPPORT</a>
                </li>
                <% if(session['userdetails']!=null && (showLibrary)){%>
                <li class="nav-item">
                    <a class="nav-link" href="/library">MY <span style="font-size: 19px;">e-bouquet</span></a>
                </li>
                <%}%>
                <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                                PUBLISHING
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/publishing-desk">PUBLISHING DESK</a>
                                <a class="dropdown-item" href="/wonderpublish/manageTabs">MANAGE TABS</a>
                                <a class="dropdown-item" href="/wonderpublish/manageExams">MANAGE EXAMS</a>

                            </div>
                        </li>
                    </sec:ifAllGranted>
%{--                    <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">--}%
%{--                        <li class="nav-item dropdown active">--}%
%{--                            <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">--}%
%{--                                DIGITAL MARKETING--}%
%{--                            </a>--}%
%{--                            <div class="dropdown-menu">--}%
%{--                                <a class="dropdown-item" href="/header/index">HEADER MANAGEMENT</a>--}%
%{--                                <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">PRINT BOOKS</a>--}%
%{--                            </div>--}%
%{--                        </li>--}%
%{--                    </sec:ifAllGranted>--}%
%{--                    <sec:ifAllGranted roles="ROLE_FINANCE">--}%
%{--                        <li class="nav-item">--}%
%{--                            <a href="/publishing-sales" class="nav-link">SALES</a>--}%
%{--                        </li>--}%
%{--                    </sec:ifAllGranted>--}%
                    <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_MASTER_LIBRARY_ADMIN,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                                ADMIN
                            </a>
                            <div class="dropdown-menu">
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/managePublishers">PUBLISHER MANAGEMENT</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                    <a class="dropdown-item" href="/institute/admin">INSTITUTE MANAGEMENT</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/libAdmin">ORGANIZATION MANAGEMENT</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/userManagement">USER MANAGEMENT</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                                    <a class="dropdown-item" href="/wonderpublish/wseditor">WS EDITOR</a>
                                </sec:ifAllGranted>
                            </div>
                        </li>
                    </sec:ifAnyGranted>
                    <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN,ROLE_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad1" data-toggle="dropdown">
                                REPORT
                            </a>
                            <ul class="dropdown-menu">

                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li class="border-0"><a class="dropdown-item dropdown-toggle">USAGE REPORT</a>
                                        <ul class="dropdown-menu" style="min-width: 255px;margin-top: 0;">
                                            <li class="border-0"><a class="dropdown-item border-bottom" href="/institute/usageReport">ORGANIZATION REPORT</a></li>
                                            <li><a class="dropdown-item" href="/institute/registeredUserReport?report=usagebookview">BOOKS REPORT</a></li>
                                        </ul>
                                    </li>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item" href="/institute/registeredUserReport?report=login">USERS REPORT</a></li>
                                </sec:ifAllGranted>
                                <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN,ROLE_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item" href="/institute/usageReportCorporateWise">USAGE REPORT ORGANIZATION WISE</a></li>
                                </sec:ifAnyGranted>
                                <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN,ROLE_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item" href="/institute/usageReportTitleWise">USAGE REPORT TITLE WISE</a></li>
                                </sec:ifAnyGranted>

                            </ul>
                        </li>
                    </sec:ifAnyGranted>

                </sec:ifLoggedIn>

                <sec:ifNotLoggedIn>

                    <li class="nav-item login_button">
                        <a class="nav-link" id="evidyaLogin">SIGN IN</a>
                    </li>
                    <div class="evidyaLogin">
                        <div class="evidyaloginWrapper" >
                            <p>Log-in to access your personalized dashboard for additional features. </p>
                            <form method="post" action="/login/authenticate" name="signin">
                                <input type="hidden" name="username">
                                <label>Email</label>
                                <input type="text" class="form-control" id="email" name="username_temp" required>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="password" name="password" required>
                                <a href="javascript:showForgetPassword();" class="mt-4 frgtPassword">Forgot Password?</a>

                                <input type="button" id="sign-in" class="btn btn-login" value="Log In" onclick="submitSignIn()">
                            </form>
                            <div class="access-code-login text-center mt-3">
                                <a href="javascript:accessCodeLogin();">Do you have an access code? Login here</a>
                            </div>
                            <div id="loginFailedEvidya" style="display:none; color: #F05A2A; margin-top: 15px;">Oops! It seems that the email id or the password entered is incorrect. Please recheck and try again.</div>
                            %{--<button class="btn btn-create" onclick="javascript:createAccount();">Create a new account</button>--}%

                        </div>
                        %{--<div class="evidyaSignup" style="display: none;">
                            <h3 class="text-center">Sign Up </h3>
                            <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
                                <label>Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <label class="mt-2">Phone Number</label>
                                <input class="form-control" type="text" name="mobile" id="mobile" required maxlength="10" minlength="10">
                                <div class="input-error-tooltip" style="display: none;">
                                    <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
                                </div>
                                <label class="mt-2">Email</label>
                                <input class="form-control" type="email" id="username" name="username" required>
                                <div class="email-error" id="emailexists" style="display: none;">
                                    <p class="email-error-text">This email is already taken. Please sign in.</p>
                                </div>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="signup-password" name="password" required>
                                <label class="mt-2">State</label>
                                <select class="form-control" name="state" id="state" required>
                                    <option value="">Select</option>
                                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                    <option value="Assam">Assam</option>
                                    <option value="Bihar">Bihar</option>
                                    <option value="Chandigarh">Chandigarh</option>
                                    <option value="Chhattisgarh">Chhattisgarh</option>
                                    <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                    <option value="Goa">Goa</option>
                                    <option value="Gujarat">Gujarat</option>
                                    <option value="Haryana">Haryana</option>
                                    <option value="Himachal">Himachal Pradesh</option>
                                    <option value="Jammu & Kashmir">Jammu & Kashmir</option>
                                    <option value="Jharkhand">Jharkhand</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Kerala">Kerala</option>
                                    <option value="Lakshadweep">Lakshadweep</option>
                                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Manipur">Manipur</option>
                                    <option value="Meghalaya">Meghalaya</option>
                                    <option value="Mizoram">Mizoram</option>
                                    <option value="Nagaland">Nagaland</option>
                                    <option value="Odisha">Odisha</option>
                                    <option value="Puducherry">Puducherry</option>
                                    <option value="Punjab">Punjab</option>
                                    <option value="Rajasthan">Rajasthan</option>
                                    <option value="Sikkim">Sikkim</option>
                                    <option value="Tamil Nadu">Tamil Nadu</option>
                                    <option value="Telangana">Telangana</option>
                                    <option value="The Government of NCT of Delhi">The Government of NCT of Delhi</option>
                                    <option value="Tripura">Tripura</option>
                                    <option value="Uttarakhand">Uttarakhand</option>
                                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                                    <option value="West Bengal">West Bengal</option>
                                </select>
                                <input type="hidden" name="email">
                                <input type="hidden" name="otp_finished" id="otp_finished">
                                <input type="button" id="signup" onclick="javascript:formSubmit();" class="btn btn-lg continue mt-4" value="Continue">
                                <a onclick="javascript:loginContinue();" class="mt-4 loginContinue">Already have an Account?Click here to login</a>
                            </g:form>
                        </div>--}%
                        <div class="forgotPassword" style="display: none;">
                            <h3>Reset Password</h3>
                            <p class="mt-4">To recieve a link to reset your password, please enter your account email address.</p>
                            <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                                <label class="mt-2">Email</label>
                                <input class="form-control"  type="text" name="username" id="fPemail" required>
                                <span id="showEmailerror" style="color:red;
                                display: block;
                                margin: 7px;"></span>
                                <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset mt-2" value="Send reset link">
                                <a id="back-login" class="btn btn-back mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </g:form>
                        </div>
                        <div id="reset-password-completed" style="display: none;">
                            <div class="text-center">
                                <p>Password reset link sent.</p>
                                <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from e-bouquet within a couple of minutes, please check your spam folder.</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="reset-google-paswd" style="display: none">
                            <div class="text-center">
                                <p>Password reset not possible!</p>
                                <p>We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="account-exists" style="display: none">
                            <div class="text-center" style="margin-top: 6rem;">
                                <p class="notRegister">This Email is not registered.<br> Please signup!</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackSingup();">Back to Signup</a>
                            </div>
                        </div>
                    </div>

                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <li class="nav-item notification">
                        <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link p-0" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="rounded-circle bg-white">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'ebouquet/user-icon.png')}" alt="" class="mr-3 rounded-circle drop-profile bg-white">
                                    <%}%>
                                    <span class="edit-btn"><i class="material-icons">edit</i></span>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </ul>
        </nav>

    </div>
    <%}%>
</header>



<% if (!hideBottomIcons) { %>
<ul class="mobile-bottom-menu-wrapper hidden-md hidden-sm hidden-lg">
    <sec:ifNotLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="estore mobile-bottom-menu-item-link mobile-store">Discover</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="javascript:showSignUpModal();" class="mobile-bottom-menu-item-link mobile-library">MY BOUQUET</a>
        </li>

    </sec:ifNotLoggedIn>

    <sec:ifLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="mobile-bottom-menu-item-link mobile-store">Discover</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="/library" class="mobile-bottom-menu-item-link mobile-library">MY BOUQUET</a>
            Library
        </li>

    </sec:ifLoggedIn>
</ul>
<% } %>
<div class="modal" id="signInSuccess">
    <div class="modal-dialog  modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">


            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="color:#233982;font-size: 16px;">Signed in Successfully</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                %{--                <button type="button" class="btn btn-danger" data-dismiss="modal">Continue</button>--}%
            </div>

        </div>
    </div>
</div>

<g:render template="/ebouquet/accessCodeLogin"></g:render>


<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";


    function createAccount(){
        $('.evidyaloginWrapper').hide();
        $('.evidyaSignup').show();
    }
    function loginContinue() {
        $('.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
    }
</script>

<script src="https://apis.google.com/js/api:client.js"></script>
<script>
    var otpReg = "${otpReg}";

    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function formSubmit() {
        if (validateSignUp()) {
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
            checkUsernameExists();
        }
    }

    function validateSignUp(){
        var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';

        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                // $("#"+flds[i]).css('border', 'none');

            }
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }
        }

        return allFilled;
    }

    function checkUsernameExists() {
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            $('#connecting-div').show();

            if(otpReg=="true") {
                $('#loginSignup').modal('hide');
                $('#otp-mobile').val($('#mobile').val());
                $('#otp-email-txt').text($('#username').val());
                $('#otp-email-txt1').text($('#username').val());
                $('#otp-mobile1').text($('#otp-mobile').val());
                $('#otp-next').modal('show');
            } else {
                document.adduser.submit();
            }

            $('#sign-up-div').hide();
        } else {
            $('#loginSignup').modal('show');
            document.getElementById('emailexists').style.display = 'block';
        }
    }

    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {

                if(siteId==23){
                    document.getElementById('showEmailerror').innerHTML='Please enter valid Email address';
                }
                else {
                    return false;
                }
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            // $('#loginSignup').modal('show');
            // $('#loginSignup').attr('data-page', 'reset-completed');
            $('.forgotPassword').hide();
            $('#reset-password-completed').show();
            $('#fp-user-email').html("“"+userEmail+"”");
        } else if("Google"==data.status) {
            $('.forgotPassword').hide();
            $('#reset-google-paswd').hide();
            $('#fp-user-email1').html("“"+userEmail+"”");
        }
        else if("Fail"==data.status){
            $('.forgotPassword').hide();
            $('#account-exists').show();

        }
    }

    <%  if(params.loginFailed=="true"&&!"24".equals(""+session["siteId"])) {%>
    $(window).on('load',function() {
        $('#loginSignup').modal('show');
    });
    <%  } %>
    function showForgetPassword() {
        $('.evidyaloginWrapper').hide();
        $('.forgotPassword').show();
    }
    function evidyabackSingup(){
        $('#account-exists').hide();
        $('.evidyaSignup').show();

    }

    function logoutClicked() {
        document.cookie = '';
        document.cookie = 'currentUrl' + "=" + window.location.href + "; path=/";
        // console.log(document.cookie);
        window.location.href ="/logoff";
    }


    function submitSignIn(){
        document.signin.username.value= "${session["siteId"]}_"+document.signin.username_temp.value;
        document.signin.submit();
        localStorage.setItem('loginClicked','1');
    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };

    function updateLogin(data){
        if(data.status=="Success"){
            document.signin.username.value= "${session["siteId"]}_"+document.getElementById("emailAddress1").value
            document.signin.password.value=document.getElementById("newUpdatePassword").value;
            document.signin.submit();

        }
        else{
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>This Email ID is already taken. Please sign in.</small>";
        }
    }

</script>

