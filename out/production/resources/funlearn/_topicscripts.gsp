
<g:render template="/funlearn/mcq"></g:render>
<g:render template="/funlearn/fib"></g:render>
<g:render template="/funlearn/opp"></g:render>
<g:render template="/funlearn/tof"></g:render>
<g:render template="/funlearn/mta"></g:render>
<asset:javascript src="quiz.js"/>
<asset:javascript src="topic.js"/>

<script>
var serverPath= "${request.contextPath}";
var topicId = "${topicId}";
var syllabusType="${syllabusType}";
var country ="${country}";
var approveMode=false;
<% if("true".equals(params.approveMode)){%>
  approveMode=true;
  <% } %>
var firstTime=true; //this is required for google analytics
  function getTopicDetails(topicId,mode){
      if(firstTime) {
          console.log("I am called only once");
          firstTime=false;
      }
      else{
          console.log("didnt i told you about second time");
      }
      <% if("true".equals(params.approveMode)){%>
      <g:remoteFunction controller="creation" action="approvalChapterDetails" params="'topicId='+topicId" onSuccess = "initializeTopicData(data);"/>
      <%}else{%>
        <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+topicId+'&mode='+mode+'&fromWeb=true'" onSuccess = "initializeTopicData(data);"/>
        <%}%>
        $('.display-area .content').hide();
        if("bookauthor"!=mode) {
            document.getElementById('videoDiv').innerHTML = "";
            document.getElementById("content-data-quiz").innerHTML = "";
            document.getElementById("content-data-relvideos").innerHTML = "";
            document.getElementById("content-data-weblinks").innerHTML = "";
            document.getElementById("content-data-QA").innerHTML = "";
            document.getElementById("content-data-shortQAndA").innerHTML = "";
        }
      }

  // quiz related functions
  function getQuestions(quizId){
    <g:remoteFunction controller="funlearn" action="quizQuestions" params="'quizId='+quizId"
    onSuccess = 'initializeQuestions(data);'/>
  }
  function getMTAQuestions(quizId){
    <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+quizId"
    onSuccess = 'initializeQuestions(data);'/>
  }
  function firstTwoQuizQuestions(quizLink,quizId){
    <g:remoteFunction controller="funlearn" action="firstTwoQuizQuestions" params="'quizLink='+quizLink+'&quizId='+quizId"
    onSuccess = 'initializeFirstTwoQuizQuestions(data);'/>
  }

  function getAnswers(quizId){
    <g:remoteFunction controller="funlearn" action="quizAnswers" params="'quizId='+quizId"
    onSuccess = "scoreAndShowAnswers(data);"/>
  }

  function getQuestionAnswers(quizId){

             <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+quizId"
    onSuccess = "scoreAndShowAnswers(data);"/>

  }



  var serverPath= "${request.contextPath}";

  function submitAlias(){
    var flds = new Array('alias');
    if(validate(flds)){
      <g:remoteFunction controller="creation" action="checkTopicNameExists" onSuccess = 'checkAliasNameExists(data);'
      params="'subject='+document.aliasForm.subject.value+'&syllabus='+document.aliasForm.syllabus.value+'&grade='+document.aliasForm.grade.value+'&topic='+document.aliasForm.alias.value"/>
    }
  }



  $(".lmodal").show();

  $(window).load(function(){
    $(".lmodal").hide();
  });

  function createResource(resourceType,useType){
    if(resourceType=='Notes')
      window.open(serverPath+"/funlearn/notescreator?topicId=${topicId}"+"&resourceType="+resourceType+"&useType="+useType+"&mode=create","_self");
    else if (resourceType=='Multiple Choice Questions')
      window.open(serverPath+"/funlearn/quizcreator?topicId=${topicId}"+"&resourceType="+resourceType+"&useType="+useType+"&mode=create","_self");
  }

  function showNotes(notesId) {
    <g:remoteFunction controller="funlearn" action="getNotes" onSuccess='displayNotes(data);' params="'notesId='+notesId+'&syllabusType='+syllabusType+'&country='+country"/>
  }

  function downloadNotes(notesId){
    window.open(serverPath+'/funlearn/notesToPDF?notesId='+notesId+'&syllabusType='+syllabusType+'&country='+country,"_self");
  }



        function sharewithgroups(id){
          $("#groupResourceModal").modal('show');
          <g:remoteFunction controller="friends" action="getGroupsForSharing" onSuccess='displayGroupsForSharing(data);' params="'id='+id"/>
        }

        function addGroupShare(groupId,resourceId){

          <g:remoteFunction controller="friends" action="addGroupShare"  onSuccess='sharedWithGroup(data);' params="'groupId='+groupId+'&resourceId='+resourceId"
          />
        }

        function sharedWithGroup(data){
          document.getElementById("groupoption"+data.groupId).innerHTML = "";
          document.getElementById("groupoption"+data.groupId).innerHTML = "<i class='fa fa-check fa-x'></i>";

        }
        function publish(id){
          <g:remoteFunction controller="creation" action="publish" onSuccess='sentForApproval(data);' params="'id='+id"/>
        }

        function  updateWithQuizAnswers(userAnswers,score,quizgenerated){
          <sec:ifLoggedIn>
          var params;
          if("true"==quizgenerated) params = "noOfQuestions="+userAnswers.length+"&timetaken="+(endTime-startTime)+"&testgenid="+testGenId;
          else params = "noOfQuestions="+userAnswers.length+"&quizid="+currentQuizId+"&timetaken="+(endTime-startTime);
          for (i = 0; i < userAnswers.length; i++) {
            params += "&id"+i+"="+userAnswers[i]['id'];
            params += "&option1"+i+"="+userAnswers[i].ans1;
            params += "&option2"+i+"="+userAnswers[i].ans2;
            params += "&option3"+i+"="+userAnswers[i].ans3;
            params += "&option4"+i+"="+userAnswers[i].ans4;
            params += "&option5"+i+"="+userAnswers[i].ans5;

            if(userAnswers[i].skipped=="true")  params += "&correctAnswer"+i+"=skipped";
            else {
                params += "&correctAnswer"+i+"="+userAnswers[i].correctAnswer;
            }
          }
          params += "&correctAnswers="+score.correctAnswers;
          params += "&wrongAnswers="+score.wrongAnswers;
          params += "&skipped="+score.skipped;
          params +="&userAnswers="+JSON.stringify(userAnswers);
          totalQuestions = userAnswers.length;
          noOfRightAnswers = score.correctAnswers;

            if(flashQuiz){
                params += "&quizType="+quiz.type;
                <g:remoteFunction controller="funlearn" action="keyValueRecorder" params="params" onSuccess="getUserAnalyticsForStudySets()"></g:remoteFunction>

            }else {
                <g:remoteFunction controller="funlearn" action="updateWithQuizAnswers" params="params" onSuccess="quizRecordsUpdated()"></g:remoteFunction>
            }
          </sec:ifLoggedIn>
        }

        function quizRecordsUpdated(){
            var htmlStr="";
            if(assignmentQuiz){
                htmlStr ="      <p class=\"assignment-done\">\n" +
                    "              <i class=\"material-icons\">check_circle</i>\n" +
                    "              Completed            </p>\n" +
                    "<div class=\"button-wrapper\">          <p>\n" +
                    noOfRightAnswers+" correct answers out of "+totalQuestions+"</p></div>" ;
                document.getElementById("testIdDone"+testGenId).innerHTML=htmlStr;
            }
        }


function backToNotesMain(){
          $("#notesnavigationsection").hide();
          $("#notesnavigation").hide();
          $("#displayreadingcontent").hide();
          $("#content-notes").show();
        }



function getFlashQuiz(id,quizType){
    if("Multiple Choice Questions"==quizType) {
        <g:remoteFunction controller="funlearn" action="getFlashCardAsMCQQuestions" params="'resId='+id"
        onSuccess = "scoreAndShowAnswers(data);"/>
    }
    else if("True or False"==quizType) {
        <g:remoteFunction controller="funlearn" action="getFlashCardAsTFQuestions" params="'resId='+id"
        onSuccess = "scoreAndShowAnswers(data);"/>
    }

}

function contentLoaded(){
}

function closeHtmls(){
    $("#displayreadingcontent").hide(500);
    $("#content-notes").show(500);
}



  var faqId=-1;
  function showfaq(id){
    var downblue="<i class='fa fa-angle-down fa-x'></i>";
    var rightgrey="<i class='fa fa-angle-right fa-x greytext'></i>";
    if(id==faqId) {
      $('#faq'+faqId).toggle(500);
      document.getElementById('fa'+id).innerHTML=rightgrey;
      faqId=-1;
    }
    else {
      if(faqId!=-1) {
        $('#faq' + faqId).toggle(500);
        document.getElementById('fa'+faqId).innerHTML=rightgrey;
      }
      faqId=id;
      document.getElementById('fa'+faqId).innerHTML=downblue;
      $('#faq' + faqId).toggle(500);
    }
  }
  </script>
  <% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
  <asset:javascript src="analytics.js"/>
  <% }%>
  <g:if test="${flash.error}">
  <div class="alert alert-error" style="display: block">${flash.error}</div>
</g:if>
<g:if test="${flash.message}">
<div class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" id="statusWindow" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <b><p>Upload status</p></b>
        <br>
        <div class="greytext" style="display: block" id="flashMessage" name="flashMessage"></div>
      </div>
      <div class="modal-footer">
        <div class="row text-center">
          <button type="button" data-dismiss="modal" class="btn btn-primary">Close</button>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
document.getElementById("flashMessage").innerHTML=htmlDecode("${flash.message}")
$("#statusWindow").modal("show");

</script>
</g:if>


<sec:ifLoggedIn>

<script>






function getVideoPolls(resId){
    <g:remoteFunction controller="funlearn" action="getVideoPolls" onSuccess='displayStudySetAnalytics(data);' params="'resId='+resId"/>

}

function addPolls(data){
    var htmlStr = document.getElementById("hostedvideocontainer").innerHTML;
    var videoPolls = data.videoPolls;
    if(videoPolls.length>0){
       pauseTime = videoPolls[0].startingTime;
       console.log("starting time is="+videoPolls[0].startingTime);
    }
}
var resourceId;
var shareAssignment=false;
var assignmentMode;
function showShareOptions(resId,assignment,quizModeInput){
    shareAssignment = assignment;
    resourceId = resId;
    assignmentMode = quizModeInput;
    $('.loading-icon').removeClass('hidden');
    <g:remoteFunction controller="institute" action="getBatchesToShare" params="'resId='+resId+'&bookId=${bookId}'" onSuccess='displayShareableInstructorBatches(data);'/>
}

var shareBatches;

function displayShareableInstructorBatches(data){
     shareBatches = data.batches;
    $("#shareErrorMessage").hide();
    var htmlStr="";

    if("OK"==data.status){
        if(shareAssignment&&assignmentMode!="nonquiz")htmlStr +="<input type='text' name='testName' placeholder='Enter assignment name' id='testName' size='60'><br><br>";
        htmlStr+="<div class='row'>";
        for(i=0;i<shareBatches.length;i++){
            htmlStr +="<div class='col-md-4'><input type='checkbox' name='batch"+shareBatches[i].batchId+"' id='sharebatch"+shareBatches[i].batchId+"' value='"+shareBatches[i].batchId+"'>&nbsp;&nbsp;"+shareBatches[i].name+"</div>";
        }
        htmlStr +="</div><br><br>";
    }
    document.getElementById("shareBatches").innerHTML=htmlStr;
    document.getElementById("shareResourceFooter").innerHTML="<button type=\"button\"  class=\"btn btn-primary\" onclick=\"shareResouces();\">Submit</button>";
    $('.loading-icon').addClass('hidden');
    $("#shareResourceModal").modal('show');
    sharingStarted=false;
}

var sharingStarted=false;
function shareResouces(resId){
    var batchIds="";
    $("#shareErrorMessage").hide();
        for(i=0;i<shareBatches.length;i++){
            if(document.getElementById("sharebatch"+shareBatches[i].batchId).checked){
                batchIds+=shareBatches[i].batchId+",";
            }
        }
        if(batchIds.length>0) {
            batchIds = batchIds.substring(0,(batchIds.length-1));
            if(!sharingStarted) {
                $('.loading-icon').removeClass('hidden');
                sharingStarted=true;
                if (shareAssignment) {
                    if("nonquiz"==assignmentMode){
                        <g:remoteFunction controller="institute" action="shareAssignment" params="'resId='+resourceId+'&batchIds='+batchIds" onSuccess='shareCompletion(data);'/>
                    }else {
                        var testName = document.getElementById("testName").value;
                        <g:remoteFunction controller="institute" action="createAssignmentFromQuiz" params="'quizId='+resourceId+'&testName='+testName+'&batchIds='+batchIds" onSuccess='shareCompletion(data);'/>
                    }
                }
                else {
                    <g:remoteFunction controller="institute" action="shareResouces" params="'resId='+resourceId+'&chapterId='+previousChapterId+'&batchIds='+batchIds" onSuccess='shareCompletion(data);'/>

                }
            }
        }
    else{
           document.getElementById("shareErrorMessage").innerHTML = "Please select atleast one batch before sharing.";
           $("#shareErrorMessage").show();
        }
}


function shareCompletion(data){
    sharingStarted=false;
    $('.loading-icon').addClass('hidden');
    if(data.status=="success"){
        document.getElementById("shareBatches").innerHTML="Successfully shared";
        document.getElementById("shareResourceFooter").innerHTML="<button type=\"button\" data-dismiss=\"modal\" class=\"btn btn-primary\">Close</button>";
    }
}

function updateUserView(id,fromTab,viewedFrom){
    <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
}

function updateUserViewChapter(id,fromTab,viewedFrom,action){
    <g:remoteFunction controller="log" action="updateUserView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
}


</script>

</sec:ifLoggedIn>
<script>
    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateViewChapter(id,fromTab,viewedFrom,action){
        <g:remoteFunction controller="log" action="updateView" params="'chapterId='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom+'&actionName='+action" />
    }

    function initCallGoogle(id,loadedType){


        var bookTitle = "${bookName}";
            var seoFriendlyTitle = "${title}";
           <%if("books".equals(grailsApplication.config.grails.appServer.default)&&"1".equals(""+session["siteId"])) {%>
            var currentUrl = window.location.href;
            //add the resId
            if(currentUrl.indexOf("&resId")==-1)
            currentUrl=currentUrl+"&resId="+id;
            else{
                currentUrl = currentUrl.substr(0,currentUrl.indexOf("&resId"))+"&resId="+id;
            }
            history.pushState({
                id: 'homepage'
            }, '', currentUrl);
            if(seoFriendlyTitle.indexOf("Wonderslate")>0) {
                var wonderslateIndex =   seoFriendlyTitle.indexOf(" - Wonderslate");
                seoFriendlyTitle = seoFriendlyTitle.substr(0,wonderslateIndex)+": "+loadedType+" - Wonderslate";
                document.title = seoFriendlyTitle;
            }

           callGoogle(window.location.pathname+"/"+window.location.search);

        <%}
       %>

    }
</script>
