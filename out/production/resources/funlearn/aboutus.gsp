<g:render template="navheader"></g:render>




    <div class="container main-shadow">
        
        <h3 id="updatedHeader">About Us</h3>
        <div class="row">
            
            <div class="col-md-10 col-md-offset-1"><br>
                <b>What is Wonderslate ?</b>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1"><br>
                <p>Wonderslate is an educational deliverance, digital publishing and test preparation platform.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1 additionalinfo"><br>
                <b>Educational Deliverance</b>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1"><br>
                <p><b>Repository & Collaboration</b> - By default everything you create will be in private mode, only the creator can see it. He can use it himself or share within the group or decide to publish. Any item that is published will be available for everybody to use it.</p>
                <p><b>Relevance</b> - All the contents are organised as per the relevance to the syllabus</p>
                <p><b>Gamification</b> - You add the content, we provide the engaging environment for effective learning.</p>
                <p><b>Crowd sourcing</b> - Allow teachers,students and parents to create the biggest and relevant educational content based on their syllabus.</p>
                <p><b>Educational specific content creation tools</b> - A set of tools to create or upload syllabus specific videos, notes, mind maps and objective types ( MCQs, Fill in the blanks, True or False etc) as quizzes.</p>

            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1 additionalinfo"><br>
                <b>Digital Publishing Platform - For Educational Book Publishers & Individual Authors</b>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1"><br>
                <p>The current trend  suggests that technology is going to be a key and integral part of education. Specifically in the field of publishing, we have witnessed inception and growth of ebook and platforms like Amazon kindle.
                While these technologies work fine for static content, not a complete solution for education purpose.</p>
                <p>Wonderslate introduces an integrated publishing platform where</p>
                <p><b>Online publishing tools</b> - Tools to publish both static and dynamic (videos,quizzes, worksheets etc).</p>
                <p><b>Multiple device support</b> - Content available both in web and app version. This will seamlessly work on all digital devices from computers to handheld devices(mobiles and tabs).</p>
                <p><b>Integrated learning experience with inbuilt analytics engine</b> - Publisher provides the content, the Wonderslate engine builds the complete learning experience which includes analytics, gamification, collaboration etc.</p>
                <p><b>Online Store at no investment from the Publisher/Author</b> - The platform provides online store where these publications will be sold and this does not need any investment from the publisher/author. We take care of payment processing.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1 additionalinfo"><br>
                <b>Exam preparation platform for your brand - For Educational/Coaching institutes and Educational Book Publishers</b>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1"><br>
                <p>Every year preparation for more and more competitive exams in almost every field(IIT-JEE entrance exams,Banking exams, Railways exams etc) are going online. As per the recent data over 2 crore students take these exams every year. And with global online learning market expected to exceed $100 Bn.</p>
                <p>Some of the popular test preparation sites in India are Toppr,Testbook,Embibe etc. </p>
                <p>Traditionally to build a platform like this for your brand, required huge investment on technology. Well not anymore. Wonderslate has built a platform where all the online test preparations. You bring the content and walk away with your branded
                test prep site. Some of the salient features</p>
                <p><b>No investment on technology</b> - We work on revenue sharing basis. So there is no risk for you.</p>
                <p><b>Simple and no technical background required</b> - Building and maintaining a site like this requires huge technical experience and incurs huge expenditure. With Wonderslate you are spared of both.</p>
                <p><b>Integrated learning experience with inbuilt analytics engine</b> - You provide the content, the Wonderslate engine builds the complete learning experience which includes insights,analytics, gamification, collaboration etc.</p>
                <p><b>Entire world is a market</b> - Physical expansion has its limitations, but online whole world is your market.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1"><br>
                <p>If you need more information, contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            </div>
        </div>


        <!--Founder-->
                    
        <div class="row">
            <div class="col-md-12 text-center"><br>
                <h3 class="teamMembers">Team</h3><br>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
            <p>
                Wonderslate was founded in 2015 by Anand Achyut and Asha Anand,
                to address the need of relevant academic materials,
                nurture creativity and provide an all-round educational support system for students, parents and teachers.
            </p><br>
            </div>
        </div>    
        
        <div class="row about">
            <div class="col-md-4 col-md-offset-2">
                <div class="thumbnail">
                    <img src="${assetPath(src: 'profile/profile.jpg')}" alt="anand">
			        <div class="caption">
                        <h3>Anand Achyut</h3>
                        <p>Anand Achyut started his corporate life in Novell and moved on to become the Global Head
                           for IT in httprint. With over 20 years of experience in building enterprise applications,
                           eProcurement systems, distributed computing, software security, information risk management,
                           data analytics and architecting bespoke client applications, Anand started Mithri Technologies,
                           running successfully till date.
                        </p>
                        <p class="founderSocial">   
                                <a href="https://www.facebook.com/anand.achyut?fref=ts"><i class="fa fa-facebook-official"></i></a>
                                <a href="https://www.linkedin.com/in/anandachyut?authType=NAME_SEARCH&authToken=cHu-&locale=en_US&trk=tyah&trkInfo=clickedVertical%3Amynetwork%2CclickedEntityId%3A6241909%2CauthType%3ANAME_SEARCH%2Cidx%3A1-1-1%2CtarId%3A1457335881742%2Ctas%3Aanand"><i class="fa fa-linkedin"></i></a>   
                        </p>
			        </div>
		        </div>
            </div>
            <div class="col-md-4">
                <div class="thumbnail">
                    <img src="${assetPath(src: 'profile/asha.jpg')}" alt="asha">
			<div class="caption">
                        <h3>Asha Anand</h3>
                        <p>
                            Asha Anand, a trained Counsellor and Psychotherapist apart from being a software engineer,
                            has had an enriching work experience both in India and abroad.
                            Her deep passion for understanding human dynamics and contributing to their wellbeing caused her to
                            deviate from the binary world of computer technology and devote herself to the field of psychology and cure.
                        </p><br>
                        <p class="founderSocial">   
                                <a href="https://www.facebook.com/asha.anand.1840?fref=ts" alt="Facebook"><i class="fa fa-facebook-official"></i></a>
                                <a href=""><i class="fa fa-linkedin" alt="LinkedIn"></i></a>   
                        </p>
			</div>
		</div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
            <p>
                Anand and Asha started artVana, together, in 2013. It is a space to develop creative learning for children. With the growth of artVana, they felt inspired to do more on the field of education. This lead to the inception of Wonderslate.
            </p><br>
            </div>
        </div>
        <div class="row about">
           <br>
            <div class="col-md-4 col-md-offset-2">
                <div class="thumbnail">
                    <img src="${assetPath(src: 'profile/sunil1.jpg')}" class="img-responsive" alt="sunil">
                    <div class="caption text-center">
                        <h3>Sunil Patki</h3>
                        <h4 >Publishing Evangelist</h4>
                        <p>3 decades plus in the Publishing Industry. Starting with McGraw-Hill in 1982. Subsequently Initiated the Simon & Schuster, Thomson Learning offices in India.
                        Moved to Delhi in 05 to head the Higher Education & General Books at Macmillan India.
                        Moving further to BPI India, publishers of children books & toys.
                        Launched www.notjustpublishing.in April 2011.
                        Business Head at Vikas Publishing House/Madhubun Books-October 2012 to March 2015.
                        Group Business Head at S Chand Publishing-April 2015 to July 2016.</p><br>
                        <p class="founderSocial">
                            <a href="https://www.facebook.com/sunil.patki"><i class="fa fa-facebook-official"></i></a>
                            <a href="https://in.linkedin.com/in/sunil-s-patki-255b894"><i class="fa fa-linkedin"></i></a>
                        </p>

                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="thumbnail">
                    <img src="${assetPath(src: 'profile/anil1.jpg')}" class="img-responsive" alt="mukesh">
                    <div class="caption text-center">
                        <h3>Anil Veerabhadrappa</h3>
                         <h4>Advisor, Mentor, Investor</h4>
                        <p>
                            Anil Veerabhadrappa has 19 years of experience in IT industry building successful products for companies such as Wipro, Adaptec and Broadcom. Anil loves travelling and outdoor adventure. He is passionate about world history, cultural heritage, wildlife photography and exploring nature.
                            Coming from a family of teachers and surrounded by inspiring minds, he is passionate about education and believes in accessible quality education for every child </p>
                        <p class="founderSocial">
                            <a href="https://www.facebook.com/anilgv"><i class="fa fa-facebook-official"></i></a>
                            <a href="https://www.linkedin.com/in/anil-veerabhadrappa-aa87613"><i class="fa fa-linkedin"></i></a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
            
        <!--Team Members-->
       <div id="team">
        <div class="row about">
            <h3 class="teamMembers"></h3><br>
                <div class="col-md-4 col-md-offset-2">
                    <div class="thumbnail">
                        <img src="${assetPath(src: 'profile/ShwetabhSingh.jpg')}" class="img-responsive" alt="shwetabh">
                        <div class="caption">
                            <h3>Shwetabh Singh</h3>
                            <p class="qualification"><b>(B.tech in Computer Science)</b></p>
                            <h4>Android Application Developer</h4>
                            <p>
                                Wonderslate’s sportsman, Shwetabh is passionate about outdoor games, travelling and watching movies. He has a deep interest in visiting places of national heritage. His love for computers led him to choose a career in computer science and technology.
                            </p>
                            <p class="social">
                                <a href="https://www.facebook.com/shwetabh07singh"><i class="fa fa-facebook-official"></i></a>
                                <a href="https://www.linkedin.com/in/shwetabh-singh-39258951"><i class="fa fa-linkedin"></i></a>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="thumbnail">
                        <img src="${assetPath(src: 'profile/MukeshThawani.jpg')}" class="img-responsive" alt="mukesh">
                            <div class="caption">
                            <h3>Mukesh Thawani</h3>
                            <p class="qualification"><b>(B.E. in Electrionics and Communication Engineering)</b></p>
                            <h4>iOS Developer</h4>
                            <p>
                               Mukesh loves his Mac and nobody is allowed to come in between! He is an open source enthusiast and a contributor to different open source projects. Other than technology, he is an amateur economist, political geek and debater.
                            </p>
                            <p class="social">
                                <a href="https://www.facebook.com/mukesh9039"><i class="fa fa-facebook-official"></i></a>
                                <a href="https://www.linkedin.com/in/mukeshthawani"><i class="fa fa-linkedin"></i></a>
                            </p>
                            </div>
                    </div>
                </div>
        </div>

            <div class="row about">
                <div class="col-md-4 col-md-offset-2">
                    <div class="thumbnail">
                        <img src="${assetPath(src: 'profile/ramya.jpg')}" class="img-responsive" alt="amrita">
                            <div class="caption">
                            <h3>Ramya</h3>
                                <p class="qualification"><b>(B.E. in Computer Science)</b></p>
                                <h4>Manager - Business Strategy and Innovation</h4>
                            <p>
                                An Engineer with decennial of experience in wireless technology providing high class solutions to global customers. Love to travel, trekking on different terrains, meeting new people,listening to their stories gives new perspective to life. Thank goodness my high energy and keen nature supports all the adventure I want to take on.
                            </p>
                            <p class="social">
                                <a href="https://www.facebook.com/ramya.vijaykumar.7"><i class="fa fa-facebook-official"></i></a>
                                <a href="https://in.linkedin.com/in/ramya-v-952a7114"><i class="fa fa-linkedin"></i></a>
                            </p>
                            </div>
                    </div>
                </div>

                <div class="col-md-4">

                </div>
                </div>
    </div>



    </div>
<g:render template="footer"></g:render>

<asset:javascript src="jquery-1.11.2.min.js"/>

<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<asset:javascript src="fileuploadwithpreview.js"/>
<asset:javascript src="quizcreator.js"/>


<script>


</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
</body>
</html>