<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <div class="d-flex justify-content-between align-items-center">
        <button onclick="window.history.back();" class="btn btn-secondary">Back</button>
        <h2 class="text-center flex-grow-1">List of Courses</h2>
    </div>
    <hr/>
    <div class="text-right">
        <g:link action="createCourse" class="btn btn-primary">Add New Course</g:link>
    </div>
    <table class="table table-striped table-responsive">
        <thead>
        <tr>
            <th>Name</th>
            <th>Grade Type</th>
            <th>Grades</th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        <g:each in="${courses}" var="course">
            <tr>
                <td>${course.name}</td>
                <td>${course.gradeType}</td>
                <td>
                    <g:if test="${course.gradeType == 'Custom'}">
                        ${course.customGrades}
                    </g:if>
                    <g:else>
                        ${course.gradeStart} - ${course.gradeEnd}
                    </g:else>
                </td>
                <td>
                    <g:link action="editCourse" params="[id: course.id]" class="btn btn-info btn-sm">Edit</g:link>
                    <g:link action="deleteCourse" params="[id: course.id]" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this course?');">Delete</g:link>
                </td>
            </tr>
        </g:each>
        </tbody>
    </table>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
