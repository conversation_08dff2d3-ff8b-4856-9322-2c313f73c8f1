
<div id="instituteBannerImages" class="banner-ws">
    <div id="slider-desktop" class="carousel slide w-100 shadow" data-ride="carousel">
        <ol class="carousel-indicators" id="slider-desktop-indicators">
        </ol>
        <div class="carousel-inner w-100" id="slider-desktop-views">
        </div>
    </div>
</div>

<div id="defaultBannerInfo" class="default-banner" style="display: none;">
    <div id="instituteDefaultBanner" class="w-100 shadow p-4">
        <div class="institute-banner-info text-center">
            <h1>${institute.name}</h1>
            <p>${institute.tagline}</p>
        </div>
    </div>
</div>

<script>
    var instituteId = "${institute.id}";

    function getInstituteBanners(instituteId) {
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="institute" action="getInstituteBannerdetails" params="'instituteId='+instituteId" onSuccess="showInstituteBanners(data);"/>
    }

    getInstituteBanners(instituteId);


    function showInstituteBanners(data) {
        setTimeout(function () {
            $(".loading-icon").addClass("hidden");
        },2000);
        if(data.status=="OK") {
            var banners = data.banners.reverse();
            $('#slider-desktop-views').empty();

            // Banner slider
            $.each(banners, function (i, v) {
                var item = v;
                var htmlStr = '';
                var indicatorStr = '';
                var imageDescUrl = "/institute/showInstituteBannerImage?instituteId="+instituteId+"&id=" + v.id + "&fileName=" + v.imagePath;
                if(v.bookTitle) {
                    var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
                }
                var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

                var actionLink = v.action //action field link
                var serverURL =  window.location.origin //getting base url
                actionLink = serverURL+'/'+actionLink;


                indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";

                if(v.bookId) {
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'" class="d-flex align-items-center w-100 rounded banner-img">' +
                        '</a>' +
                        '</div>';
                } else if (v.action) {
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'" class="d-flex align-items-center w-100 rounded banner-img">' +
                        '</a>' +
                        '</div>';
                }else{
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<img src="'+imageDescUrl+'" class="d-flex align-items-center w-100 rounded banner-img">' +
                        '</div>';
                }

                // If desktop banners are available
                if(v.imagePath) {
                    $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
                    $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                }

            });

        } else if(data.status=="Nothing Present") {
            $("#defaultBannerInfo").show();
            $("#instituteBannerImages").hide();
        }

    }

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
</script>
