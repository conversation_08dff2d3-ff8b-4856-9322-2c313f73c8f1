<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    var defaultBatchId="";
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js pb-5">

    <div class="container">
        <div class="mt-3 mt-md-0 py-4 pt-md-5">
            <div class="d-flex justify-content-start align-items-center">
                <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
                <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
                <h3><strong>Classes</strong></h3>
                <%if(classes.size()>1){%><a href="javascript:toggleClassAdd();" class="btn btn-primary btn-primary-modifier btn-shadow d-flex justify-content-center align-items-center ml-3 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect"> <i class='material-icons'>add</i> <span>Add</span></a><%}%>
            </div>
        </div>
        <%if(classes.size()==1){%>
        <div class='p-3 p-md-4' id="zeroClasses">
            <p class='text-center text-secondary text-secondary-modifier pt-4'>No classes added to the institute yet. <br><strong>Create the first class now.</strong></p>
            <div class='text-center'><a href='javascript:toggleClassAdd();' class='btn btn-success btn-success-modifier btn-shadow mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Add Class</a></div>
        </div>
        <%}%>
        <div id="addClass" class="py-3" style="display: none">
            <label class="text-secondary text-secondary-modifier">** Enter the details of class</label>
            <div class="form-row">
                <div class="col-12 col-md-4 col-lg-3 mb-3">
                    <div class="form-group form-group-modifier">
                        <input type="text" class="form-control form-control-modifier border-bottom" name="batchName" id="batchName" placeholder="Class Name">
                    </div>
                    <p id="errorClassName" class="form-text form-text-modifier text-danger hidden"></p>
                </div>
                <div class="col-12 col-md-4 col-lg-3 mb-3">
                    <div class="form-group form-group-modifier">
                        <input type="text" id="endDate" class="form-control form-control-modifier border-bottom" name="endDate" placeholder="Completion Date (optional)">
                    </div>
                </div>
                <div class="col-12 col-md-4 col-lg-3 mb-3">
                    <div class="form-group form-group-modifier">
                        <select id="createSyllabus" class="form-control" name="createSyllabus"  onchange="javascript:getCreateGrade(this.value)" style="display: none"><option>Select</option></select>
                    </div>
                </div>
                <div class="col-12 col-md-4 col-lg-3 mb-3">
                    <div class="form-group form-group-modifier">
                        <select id="createGrade" class="form-control" name="createGrade"  style="display: none"><option>Select</option></select>
                    </div>
                </div>

                <div class="col-6 col-md-3 col-lg-2 mb-3">
                    <button class="btn btn-success btn-success-modifier btn-shadow w-100 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect"  onclick="addBatch();" id="addClassButton">Add Class</button>
                    <button class="btn btn-success btn-success-modifier btn-shadow w-100 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" onclick="saveBatch();" id="editClassButton" style="display: none">Save</button>
                </div>
             </div>
        </div>
    </div>

    <%-- Manage Classes --%>
    <%if(classes.size()>1){%>
    <div class='container'>
        <div class='card card-modifier card-shadow border-0 mt-4 mt-md-5 p-4'>
            <div id="content-books">
                <h5>Manage</h5>
                <div class="form-row">
                    <div class="col-12 col-md-4 col-lg-3 mb-3">
                        <div class="form-group form-group-modifier">
                            <select name="batches" id="batches" class="form-control form-control-modifier border-bottom" onchange="batchSelected();">
                            <option value="" disabled="disabled" selected>Select Class</option>
                            <g:each in="${classes}" var="batch" status="i">
                                <%if(!"Default".equals(batch.name)) {%>
                                <option value="${batch.id}">${batch.name}</option>
                                <%}else{%>
                                <script>
                                    defaultBatchId ="${batch.id}";
                                </script>
                                <%}%>
                            </g:each>
                        </select>
                        </div>
                    </div>
                    <%-- Manage Books--%>
                    <div class="col-12 col-md-4 col-lg-3 mb-3">
                        <div class="form-group form-group-modifier">
                            <select id="manageOptions" class="form-control form-control-modifier border-bottom" style="display: none" onchange="manageOptionChanged(this);">
                            <option value="" disabled="disabled" selected>Select</option>
                            <option value="editClass">Edit class</option>
                            <option value="addbooks">Add eBooks</option>
                                <%if(isEduWonder){%>
                            <option value="addinstructors">Add Instructors</option>
                                <%}%>
                            <option value="addstudents">Add Students</option>
                            <option value="viewbooks">View eBooks</option>
                                <%if(isEduWonder){%>
                                <option value="viewinstructors">View Instructors</option>
                                <%}%>
                            <option value="viewstudents">View Students</option>
                        </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="errormsg" class="alert alert-danger mt-4" role="alert" style="display: none;"></div>
            <div id="successmsg" class="alert alert-success mt-4" role="alert" style="display: none;"></div>
            <div id="batchUsers" style="display: none"></div>

        </div>
    </div>
    <%}%>
</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    var bookIdArr = [];
    var viewMode = true;
    var selectedSyllabus = '${syllabus}';
    var selectedGrade = '';
    $('#endDate').datepicker({
        format: 'dd-mm-yyyy',
        startDate: new Date(),
        maxViewMode: 1,
        autoclose: true,
        todayHighlight: true,
        keyboardNavigation: false,
        orientation: "bottom left"
    });

    function toggleClassAdd(){
        $("#zeroClasses").hide();
        $("#addClass").toggle(500);
        $("#addClassButton").show();
        $("#editClassButton").hide();
        $("#batchName").focus();
        <%if(level!=null){%>
        getCreateSyllabus('${level}');
        <%}%>
    }
    function getCreateSyllabus(level){

        $('#createSyllabus').hide();

        level = encodeURIComponent(level);

        <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
             params="'level='+level"/>

    }

    function initializeCreateSyllabus(data){
        syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {
            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            if(syllabusList[i].syllabus==selectedSyllabus) {
                el.selected = "true";
            }
            select.appendChild(el);
        }

        select.focus();
        $('#createSyllabus').show();
        getCreateGrade(selectedSyllabus);

    }

    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        $('#createSubject').hide();
        var length=13;
        var level = document.getElementById("createLevel");
        if("School"=="${level}"&&syllabus!='NIOS'){
            var select = document.getElementById("createGrade");
            select.options.length = 1;
            <% if(session['siteId'].equals(5)){%>
            length=9;
            el = document.createElement("option");
            el.textContent = 'Pre Primary';
            el.value = 'Pre Primary';
            select.appendChild(el);
            <%}%>
            for(var i=1;i< length; i++) {
                el = document.createElement("option");
                el.textContent = i;
                el.value = i;
                if(i==selectedGrade) {
                    el.selected = "true";
                }
                select.appendChild(el);
            }

            select.focus();
            $('#createGrade').show();

        }
        else{
            var seperate=true;
            for(var i=0;i< syllabusList.length; i++) {
                if(syllabus==syllabusList[i].syllabus){
                    if(syllabusList[i].gradeType=="Semester"){
                        seperate=false;
                        var select = document.getElementById("createGrade");
                        select.options.length = 1;
                        var startSemester=1;
                        var endSemester=8;
                        if(syllabusList[i].startSemester!=null) startSemester = syllabusList[i].startSemester;
                        if(syllabusList[i].endSemester!=null) endSemester = syllabusList[i].endSemester;
                        el = document.createElement("option");
                        el.textContent = 'All Semesters';
                        el.value = 'All Semesters';
                        select.appendChild(el);
                        for(var j=startSemester;j< (1+endSemester); j++) {

                            el = document.createElement("option");
                            el.textContent = 'Semester '+j;
                            el.value = 'Semester '+j;
                            if(('Semester '+j)==selectedGrade) {
                                el.selected = "true";
                            }
                            select.appendChild(el);
                        }

                        select.focus();
                        $('#createGrade').show();

                    }
                    break;
                }
            }
            if(seperate){
                if(syllabus==='NIOS'){
                    $('#createGrade').html(" <option>Select</option>" +
                        "<option value=\"Open Basic Education\">Open Basic Education</option>\n" +
                        "    <option value=\"Secondary courses\">Secondary courses</option>\n" +
                        "    <option value=\"Open Basic Education\">Senior Secondary courses</option>\n" +
                        "    <option value=\"Vocational Courses\">Vocational Courses</option>\n" +
                        "    <option value=\"Diploma in Elementary Education (D.El.Ed)\">Diploma in Elementary Education (D.El.Ed)</option>");
                    $('#createGrade').show();
                    select.focus();
                }
                else {
                    $("#booksaving").show(500);
                    syllabus = encodeURIComponent(syllabus);
                    <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>
                }
            }


        }
    }

    function initializeCreateGrades(data){
        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");

            el.textContent = grades[i].grade+(grades[i].state!=null?" ( "+grades[i].state+" )":"");
            el.value = grades[i].grade;
            if(grades[i].grade==selectedGrade) {
                el.selected = "true";
            }
            select.appendChild(el);
        }

        select.focus();
        $('#createGrade').show();
        z
    }

    function addBatch() {
        if (document.getElementById("batchName").value === "") {
            document.getElementById("errorClassName").innerHTML = "Please enter the class name.";
            $("#errorClassName").removeClass('hidden');
            $("#batchName").focus();
        } else {
            $('.loading-icon').removeClass('hidden');
            $("#errorClassName").addClass('hidden');
            var instituteId = "${params.instituteId}";
            var batchName = document.getElementById("batchName").value;
            var endDate = document.getElementById("endDate").value;
            endDate = endDate.split("-").reverse().join("-");
            var grade = document.getElementById("createGrade")[document.getElementById("createGrade").selectedIndex].value;
            var syllabus = document.getElementById("createSyllabus")[document.getElementById("createSyllabus").selectedIndex].value;

            <g:remoteFunction controller="institute" action="addBatch"
            params="'instituteId='+instituteId+'&endDate='+endDate+'&batchName='+batchName+'&grade='+grade+'&syllabus='+syllabus" onSuccess = "batchAdded(data);"/>
        }
    }
    function saveBatch() {
        if (document.getElementById("batchName").value === "") {
            document.getElementById("errorClassName").innerHTML = "Please enter the class name.";
            $("#errorClassName").removeClass('hidden');
            $("#batchName").focus();
        } else {
            $('.loading-icon').removeClass('hidden');
            $("#errorClassName").addClass('hidden');
            var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
            var batchName = document.getElementById("batchName").value;
            var endDate = document.getElementById("endDate").value;
            endDate = endDate.split("-").reverse().join("-");
            var grade = document.getElementById("createGrade")[document.getElementById("createGrade").selectedIndex].value;
            var syllabus = document.getElementById("createSyllabus")[document.getElementById("createSyllabus").selectedIndex].value;

            <g:remoteFunction controller="institute" action="editBatch"
            params="'batchId='+batchId+'&endDate='+endDate+'&batchName='+batchName+'&grade='+grade+'&syllabus='+syllabus" onSuccess = "batchAdded(data);"/>
        }
    }

        function batchAdded(data){
            window.location.href = "/institute/manageClasses?instituteId=${params.instituteId}";
        }

        function getBatchDetails(){
            var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
            <g:remoteFunction controller="institute" action="getBatchDetails"
            params="'batchId='+batchId" onSuccess = "showBatchDetails(data);"/>

        }

        function showBatchDetails(data){
         document.getElementById("batchName").value=data.batchName;
         document.getElementById("endDate").value = data.endDate;
            <%if(level!=null){%>
            selectedGrade="";
            getCreateSyllabus('${level}');
            if(data.syllabus!=null) selectedSyllabus = data.syllabus;
            if(data.grade!=null) selectedGrade = data.grade;
            <%}%>
         $("#addClassButton").hide();
            $("#editClassButton").show();
            $("#addClass").show(500);
            $("#batchName").focus();
            $('html, body').animate({scrollTop:0}, 'slow');
        }
        function batchSelected(){
            $("#manageOptions").show(500).focus();
            document.getElementById("manageOptions").selectedIndex=0;
            document.getElementById("batchUsers").innerHTML="";
        }

        function manageOptionChanged(field){
          if(field.selectedIndex==0){

          }else{
              if("addbooks"==field[field.selectedIndex].value){
                  getBooksForInstitute();
              }
              else if("viewbooks"==field[field.selectedIndex].value){
                  getBooksForBatch();
              }else if("viewstudents"==field[field.selectedIndex].value){
                  getBatchUsers('students');
              }
              else if("addstudents"==field[field.selectedIndex].value){
                  getInstituteUsers('students');
              }else if("viewinstructors"==field[field.selectedIndex].value){
                  getBatchUsers('instructor');
              }
              else if("addinstructors"==field[field.selectedIndex].value){
                  getInstituteUsers('instructor');
              }else if("editClass"==field[field.selectedIndex].value){
                  getBatchDetails();
              }
          }
        }

        //book related
    function getBooksForInstitute(){
        $('.loading-icon').removeClass('hidden');
        viewMode = false;
        $("#errormsg, #successmsg").hide();
        $("#batchUsers").show();
        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="getBooksForBatch" params="'batchId='+defaultBatchId+'&sourceBatchId='+batchId" onSuccess = "showBooksForBatch(data);"/>

    }
    function getBooksForBatch(){
        $('.loading-icon').removeClass('hidden');
        viewMode = true;
        $("#errormsg, #successmsg").hide();
        $("#batchUsers").show();
        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="getBooksForBatch" params="'batchId='+batchId" onSuccess = "showBooksForBatch(data);"/>

    }

    function showBooksForBatch(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {

            var htmlStr = "";
            if(viewMode) {
                htmlStr = "<div class='mb-2 float-right'><button disabled type=\"button\" class='btn btn-danger btn-danger-modifier px-4' id='deleteBook' onclick=\"deleteBook()\">Delete</button></div>";
            }else{
                htmlStr = "<div class='mb-2 float-right'><button disabled type=\"button\" class='btn btn-success btn-success-modifier px-4' id='deleteBook' onclick=\"addMultipleBook(this)\">Add</button></div>";
            }
            htmlStr +=   "                    <table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary bg-primary-modifier text-white text-center'>              <tr>\n" +
                "                           <th><div style='display: flex;align-items: center;'> <input type=\"checkbox\" class='btn btn--light' id=\"selectAll\" value='Select All ' onchange=\"selectAllBooks()\"><span style='margin: 0 1rem;'>Select All</span></div></th >" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Title</th>\n" +
                "                            <th>Isbn</th>\n" +
                "                        </tr> </thead>\n";
        }
        if(data.status=="OK"){
            var books = data.books;
            for(var i=0;i<books.length;i++){
                htmlStr +="<tr>" +
                    "<td><input type=\"checkbox\" onchange='bookSelectChanged(this);' class=\"selectAll\" value="+books[i].bookId+"></td>" +
                    "<td style='text-transform:capitalize;'>"+books[i].bookId+"</td>"+
                    "<td>"+books[i].title+"</td>"+
                    "<td>"+books[i].Isbn+"</td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#batchUsers table').DataTable( {
                "ordering": false
            });
        }else{
            document.getElementById("batchUsers").innerHTML= "<p class='text-secondary text-secondary-modifier'>No eBooks added to this class yet.</p>";
        }
        $("#batchUsers").show();
    }

    function bookSelectChanged(field){

        if($(field).is(":checked") == true){
            if(bookIdArr.indexOf($(field).val()) == -1) bookIdArr.push($(field).val());
        }else if($(field).is(':checked') == false){
            if(bookIdArr.indexOf($(field).val()) > -1){
                bookIdArr.splice(bookIdArr.indexOf($(field).val()),1);
            }
        }

        var bookChkSelected = false;
        var checkArr = document.getElementsByClassName("selectAll");
        if(!checkArr.checked){
            document.getElementById("selectAll").checked = false;
        }
        for(var k = 0; k < checkArr.length; k++){
            if(checkArr[k].checked){
                document.getElementById("deleteBook").disabled = false;
                bookChkSelected = true;
                break
            }
        }

        if(!bookChkSelected){
            document.getElementById("deleteBook").disabled = true;
            document.getElementById("selectAll").checked = false;
        }
    }

    function addMultipleBook() {
        var bookIdsStr ="";
        for(var i=0;i<bookIdArr.length;i++){
            bookIdsStr = bookIdsStr + bookIdArr[i] + ",";
        }
        bookIdsStr = bookIdsStr.substring(0, bookIdsStr.length-1)

        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="addBooksToBatch" params="'batchId='+batchId+'&bookIds='+bookIdsStr+'&defaultBatchId='+defaultBatchId" onSuccess = "bookAdded(data);"/>
    }

    function selectAllBooks(){
        var checkArr = document.getElementsByClassName("selectAll");
        if(document.getElementById("selectAll").checked){
            for (var k = 0; k < checkArr.length; k++) {
                checkArr[k].checked = true;
                if(bookIdArr.indexOf(checkArr[k].value) > -1) continue;
                else bookIdArr.push(checkArr[k].value);

            }
            document.getElementById("deleteBook").disabled = false;
        }else{
            for (var k = 0; k < checkArr.length; k++) {
                // alert(bookIdArr.indexOf(checkArr[k].value));
                bookIdArr.splice(bookIdArr.indexOf(checkArr[k].value),1);
                checkArr[k].checked = false;
                // get the index of the id
                // slice the id using the index
            }
            document.getElementById("deleteBook").disabled = true;
        }
    }

    function bookAdded(data){
        $("#errormsg, #successmsg").hide();
        $('.loading-icon').addClass('hidden');
        document.getElementById("manageOptions").selectedIndex=0;
        document.getElementById("batchUsers").innerHTML="";
        bookIdArr = [];
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Book(s) added!";
            $("#successmsg").show();
            $("#manageOptions").focus();
        }

    }

    function deleteBook(){

        if(confirm("Do you want to go ahead and delete this book from the class?")) {
            var ids = "";
            var checkArr = document.getElementsByClassName("selectAll");
            for(var k = 0; k < checkArr.length; k++){
                if(checkArr[k].checked){

                    ids = ids +checkArr[k].value +",";
                }
            }
            ids = ids.substring(0, ids.length-1);
            var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
            if(ids != "" && ids != undefined && ids != null){
                <g:remoteFunction controller="institute" action="removeBookFromBatchSage" params="'batchId='+batchId+'&bookId='+ids" onSuccess = "bookDeleted(data);"/>
            }
        }

    }

    function bookDeleted(data){
        $("#errormsg, #successmsg").hide();
        $('.loading-icon').addClass('hidden');
        document.getElementById("batchUsers").innerHTML="";
        document.getElementById("manageOptions").selectedIndex=0;
        bookIdArr = [];
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Book(s) deleted!";
            $("#successmsg").show();
            $("#manageOptions").focus();
        }
    }

    //users
    var globalUserType;
    var userIdArr = [];
    function getBatchUsers(userType){
        globalUserType = userType;
        viewMode = true;
        $("#errormsg, #successmsg").hide();
        $("#batchUsers").show();
        $('.loading-icon').removeClass('hidden');
        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="showAllUsersForBatch" params="'instituteId=${params.instituteId}&batchId='+batchId+'&userType='+userType" onSuccess = "showUsersForBatch(data);"/>

    }

    function getInstituteUsers(userType){
        globalUserType = userType;
        viewMode = false;
        $("#errormsg, #successmsg").hide();
        $("#batchUsers").show();
        $('.loading-icon').removeClass('hidden');
        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="showAllUsersForBatch" params="'instituteId=${params.instituteId}&batchId='+defaultBatchId+'&userType='+userType+'&sourceBatchId='+batchId" onSuccess = "showUsersForBatch(data);"/>
    }
    function showUsersForBatch(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr = "";
        if(viewMode) {
            htmlStr = "<div class='mb-2 float-right'><button disabled type=\"button\" class='btn btn-danger btn-danger-modifier px-4' id='deleteBook' onclick=\"deleteUser()\">Delete</button></div>";
        }else{
            htmlStr = "<div class='mb-2 float-right'><button disabled type=\"button\" class='btn btn-success btn-success-modifier px-4' id='deleteBook' onclick=\"addMultipleUser(this)\">Add</button></div>";
        }
         htmlStr+="<table class='table table-striped table-bordered'>\n" +
            "           <thead class='bg-primary bg-primary-modifier text-white text-center'>              <tr>\n"  +
             "                           <th><div style='display: flex;align-items: center;'> <input type=\"checkbox\" class='btn btn--light' id=\"selectAll\" value='Select All ' onchange=\"selectAllUsers()\"><span style='margin: 0 1rem;'>Select All</span></div></th >" +
             "<th>Name</th>\n" +
            "<th>Email</th>\n"
        htmlStr+="<th>Mobile</th>\n"
        htmlStr+= " </tr></thead>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            for(var i=0;i<users.length;i++){
                htmlStr+="<tr><td><input type=\"checkbox\" onchange='userSelectChanged(this);' class=\"selectAll\" value="+users[i].userId+"></td>" +
                     "<td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+(users[i].email?users[i].email: '')+"</td>"
                htmlStr+="<td>"+(users[i].mobile?users[i].mobile: '')+"</td>"
                htmlStr+=    "</tr>";
            }
            htmlStr +="</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#batchUsers table').DataTable( {
                "ordering": false
            });
        }else{
            document.getElementById("batchUsers").innerHTML= "<p class='text-secondary text-secondary-modifier'>No "+globalUserType+" added to this class yet.</p>";
        }
        $("#batchUsers").show();

    }

    function deleteUser(){

        if(confirm("Do you want to go ahead and delete this "+globalUserType+" from the class?")) {
            var ids = "";
            var checkArr = document.getElementsByClassName("selectAll");
            for(var k = 0; k < checkArr.length; k++){
                if(checkArr[k].checked){

                    ids = ids +checkArr[k].value +",";
                }
            }
            ids = ids.substring(0, ids.length-1);
            var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
            if(ids != "" && ids != undefined && ids != null){
                <g:remoteFunction controller="institute" action="removeUsersFromBatch" params="'batchId='+batchId+'&userIds='+ids" onSuccess = "userDeleted(data);"/>
            }
        }

    }

    function addMultipleUser() {
        var userIdsStr ="";
        for(var i=0;i<userIdArr.length;i++){
            userIdsStr = userIdsStr + userIdArr[i] + ",";
        }
        userIdsStr = userIdsStr.substring(0, userIdsStr.length-1);
        var batchId = document.getElementById("batches")[document.getElementById("batches").selectedIndex].value;
        <g:remoteFunction controller="institute" action="addUsersToBatch" params="'batchId='+batchId+'&userIds='+userIdsStr+'&userType='+globalUserType" onSuccess = "userAdded(data);"/>
    }

    function userDeleted(data){
        $("#errormsg, #successmsg").hide();
        $('.loading-icon').addClass('hidden');
        document.getElementById("batchUsers").innerHTML="";
        document.getElementById("manageOptions").selectedIndex=0;
        if(data.status=="OK"){
            if(globalUserType == "instructor") {
                document.getElementById("successmsg").innerHTML="Instructor(s) deleted!";
            } else if (globalUserType == "students") {
                document.getElementById("successmsg").innerHTML="Student(s) deleted!";
            }
            $("#successmsg").show();
            $("#manageOptions").focus();
        }
    }

    function userAdded(data){
        $("#errormsg, #successmsg").hide();
        $('.loading-icon').addClass('hidden');
        document.getElementById("manageOptions").selectedIndex=0;
        document.getElementById("batchUsers").innerHTML="";
        if(data.status=="OK"){
            if(globalUserType == "instructor") {
                document.getElementById("successmsg").innerHTML="Instructor(s) added!";
            } else if (globalUserType == "students") {
                document.getElementById("successmsg").innerHTML="Student(s) added!";
            }
            $("#successmsg").show();
            $("#manageOptions").focus();
        }

    }

    function selectAllUsers(){
        var checkArr = document.getElementsByClassName("selectAll");
        if(document.getElementById("selectAll").checked){
            for (var k = 0; k < checkArr.length; k++) {
                checkArr[k].checked = true;
                if(userIdArr.indexOf(checkArr[k].value) > -1) continue;
                else userIdArr.push(checkArr[k].value);

            }
            document.getElementById("deleteBook").disabled = false;
        }else{
            for (var k = 0; k < checkArr.length; k++) {
                userIdArr.splice(userIdArr.indexOf(checkArr[k].value),1);
                checkArr[k].checked = false;
               
            }
            document.getElementById("deleteBook").disabled = true;
        }
    }

    function userSelectChanged(field){

        if($(field).is(":checked") == true){
            if(userIdArr.indexOf($(field).val()) == -1) userIdArr.push($(field).val());
        }else if($(field).is(':checked') == false){
            if(userIdArr.indexOf($(field).val()) > -1){
                userIdArr.splice(userIdArr.indexOf($(field).val()),1);
            }
        }
        var userChkSelected = false;
        var checkArr = document.getElementsByClassName("selectAll");
        if(!checkArr.checked){
            document.getElementById("selectAll").checked = false;
        }
        for(var k = 0; k < checkArr.length; k++){
            if(checkArr[k].checked){
                document.getElementById("deleteBook").disabled = false;
                userChkSelected = true;
                break
            }
        }

        if(!userChkSelected){
            document.getElementById("deleteBook").disabled = true;
            document.getElementById("selectAll").checked = false;
        }
    }

</script>

<script>
    $(document).ready(function(){
        $("#batchName").keyup(function () {
            $("#errorClassName").addClass('hidden');
        });

        $(".form-control-modifier").on('focus', function (){
            $(this).parent().addClass('is-focused');
        }).on('blur', function () {
            $(this).parent().removeClass('is-focused');
        });
    });
</script>
