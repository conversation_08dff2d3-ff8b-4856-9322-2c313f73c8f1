<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <title>Wonderslate | Digital Library</title>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
    <asset:stylesheet href="wonderslate/demoRegistration.css"/>
    <style>
        input::-webkit-input-placeholder {
            color: rgba(68, 68, 68, 0.81) !important;
            font-size: 13px !important;
        }
        input::-moz-placeholder {
            color: rgba(68, 68, 68, 0.81) !important;
            font-size: 13px !important;
        }
        input:-ms-input-placeholder {
            color: rgba(68, 68, 68, 0.81) !important;
            font-size: 13px !important;
        }
        input:-moz-placeholder {
            color: rgba(68, 68, 68, 0.81) !important;
            font-size: 13px !important;
        }
        /* Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Firefox */
        input[type=number] {
            -moz-appearance: textfield;
        }
    </style>
</head>

<body class="digital-library-page">
    <header class="row justify-content-md-between justify-content-center m-0 py-3 px-4 px-md-5">
        <div class="digital-library-logo text-center">
            <span class="logo"><img src="${assetPath(src: 'ws/ws-digital-library-logo.svg')}" alt="Wonderslate Logo"></span>
            <span class="divider mx-3 mx-md-4"><img src="${assetPath(src: 'ws/chapter-line.png')}"></span>
            <span class="tagline">Registration Portal</span>
        </div>
    </header>
    <div class="curve-bg">
        <img src="${assetPath(src: 'ws/curve-bg-purple.svg')}">
    </div>

    <section class="banner_wrap p-4 p-md-5 mt-0 mt-md-4 mb-4">
        <div class="row mt-md-5">
            <div class="banner_info col-12 col-md-6">
                <h2>We bring you India's first</h2>
                <h2 class="mb-3"><strong>Smart Digital Library!</strong></h2>
                <p>Personalised Cloud-Based Digital Library <br>for every Institution.</p>
                <div class="icon-lists d-flex flex-wrap align-items-center justify-content-around mt-5 mb-4 mb-md-5">
                    <div class="icon-list">
                        <img src="${assetPath(src: 'ws/icon-cloud-based.svg')}">
                        <p>Cloud<br>Based</p>
                    </div>
                    <div class="icon-list">
                        <img src="${assetPath(src: 'ws/icon-institutional-repository.svg')}">
                        <p>Institutional<br>Repository</p>
                    </div>
                    <div class="icon-list">
                        <img src="${assetPath(src: 'ws/icon-remote-access.svg')}">
                        <p>Remote<br>Access</p>
                    </div>
                    <div class="icon-list">
                        <img src="${assetPath(src: 'ws/icon-drm-secured.svg')}">
                        <p>DRM<br>Secured</p>
                    </div>
                    <div class="icon-list">
                        <img src="${assetPath(src: 'ws/icon-publisher-resources.svg')}">
                        <p>Other Publisher<br>Resources</p>
                    </div>
                </div>
                <div class="d-flex bg-white mb-5 pb-4 mb-md-0 pb-md-0">
                    <p class="p-2">and Other exciting features..</p>
                </div>
            </div>
            <div class="banner_register_form col-12 col-md-6">
                <div class="form-info col-12 col-md-9 col-lg-7 mx-auto text-center p-3 p-md-4">
                    <g:form class="form-horizontal" name="adduser" method="post" autocomplete="off">
                        <div class="form-group p-3 p-md-4 pb-4">
                            <h3 class="py-3"><strong>Yes, I want a demo!</strong></h3>
                            <input type="text" class="form-control mt-2" id="name" name="name" placeholder="Name" required>
                            <input type="text" class="form-control mt-2" id="schoolName" name="schoolName" placeholder="Institution's Name" required>
                            <input type="number" class="form-control mt-2" name="mobile" id="mobile" placeholder="Mobile Number" required>
                            <div class="alert-mobile border-0 mb-0 mt-2 p-0" style="display: none;font-size: 15px;color:red">Please enter a valid Mobile Number!</div>
                            <input type="email" id="email" class="form-control mt-2" name="email" placeholder="Email Address" required>
                            <div class="alert border-0 mb-0 mt-2 p-0" style="display: none;font-size: 15px;color:red">Please enter all Fields!</div>
                        </div>
                        <button type="button" class="btn btn-primary submit-btn" onclick="formSubmits()"> Submit</button>
                    </g:form>
                    <div class="bottom-image pt-4">
                        <img src="${assetPath(src: 'ws/form-bottom-img.svg')}">
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
    var flds = new Array (
        'name',
        'email',
        'schoolName',
        'mobile'
    );

    function formSubmits() {
        if (validate()) {
            var name = document.adduser.name.value;
            var email = document.adduser.email.value
            var schoolName = document.adduser.schoolName.value;
            var mobile = document.adduser.mobile.value;

           if(mobile.length === 10) {
               $("#mobile").css('border', '2px solid green');
               $(".alert-mobile").hide();
                swal({
                    title: "Success!",
                    text: "Your information submitted successfully.",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#27AE60",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function () {
                    <g:remoteFunction controller="log" action="addEnquiryFormRecord"  onSuccess='enquired(data);'
              params="'name='+name+'&email='+email+'&schoolName='+schoolName+'&mobile='+mobile"/>
                });
            }
           else{
                   $("#mobile").css('border', '2px solid rgba(240, 90, 40, 0.5)');
                   $(".alert-mobile").show();
           }
        }
    }
function enquired(data){
        window.location = "/books/index";
        $("form").trigger("reset");
        console.log(data);
}
    function validate(){
        var allFilled=true
        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '2px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '2px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                $('.alert').hide();
                $("#"+flds[i]).css('border', '2px solid green');
                $("#"+flds[i]).css('border', '2px solid green');
            }
        }
        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#email").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");
            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                document.getElementById('email').style.border = 'rgba(240, 90, 40, 0.5)';
                $('.alert').html('Please enter a valid email address.').show();
                return false;
            }
        }
        return allFilled;
    }

</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<asset:javascript src="landingpage/popper.min.js" async="true"/>
<asset:javascript src="landingpage/bootstrap.min.js" async="true"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
</body>
</html>
