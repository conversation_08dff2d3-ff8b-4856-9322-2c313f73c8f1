<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<style>
.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
}

table.dataTable {
    width: 100% !important;
}

    #currentDate {
        font-weight: bold;
        text-align: center;
        font-size: 20px;
    }
    .publishing_sales #content-books #salesData_filter input {
        width: auto;
    }
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-4">
                            <label for="bookId"><strong>Package course Id:</strong></label>
                            <small class="text-secondary"><em>(comma seperated package course ids for mutilpe courses)</em></small>
                            <input type="text" class="form-control" id="bookId"   autocomplete="off">
                        </div>
                        <div class="form-group col-md-6 mt-2">
                            <button type="button" id="search-btn" onclick="saleSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                            <button type="button" id="download-btn" class="btn btn-lg btn-primary ml-3 col-3">Download</button>
                        </div>
                    </div>
                    <div class="col-md-12 mt-5">
                        <table id="salesData" class="table table-striped table-bordered dt-reponsive nowrap" style="display: none;">
                            <thead>
                            <div id="currentDate"  style="display: none;"></div>
                            <tr class="bg-primary text-white">
                                <th>Package Course Id</th>
                                <th>Package Course Title</th>
                                <th>Main Course Id</th>
                                <th>Main  Course Title</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>

                </div>
            </div>
        </div>
    </div>
</div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>
        $('#download-btn').on('click', function() {
            if (document.getElementById("bookId").value === "") {
                document.getElementById("errormsg").innerHTML = "Please enter the Package Course ID."
                $("#errormsg").show();
                $("#salesData").hide();

            } else {
                $("#errormsg").hide();
                window.location.href = "/log/packageBooksReport?download=true&packageCourseId=" + $('#bookId').val();
            }
        });
    </script>
</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#salesData').hide();
    function saleSearch() {

        if (document.getElementById("bookId").value == "") {
            document.getElementById("errormsg").innerHTML = "Please enter the Package Course ID."
            $("#errormsg").show();
            $("#salesData").hide();
            $("#currentDate").hide();
            $("#salesData_wrapper").hide();
        } else {

            if ($.fn.dataTable.isDataTable('#salesData')) {
                $('#salesData').DataTable().destroy();
            }

            var date=momnt().format("MMMM Do YYYY");
            document.getElementById("currentDate").innerHTML = date;
            $("#currentDate").show();
            $("#errormsg").hide();
            $('#salesData').show();
            $('#salesData').DataTable({
                //'responsive': true,
                "scrollX": true,
                'destroy': true,
                //'processing': true,
                'serverSide': true,
                // 'searching': false,
                'ordering': false,
                'retrieve': true,
                'ajax': {
                    'url': 'packageBooksReport',
                    'type': 'GET',
                    'data': function (outData) {
                        outData.packageCourseId = $('#bookId').val();
                        return outData;
                    },
                    dataFilter: function (inData) {
                        //console.log(inData);
                        return inData;
                    },
                    error: function (err, status) {
                        console.log(err);
                    },
                },
                'columns': [
                    {
                        'data': 'packagebookId'
                    },

                    {
                        'data': 'packageBooktitle'
                    },
                    {
                        'data': 'mainBookId'
                    },
                    {
                        'data': 'mainBookTitle'
                    },
                ],

            });
        }
    }

</script>
</body>
</html>
