<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm my-5 px-5" style="min-height: calc(100vh - 160px);">
    <div class='row m-0 px-5'>
        <div class='col-md-12 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <div class="form-group">
                    <div class="flex_st">
                <div class="dropdown">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                        SELECT STATUS
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="javascript:showAllUsers('active')">Active</a>
                        <a class="dropdown-item" href="javascript:showAllUsers('completed')"> Completed</a>
                    </div>
                </div>
                    </div>
                </div>

                <div id="batchUsers" style=""></div>
            </div>
        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>


    function showAllUsers(actionMode){
            <g:remoteFunction controller="log" action="issueList"  params="'actionMode='+actionMode" onSuccess = "quizissuesList(data,actionMode);"/>

    }

    function quizissuesList(data,actionMode){
        if(data.status=="OK") {
            var htmlStr = '';
            htmlStr += "<div class='text-center mb-3'><h2>QUIZ ISSUES</h2></div>\n" +
                "                    <table class='table table-hover table-bordered'>\n" +
                "                        <tr class='bg-primary text-white'>\n" +
                "                            <th>Title</th>\n" +
                // "                            <th>Chapter Description</th>\n" +
                "                            <th>Resource Name</th>\n" +
                "                                <th>Issue Description</th>\n" +
                "                            <th>Issues</th>\n" +
                "                              <th>User Name</th>\n" +
                "                                  <th style='width: 120px;'>Date Created</th>\n";
            if (actionMode == 'active') {
                htmlStr += "<th>Action</th>\n";
            }
            htmlStr += "</tr>\n";

        }

        if(data.status=="OK"){
            var users = data.quizlist;
            for(i=0;i<users.length;i++){
                htmlStr +="<tr class='remove"+[i]+"'><td style='text-transform:capitalize;'>"+users[i].title+"</td>"+
                    // "<td>"+users[i].chapterDesc+"</td>" +
                    "<td>"+users[i].resourceName+"</td>" +
                    "<td>"+users[i].issues+"</td>" +
                    "<td>"+users[i].issuesLists+"</td>" +
                    "<td>"+users[i].username+"</td>" +
                    "<td>"+users[i].dateCreated+"</td>";
                if(actionMode=='active') {
                    htmlStr += "<td><input type='checkbox' id='myCheck"+ (i) +"' name='name" + (i) + "' onclick='myFunction(" + users[i].id + ","+i+")'>&nbsp;Mark as completed</td>";
                }
                htmlStr += "</tr>";
            }
            htmlStr +="</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;

        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found";
        }
    }

    function myFunction(id,i) {
        // Get the checkbox
        var checkBox = document.getElementById("myCheck"+i);
        // If the checkbox is checked, display the output text
        if (checkBox.checked == true){
            <g:remoteFunction controller="log" action="issueUpdate" params="'id='+id"/>
            alert("Updated Successfully!")
            showAllUsers('active');
        }
    }
</script>


</body>
</html>