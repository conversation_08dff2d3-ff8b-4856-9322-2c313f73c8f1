<!DOCTYPE html>
<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <title><%= title!=null?title:"Best Books for CBSE | ICSE ISC | JEE Main & Advanced | NEET | UPSC | CUET \n" +
          "| NDA CDS | GATE | CAT | Olympiad | UGC NET | CLAT - Oswaal Books" %></title>
  <%if(seoDesc!=null){%>
  <meta name="description" content="${seoDesc}">
  <%}else{%>
  <meta name="description" content="Latest Sample Papers, Question Banks, Previous Years Solved Papers  CBSE | ICSE ISC | JEE Main & Advanced | NEET | UPSC | CUET | NDA CDS | GATE | CAT | Olympiad | UGC NET | CLAT.">
  <%}%>

  <meta charset="utf-8">
  <%if(keywords!=null){%>
  <meta name="keywords" content="${keywords}"/>
  <%}else{%>
  <meta name="keywords" content="CBSE Books, CBSE Question Bank, CBSE Sample Paper, CBSE Previous Years Solved Paper, ICSE Books, ICSE Question Bank, ICSE Sample Paper, ICSE Previous Years Solved Paper, ISC Books, ISC Question Bank, ISC Sample Paper, ISC Previous Years Solved Paper, NCERT Books, NCERT Question Bank, CUET Books, CUET Question Bank, CUET Sample Paper, CUET Previous Years Solved Paper, JEE Main Books, JEE Main Sample Paper, JEE Main Previous Years Solved Paper, JEE Advance books, JEE Advance Sample Paper, JEE Advance Previous Years Solved Paper, NEET books, NEET Question Bank, NEET Sample Paper, NEET Previous Years Solved Paper, GATE books, GATE Previous Years Solved Paper, UPSC Books, UPSC Sample Paper, UPSC Previous Years Solved Paper, UGC NET books, UGC NET Question Bank, UGC NET Previous Years Solved Paper, CLAT Books, CLAT Sample Paper, CAT Books, CAT Question Bank, CAT Sample Paper, Olympiad Books, Olympiad Previous Years Solved Paper, Karnataka SSLC books, Karnataka SSLC Question Bank, Karnataka SSLC Sample Paper, Karnataka SSLC Previous Years Solved Paper, Karnataka PUC books, Karnataka PUC Question Bank, Karnataka PUC Sample Paper, Karnataka PUC Previous Years Solved Paper." />
  <%}%>
  <meta name="generator" content="OswaalBooks" />
  <meta name="viewport" content="initial-scale=1.0, maximum-scale=5.0" />
  <meta name="author" content="webmaster - Oswaal Books and Learning Pvt Ltd">
  <meta name="Oswaal Books and Learning Pvt Ltd" content="https://oswaalbooks.com">
  <meta name="subject" content="Book Store Online : Buy Books Online from Oswaal Books Store">
  <meta name="keyphrase" content="Medical Exam Books, Board exams books, CBSE Exam Books, UGC Net, Air Force Books, state exam books, Govt Exam Books, NDA Exam Books, Bank Po Books, Entrance Exam Books, Engineering Books, Exam Books, General Books, General English Books, General Knowledge Books, NDA & CDS Books, SBI exam books, competition books in Hindi, ssc competition books, civil service books, banking Exams books, Gate, Teacher exam Books, buy books online, shop books online, online shopping for books, book shop, bookstore, online bookstore, online book shop india, books online, online book store, online bookstore india, Competitive Exam Book.">
  <meta name="abstract" content="Find large collection of Entrance Exam Books for engineering, medical, Banking, school and other Exam.">
  <link rel="shortcut icon" href="${assetPath(src: 'oswaal/favicon.ico')}" type="image/x-icon"/>
  <link rel="icon"  href="${assetPath(src: 'oswaal/favicon.ico')}" type="image/x-icon">
  <link rel="android-touch-icon" href="${assetPath(src: 'oswaal/favicon.ico')}"/>
  <link rel="windows-touch-icon" href="${assetPath(src: 'oswaal/favicon.ico')}" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>
  <!-- Bootstrap CSS -->
  <asset:stylesheet href="arihant/style.css"/>
  <asset:stylesheet href="arihant/responsive.css" />
  <asset:stylesheet href="font-awesome.min.css"/>
  <asset:stylesheet href="arihant/animate.css"/>
  <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
  <asset:stylesheet href="/assets/katex.min.css"/>

  <asset:stylesheet href="wonderslate/material.css" async="true"/>
  <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
  <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
  <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
  <asset:stylesheet href="oswaal/oswaalStyles.css" async="true"/>

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,700|Oswald:200,300,400,500,600,700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Fira+Sans:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <asset:javascript src="jquery-1.11.2.min.js"/>
  <script src="/assets/katex.min.js"></script>
  <script src="/assets/auto-render.min.js"></script>
  <!-- General styles for admin & user pages -->
  <%if("true".equals(commonTemplate)){%>
  <asset:stylesheet href="oswaal/oswaalTemplate.css" async="true"/>
  <% }%>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-VPE5331MWM"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-VPE5331MWM');
    gtag('config', 'G-2Y9D9ELSN2');  //WS GA code
  </script>
</head>
<body class="arihant oswaal_books">
<sec:ifNotLoggedIn>
  <g:render template="/books/signIn"></g:render>
</sec:ifNotLoggedIn>
<style>
.menu-wrp-all-users-com ul li a{
  font-size: 16px !important;
  padding: 10px !important;
  text-transform:unset !important;
}
</style>
<style>
.menu-wrp-all-users-com ul li a{
  font-size: 16px !important;
  padding: 10px !important;
  text-transform:unset !important;
}
</style>
<%if(params.tokenId==null&&session["appType"]==null){%>
  <header class="oswaal__header">
    <nav class="oswaal__header-nav" id="nav">
      <div class="logoAndCart">
        <a href="/oswaal/index" title="OswaalBooks"><img src="${assetPath(src: 'oswaal/oswaal-logo.png')}" class="logoImgOswaal" alt="Oswaal Logo" width="200px" ></a>
        <span class="link-menu-li d-block d-md-none navbar_cart"><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon" title="cart"><img src="${assetPath(src: 'arihant/shopping-cart.png')}" alt="oswaal cart" style="margin-top: -10px;"/><span class="text-dark">Cart</span> <span class="cart_count" style="right: 0 !important;margin: 0;" id="navbarCartCount">0</span></a></span>
      </div>
      <div class="d-flex justify-content-start global-search">
        <i class="fa-solid fa-bars d-block d-md-none" id="hamburgerMenuIconOpen"></i>
        <form class="d-flex rounded rounded-modifier">
          <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
          <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="submitSearchHeader()" id="search-btn-header"><i class="material-icons">search</i></button>
        </form>
      </div>
      <ul class="oswaal__header-nav__list" id="ulList">
        <li class="mobMenuDrawerClose">
          <i class="fa-solid fa-xmark d-block d-md-none" id="hamburgerMenuIconClose"></i>
        </li>
        <li class="link-menu-li d-block"><a href="/oswaal/store" class="menu-link-anchor"><span>Store</span></a></li>
        <li class="link-menu-li d-block navbar_cart"><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon"><img src="${assetPath(src: 'arihant/shopping-cart.png')}" alt="oswaal cart" style="margin-top: -10px;"/><span class="text-dark">Cart</span> <span class="cart_count" id="navbarCartCount">0</span></a></li>
        <sec:ifNotLoggedIn>
          <li class="none-add-class-responsive"><a href="javascript:loginOpen()"><span class="">Login</span></a></li>
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
          <li class="none-add-class-responsive d-block"><a   href="/wsLibrary/myLibrary"><span class="">My Books</span></a></li>
        </sec:ifLoggedIn>
        <sec:ifAllGranted roles="ROLE_FINANCE">
          <li class="header-menu-item d-block">
            <a href="/publishing-sales" class="header-menu-item-link">Sales</a>
          </li>
        </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_EXTERNAL_SALES_VIEWER">
          <li class="header-menu-item d-block">
            <a href="/reports/externalReportInput" class="header-menu-item-link">Integration Sales</a>
          </li>
        </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
          <li class="header-menu-item d-block">
            <a href="/wsshop/orderManagement" class="header-menu-item-link">Order Management</a>
          </li>
          <li class="header-menu-item d-block">
            <a href="/usermanagement/addUserAndBooks" class="header-menu-item-link">User Upload</a>
          </li>
        </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
          <li class="nav-item dropdown d-block">
            <a class="dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
              Publishing
            </a>
            <div class="dropdown-menu">
              <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
              <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tags</a>
              <a class="dropdown-item" href="/wonderpublish/manageExams">Manage MCQs Templates</a>
            </div>
          </li>
        </sec:ifAllGranted>
        <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN,ROLE_CLIENT_ORDER_MANAGER">
          <li class="nav-item dropdown d-block">
            <a class="dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
              Admin
            </a>
            <div class="dropdown-menu dropdown-menu-right">
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/admin/priceList">Price List</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/excel/fileUploader">File Uploader</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/admin/discountManager">Discount Management</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_DELETE_USER">
                <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                <a class="dropdown-item" href="/institute/admin">eClass+</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/institute/libAdmin">Library Management</a>
              </sec:ifAllGranted>
              <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
              </sec:ifAnyGranted>
              <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
              </sec:ifAnyGranted>
              <sec:ifAllGranted roles="ROLE_PUBLISHER">
                <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                <a class="dropdown-item" href="/log/notification">Notification</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                <a class="dropdown-item" href="/log/deleteUserBooks">User Books </a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                <a class="dropdown-item" href="/log/deleteUserBooks">User Books </a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                <a class="dropdown-item" href="/log/userAccess">User Access</a>
              </sec:ifAllGranted>

              <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
              </sec:ifAllGranted>
              <%if(session["userdetails"].publisherId!=null){%>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/publisherManagement/publisherReport">Publisher Report</a>
              </sec:ifAllGranted>
              <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                <a class="dropdown-item" href="/admin/accessCodeUsage">Access Code Report</a>
              </sec:ifAllGranted>
              <%}%>
              <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                <a class="dropdown-item" href="/institute/userManagement">Library Management</a>
                <a class="dropdown-item" href="/institute/manageInstitutePage">Institute Page Management</a>
                <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
              </sec:ifAllGranted>
              <sec:ifAnyGranted roles="ROLE_BOOK_CREATOR,ROLE_DIGITAL_MARKETER">
                <a class="dropdown-item" href="/wonderpublish/bannerManagement">Banner Management</a>
              </sec:ifAnyGranted>
            </div>
          </li>
        </sec:ifAnyGranted>
        <li class="mobCatAcc">

        </li>
        <sec:ifLoggedIn>
          <li class="bottomLinks">
            <li class="myacc"><a href="/creation/userProfile"><i class="fa-solid fa-user d-inline d-md-none mr-1"></i>My Account</a></li>
            <li class="logoutBtn"><a href="javascript:logout()"><i class="fa-solid fa-right-from-bracket d-inline d-md-none"></i> Logout</a></li>
          </li>
        </sec:ifLoggedIn>
      </ul>
    </nav>
  <div class="headerCategoriesMenu">
    <div class="header__categories">
      <ul class="header__categories-list" id="catList"></ul>
    </div>
    <div class="header__submenus" id="headerCategorySubMenus">
      <p class="text-center subMenuTitleText"><strong id="subMenuTitle"></strong></p>
      <div class="submenuLists">
        <ul class="syllabusList"></ul>
        <div class="listScrollArrow">
          <div class="listScrollArrowBall"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-backdrop fade show headerBackdrop d-none" id="categoryMenuBackdrop"></div>
  </header>

<% }%>
<script>
  var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  function logout(){
    window.location.href = '/logoff';
  }
  var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  const hamburgerMenuIcon = document.getElementById('hamburgerMenuIconOpen');
  const hamburgerMenuIconClose = document.getElementById('hamburgerMenuIconClose');
  function updateHeaderCategories(){
    let catItems="";
    const catList = levelTags;
    const catListElement = document.getElementById('catList');
    catList.forEach((cat,index)=>{
      const catName = cat.level.replaceAll(' ','-');
      catItems += "<li class='header__categories-list__item'>" +
              "<a href='/${session["entryController"]}/store?level="+catName+"' class='headerLevel' target='_blank'>"+cat.level+"</a>"+
              "</li>";
    });
    catListElement.innerHTML = catItems;
    const hoverElement = document.querySelectorAll('.headerLevel');
    const showDiv = document.getElementById('headerCategorySubMenus');
    const handleMouseLeave = () => {
      addRemoveBackDrop('hide',showDiv);
    };
    hoverElement.forEach(elem=>{
      elem.addEventListener('mouseover', () => {
        updateSubMenus(elem.textContent)
        addRemoveBackDrop('show',showDiv);
      });
      showDiv.addEventListener('mouseout', handleMouseLeave);
      showDiv.addEventListener('mouseover', ()=>{
        addRemoveBackDrop('show',showDiv);
      });
      document.querySelector('#nav').addEventListener('mouseover',handleMouseLeave);
      document.addEventListener('click',handleMouseLeave);
    });
  }
  function updateSubMenus(hoveredText){
    let syllabusListHTML = "";
    const syllabusListDiv =  document.querySelector('.syllabusList');
    const headerCategorySubMenus = document.getElementById('headerCategorySubMenus');
    document.getElementById('subMenuTitle').innerHTML = hoveredText;
    const listScrollArrow = document.querySelector('.listScrollArrow');
    let syllabusCount = 0;
    syllabusTags.forEach(syllabus=>{
      if (syllabus.level === hoveredText){
        let syllabusLink = syllabus.level.replaceAll(" ",'-');
        syllabusLink += "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-');
        syllabusListHTML += "<li><a href='/${session["entryController"]}/store?level="+syllabusLink+"' target='_blank'>"+syllabus.syllabus+"</a></li>";
        syllabusCount++;
      }
    })
    syllabusListDiv.innerHTML = syllabusListHTML;
    setTimeout(()=>{
      if (headerCategorySubMenus.offsetHeight >= 500 && syllabusCount >= 30){
        listScrollArrow.style.display = 'flex';
      }else{
        listScrollArrow.style.display = 'none';
      }
    })
  }
  function addRemoveBackDrop(action,showDiv){
    const categoryMenuBackdrop = document.getElementById('categoryMenuBackdrop');
    const headerElement = document.querySelector('#nav');
    const headerCategoriesMenu = document.querySelector('.headerCategoriesMenu');
    if(action=='show'){
      showDiv.style.display = 'block';
      showDiv.style.opacity = '1';
      categoryMenuBackdrop.classList.remove('d-none');
      categoryMenuBackdrop.style.top = headerElement.offsetHeight + headerCategoriesMenu.offsetHeight+'px';
    }else if(action=='hide'){
      showDiv.style.display = 'none';
      showDiv.style.opacity = '0';
      categoryMenuBackdrop.classList.add('d-none');
    }
  }
  if (levelTags.length>0){
    updateHeaderCategories();
  }else {
    document.querySelector('.headerCategoriesMenu').classList.add('d-none');
  }
  if ($(window).width() < 767){
    let accordionHTMl = "";
    let accID = "";
    accordionHTMl +="<div class='accordion' id='accordion'>";
    levelTags.forEach((cat,index)=>{
      accID = index;
      accordionHTMl += "<div class='card mb-3'>" +
              "<div class='card-header p-2' id='heading-"+index+"'>";
      if(index==0){
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"' title='levelbtn'>";
      }else{
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0 collapsed' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"' title='levelbtn'>";
      }
      accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level.replaceAll(" ",'-')+"' target='_blank' title='level'><p>"+cat.level+"</p></a>"+
              "</button>" +
              "</div>";
      if (index==0){
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse show' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }else{
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }
      accordionHTMl +="<div class='card-body'>";
      syllabusTags.forEach((syllabus,index)=>{
        if (cat.level === syllabus.level){
          accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level+"&"+ "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-')+"'  target='_blank' title='syllabus'><p>"+syllabus.syllabus+"</p></a>";
        }
      })
      accordionHTMl +="</div>"+
              "</div>"+
              "</div>";
    })
    accordionHTMl+="</div>";
    document.querySelector('.mobCatAcc').innerHTML = accordionHTMl;
  }
</script>
