
<%@ page import="com.wonderslate.data.ChaptersMst" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
    .quizWrapper{
        min-height: 70vh;
    }
</style>
<section class="quizWrapper">
    <div class="container">
<div class="row">
    <div class="col-12 col-lg-6">
        <form>
            <div class="form-group">
                <label>Quiz Id:</label>
                <input type="number" class="form-control" placeholder="Enter QuizId" id="myQuizId">
            </div>
            <div class="form-group">
                <label>Resource Id:</label>
                <input type="number" class="form-control" placeholder="Enter resId" id="myResId">
            </div>
            <div class="form-group">
                <label>Quiz Type:</label>
                <select class="form-control" id="quizType">
                    <option value="prepjoy">Prepjoy</option>
                    <option value="practice">Practice</option>
                    <option value="testSeries">Test without Timer</option>
                    <option value="test">Custom Timer Test</option>
                </select>
            </div>
            <button type="button" class="btn btn-primary" onclick="submitQuiz()">Submit</button>
        </form>
    </div>
</div>
</div>
</section>
<script>

    function submitQuiz(){
        var quizId=document.getElementById('myQuizId').value;
        var resId=document.getElementById('myResId').value;
        var quizType=document.getElementById('quizType').value;
        if(quizId =='' || quizId==''){
            alert('Please enter QuizId and ResId');
        }
        else {
            window.location.href = '/prepjoy/prepJoyGame?quizId=' + quizId + '&resId=' + resId + '&quizType=' + quizType;
        }
    }
</script>
<g:render template="/${session['entryController']}/footer_new"></g:render>
</html>