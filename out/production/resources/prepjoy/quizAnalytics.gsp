<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="prepJoy/prepjoyWebsites/analyticsStyles.css" async="true" media="all"/>


<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="container an-header">
    <div class="daily__test-header mt-2 mt-lg-5">
        <div class="daily__test-header__title d-flex align-items-center">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <h3 class="text-primary-modifier">
                <strong>Analytics</strong>
            </h3>
        </div>
        <div id="history__header-title" class="text-center ">

        </div>
        <div id="history__header-content" class="text-center mt-3">
            <div class="d-none ds">
                <h6 class="dt dtd sml-font">Date</h6>
                <h6 class="dt sml-font pl-5">Name</h6>
                <h6 class="scr sml-font">Score</h6>
            </div>
        </div>
    </div>
</div>

<div class="container listContainer">

    <div id="historyList">

    </div>
    <button class="btn btn-primary d-none justify-content-center align-items-center mt-4" style="margin: 0 auto;" onclick="paginate()" id="moreBtn">Load More</button>
</div>

<div class="hidden-footer">
<g:render template="/${session['entryController']}/footer_new"></g:render>
</div>
<script>
    var siteId = "${session.siteId}";
    var pageNo = 1;
    var allFetchedData;
    var userHistoryArr = [];
    var lastQuizData;

    function getLastQuizDetails(){
        <g:remoteFunction controller="analytics" action="getUsersLastQuiz" params="'siteId='+siteId" onSuccess="updateLastQuiz(data)" />
    }
    getLastQuizDetails();

    function getUsersHistoryForAllQuiz(){
        showLoader();
        <g:remoteFunction controller="analytics" action="getUsersHistoryForAllQuizzes" params="'siteId='+siteId+'&startIndex='+pageNo" onSuccess="showUserHistoryUI(data)" />
    }

    function showUserHistoryUI(data){
        allFetchedData = data;
        if (data.quizHistory!=null){
            userHistoryArr = userHistoryArr.concat(data.quizHistory);
            userHistoryArr.unshift(lastQuizData[0]);
        }else{
            userHistoryArr.unshift(lastQuizData[0]);
        }

        document.getElementById('history__header-title').innerHTML = "<div class='totalQuiz'> Total Quizzes Attempted :"+data.totalQuizzesAttempted+"</div>";

        var uHtml = "";
        if (userHistoryArr.length > 19){
            document.getElementById('moreBtn').classList.remove('d-none');
            document.getElementById('moreBtn').classList.add('d-flex');
        }
        for (var u=0;u<userHistoryArr.length;u++){
            var d = new Date(userHistoryArr[u].dateCreated);
            var options = {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
            };

            var formattedDate = d.toLocaleDateString('en-ZA', options);
            var [date,month,year] = formattedDate.split(' ');
            uHtml += "<div class='history__card' id='history__card-"+(u+1)+"' onclick='getHistoryData("+userHistoryArr[u].quizRecId+")'>\n"+
                        "<div class='history__card-wrap d-flex align-items-center justify-content-between'>\n"+
                            "<div class='d-flex align-items-center item1'>\n"+
                                "<div class='datesDiv'>"+
                                    "<p>"+month+"</p>"+
                                    "<p class='dateDigit'>"+date+"</p>\n"+
                                    "<p>"+year+"</p>\n"+
                                "</div>"+
                            "</div>\n"+
                            "<div class='text-center'>"+
                                 "<p class='qName'>"+userHistoryArr[u].quizName+"</p>"+
                                 "<h6>Total Points : "+userHistoryArr[u].points+"</h6>\n"+
                            "</div>"+
                            "<div class='d-flex align-items-center justify-content-end '>\n"+
                                "<button class='btn getHistoryData-btn' style='font-size: 18px' ><i class='fa-solid fa-chevron-right'></i></button>\n"+
                            "</div>\n"+
                        "</div>";
            uHtml += "</div>";
        }

        document.getElementById('historyList').innerHTML = uHtml;
        hideLoader();
        if (userHistoryArr.length > 20){
            var targetElm = document.querySelector('#history__card-'+(pageNo+20));
            pageNo > 19 ? targetElm.scrollIntoView() : ''
        }
    }

    function updateLastQuiz(data){
        if (data.lastQuizDetails!="no records"){
            lastQuizData = JSON.parse(data.lastQuizDetails);
            getUsersHistoryForAllQuiz();
        }else{
            document.getElementById('historyList').innerHTML = "<h4 class='text-center'>No Records Found</h4>";
        }
    }

    function getHistoryData(id){
        window.open('/prepjoy/prepJoyGame?siteId='+siteId+'&quizRecId='+id+'&historyPage=true&learn=false','_blank');
    }

    function paginate(){
        pageNo+=19;
        getUsersHistoryForAllQuiz();
    }

    function showLoader(){
        document.querySelector('.loading-icon').classList.remove('hidden');
    }
    function hideLoader(){
        document.querySelector('.loading-icon').classList.add('hidden')
    }

</script>