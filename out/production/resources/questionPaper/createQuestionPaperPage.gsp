<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h2 class="text-center">Create Question Paper</h2>
                     <!-- take input for name of the question paper -->
                    <div class="form-group">
                        <label for="qpName">Name of the Question Paper <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="qpName" name="qpName" required>
                    </div>
                    <!-- tke input for number of sets, let it be dropdown from 1 to 3 -->
                    <div class="form-group">
                        <label for="sets">Number of Sets <span class="text-danger">*</span></label>
                        <select class="form-control" id="sets" name="sets" required>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                    <!-- Question Paper Pattern Dropdown -->
                    <div class="form-group">
                        <label for="pattern">Select Question Paper Pattern <span class="text-danger">*</span></label>
                        <select class="form-control" id="pattern" name="pattern" required>
                            <option value="">Select Pattern</option>
                            <g:each in="${questionPaperPatterns}" var="pattern">
                                <option value="${pattern.id}">${pattern.name}</option>
                            </g:each>
                        </select>
                    </div>

                    <!-- Batch Selection -->
                    <%if(institutes!=null){%>
                    <div class="form-group">
                        <label for="batches">Select Batch <span class="text-danger">*</span></label>
                        <select class="form-control" id="batches" name="batches" required onchange="getSubjects()">
                            <option value="">Select Class / Division</option>
                            <%  institutes.each{ %>
                            <option value="${it.batchId}">${it.name == "Default" ? "All" : it.name}</option>
                            <%}%>
                        </select>
                    </div>

                    <!-- Subject Selection -->
                    <div class="form-group">
                        <label for="subject">Select Subject <span class="text-danger">*</span></label>
                        <select class="form-control" id="subject" name="subject" required onchange="getBooks()"></select>
                    </div>
                    <%}%>

                    <!-- Books List -->
                    <div class="form-group" id="booksContainer" style="display: none;">
                        <label>Select Books</label>
                        <div id="bookList"></div>
                        <button class="btn btn-primary mt-2" onclick="getChapters()">Get Chapters</button>
                    </div>

                    <!-- Chapters List -->
                    <div class="form-group" id="chaptersContainer" style="display: none;"></div>

                    <!-- Generate Button (Hidden initially) -->
                    <button id="generateBtn" class="btn btn-success mt-3" style="display: none;" onclick="generateQuestionPaper()">Generate Question Paper</button>


                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    function getSubjects() {
        let batchId = $('#batches').val();
        if (!batchId) return;
        $.post('/questionPaper/getSubjectsForClass', { batchId: batchId }, function(response) {
            let subjectDropdown = $('#subject');
            subjectDropdown.empty().append('<option value="">Select Subject</option>');
            response.forEach(subject => {
                subjectDropdown.append('<option value="' + subject.subject + '">' + subject.subject + '</option>');
            });
        });
    }

    function getBooks() {
        let batchId = document.getElementById("batches").value;
        let subject = document.getElementById("subject").value;
        if (!subject || !batchId) return;

        $.post('/onlineTest/getUserBooksForTestGenerator', { subject: subject, batchId: batchId }, function(response) {
            let booksContainer = document.getElementById("booksContainer");
            let bookList = document.getElementById("bookList");

            // Clear existing books
            bookList.innerHTML = "";
            booksContainer.style.display = "block";

            // Store book titles in a data attribute for later use
            let bookTitles = {};

            response.books.forEach(function(book) {
                bookTitles[book.id] = book.title; // Store book title

                // Create book checkbox container
                let bookDiv = document.createElement("div");

                // Create book checkbox
                let bookCheckbox = document.createElement("input");
                bookCheckbox.type = "checkbox";
                bookCheckbox.name = "books";
                bookCheckbox.value = book.id;

                // Append checkbox and book title
                bookDiv.appendChild(bookCheckbox);
                bookDiv.appendChild(document.createTextNode(" " + book.title));
                bookList.appendChild(bookDiv);
            });

            // Store book titles for later use in getChapters()
            $("#booksContainer").data("bookTitles", bookTitles);
        });
    }



    function getChapters() {
        let bookIds = $("input[name='books']:checked").map(function() { return this.value; }).get();
        if (bookIds.length === 0) {
            alert("Please select at least one book.");
            return;
        }
       //convert booksIds to comma separated string
        bookIds = bookIds.join(',');
        $.post('/questionPaper/getChaptersForBooks', { bookIds: bookIds }, function(response) {
            let chaptersContainer = document.getElementById("chaptersContainer");
            chaptersContainer.innerHTML = ""; // Clear previous results

            // Ensure response has the correct structure
            if (!response.chaptersByBook) {
                console.error("Unexpected response structure:", response);
                alert("Error fetching chapters. Please try again.");
                return;
            }

            let chaptersByBook = response.chaptersByBook;
            let bookTitles = $("#booksContainer").data("bookTitles"); // Retrieve stored book titles

            Object.keys(chaptersByBook).forEach(function(bookId) {
                let chapters = chaptersByBook[bookId];
                let bookTitle = bookTitles[bookId] ? bookTitles[bookId] : "Book " + bookId; // Get book title

                // Ensure chapters exist and are an array
                if (!Array.isArray(chapters)) {
                    console.error("Unexpected data for bookId:", bookId, chapters);
                    return;
                }

                // Create book title element
                let bookTitleElem = document.createElement("h4");
                bookTitleElem.textContent = bookTitle;
                chaptersContainer.appendChild(bookTitleElem);

                // Create "Select All" checkbox
                let selectAllLabel = document.createElement("label");
                let selectAllCheckbox = document.createElement("input");
                selectAllCheckbox.type = "checkbox";
                selectAllCheckbox.className = "select-all";
                selectAllCheckbox.setAttribute("data-book", bookId);
                selectAllLabel.appendChild(selectAllCheckbox);
                selectAllLabel.appendChild(document.createTextNode(" Select All"));
                chaptersContainer.appendChild(selectAllLabel);

                // Loop through chapters and create checkboxes
                chapters.forEach(function(chapter) {
                    let chapterDiv = document.createElement("div");
                    let chapterCheckbox = document.createElement("input");
                    chapterCheckbox.type = "checkbox";
                    chapterCheckbox.name = "chapters";
                    chapterCheckbox.setAttribute("data-book", bookId);
                    chapterCheckbox.value = chapter.id;
                    chapterDiv.appendChild(chapterCheckbox);
                    chapterDiv.appendChild(document.createTextNode(" " + chapter.name));
                    chaptersContainer.appendChild(chapterDiv);
                });
            });

            // Handle "Select All" functionality
            document.querySelectorAll(".select-all").forEach(function(selectAll) {
                selectAll.addEventListener("change", function() {
                    let bookId = this.getAttribute("data-book");
                    document.querySelectorAll("input[name='chapters'][data-book='" + bookId + "']").forEach(function(chapterCheckbox) {
                        chapterCheckbox.checked = selectAll.checked;
                    });
                });
            });

            // Show generate button only after chapters are listed
            document.getElementById("generateBtn").style.display = "block";
            chaptersContainer.style.display = "block";
        }).fail(function(xhr, status, error) {
            console.error("Error fetching chapters:", xhr.responseText);
        });
    }



    function generateQuestionPaper() {
        let patternId = $('#pattern').val();
        let chapterIds = $("input[name='chapters']:checked").map(function() { return this.value; }).get().join(',');
        let name = $('#qpName').val();
        let noOfSets = $('#sets').val();

        if (!patternId || chapterIds === '') {
            alert("Please select all required fields.");
            return;
        }
        window.location.href = "/questionPaper/generateQuestionPaper?patternId=" + patternId + "&chapterIds=" + chapterIds+ "&name=" + name + "&noOfSets=" + noOfSets;
    }
</script>
</body>
</html>
