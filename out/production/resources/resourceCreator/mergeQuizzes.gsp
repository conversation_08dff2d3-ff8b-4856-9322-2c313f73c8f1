<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="katex.min.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>
<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information ebooks pb-5 mergeQuizzes">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <h3 class="mainText">Merge MCQs</h3>

        <div class="container table-container">
            <p>** You have select at least 2 MCQs to merge.</p>
        <table border="1" cellpadding="2" cellspacing="2">
            <thead class="bg-primary text-white text-center">
            <tr><th>Res Id</th>
                <th>Name</th>
                <th>Select</th>
            </thead>


        <form type="post" action="/resourceCreator/mergeQuizzes" id="mergeForm">
            <input type="hidden" name="chapterId" value="${params.chapterId}">
            <input type="hidden" name="bookId" value="${params.bookId}">
            <input type="hidden" name="resIds" id="resIds">
<%for(int i=0;i<resources.size();i++){%>
   <tr>
        <td>${resources[i].id}</td>
        <td>${resources[i].resourceName}</td>
        <td><input type="checkbox" name="res_${i}" value="${resources[i].id}" id="res_${i}"> </td>
    </tr>

<%}%>
        </table>

<br>
            <div class="form-group">
                <div class=" col-sm-12 text-center">
                    <button type="button" onclick="submitForm()" class="btn btn-primary submitBtn">Submit</button>
                </div>
            </div>
%{--            <button type="button" onclick="submitForm()"  value="Submit" class="submitBtn"> Submit</button>--}%
        </form>
    </div>
</div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    var numberOfResources = "${resources.size()}";
    function submitForm(){

        var numberOfItemsSelected = 0;
        var resIds = "";

        for(var i=0;i<numberOfResources;i++){
            if(document.getElementById("res_"+i).checked) {
                if(numberOfItemsSelected==0) resIds = document.getElementById("res_"+i).value;
                else resIds = resIds+","+document.getElementById("res_"+i).value;
                numberOfItemsSelected++;

            }
        }

        if(numberOfItemsSelected<2){
            alert("Please select atleast 2 MCQs");
        }else{
            document.getElementById("resIds").value=resIds;
            document.getElementById("mergeForm").submit();
        }


    }
</script>
