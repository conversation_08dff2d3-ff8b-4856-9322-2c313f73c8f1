<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-left">Add User</h3>
                    <div class="form-group" id="intrst-area">
                        <div class="form-group col-md-3">
                            <g:select id="siteId" class="form-control w-100 m-0" optionKey="id" optionValue="clientName"
                                      value="" name="siteId" from="${sitesList}" noSelection="['':'Select Site']"/>
                        </div>


                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="userName" id="userName" placeholder="Name">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="tel" class="form-control" name="mobile" id="mobile" placeholder="Mobile Number" minlength="10" maxlength="10" pattern="[0-9]*" oninput="numberOnly(this.id)">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="emailId" id="emailId" placeholder="Email">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="passwordID" id="passwordID" placeholder="Password">
                            </div>
                            <div class="form-group col-md-10">
                                <label for="teacher">Teacher</label>
                                <input type="checkbox" class="form-control" name="teacher" id="teacher" >
                            </div>

                            <button class="btn btn-primary btadd" onclick="javascript:addUser1()">Add User</button>
                            <button class="btn  btadd" onclick="javascript:clearFields()">Clear</button>
                            <div class="form-group col-md-10 mx-auto">
                                <div class="alert alert-sm alert-primary p-2" id="addOrderResult" style="display: none"></div>
                            </div>


                    </div>
                    <h3 class="text-left">Manage User</h3>
                    <div class="form-group col-md-10">
                        <input type="text" class="form-control" name="user" id="user" placeholder="Email/Mobile">
                    </div>
                        <div class="form-group col-md-10">
                            <textarea class="form-control" name="bookId" id="bookId" placeholder="Book Id/s"></textarea>
                        </div>
<div class="form-group col-md-3">
                    <select name="bookType" id="bookType" class="form-control">
                        <option value="eBook">eBook</option>
                        <option value="testSeries">Online test series</option>
                        <option value="bookGPT">iBookGPT</option>
                    </select>
</div>
                        <button class="btn btn-primary btadd" onclick="javascript:updateUser()">Update</button>
                        <div class="form-group col-md-10 mx-auto">
                            <div class="alert alert-sm alert-primary p-2" id="updateUserResult" style="display: none"></div>
                        </div>

                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>

    function addUser1() {
        $("#addOrderResult").html("").hide();

        if (document.getElementById("siteId").selectedIndex == 0) {
            alert("Please select the site.");
            document.getElementById("siteId").focus();

        } else if (document.getElementById("userName").value == "") {
            alert("Please enter name.");
            document.getElementById("userName").focus();

        } else if (!(document.getElementById("mobile").value) && !(document.getElementById("emailId").value)) {
            alert("Please enter mobile number or email id.");
        }
        else if (document.getElementById("passwordID").value == "") {
                alert("Please enter password.");
                document.getElementById("passwordID").focus();

            } else {
                var name = document.getElementById("userName").value;
                var mobile = document.getElementById("mobile").value;
                var email = document.getElementById("emailId").value;
                var password = document.getElementById("passwordID").value;
                var siteId = document.getElementById("siteId").value;
                var teacher = null;
                if (document.getElementById("teacher").checked) {
                    teacher = "true";
                }
                <g:remoteFunction controller="support" action="addUser" params="'mode=submit&name='+name+'&mobile='+mobile+'&email='+email+'&siteId='+siteId+'&password='+password+'&teacher='+teacher " onSuccess = "userAddedNew(data);"/>
                $('.loading-icon').removeClass('hidden');
            }
    }

    function userAddedNew(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("addOrderResult").innerHTML="<p> "+data.Status+"</p>";
        $("#addOrderResult").show();
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    function clearFields(e){
        e.preventDefault()
        document.getElementById("userName").value="";
        document.getElementById("mobile").value="";
        document.getElementById("userEmail").value="";
        document.getElementById("pincode").value="";

    }

    function updateUser(){
        var user = document.getElementById("user").value;
        var bookId = document.getElementById("bookId").value;
        var siteId = document.getElementById("siteId").value;
        var bookType = document.getElementById("bookType").value;
        if(user==""){
            alert("Please enter user email/mobile.");
            document.getElementById("user").focus();
            return;
        }
        if(bookId==""){
            alert("Please enter book id.");
            document.getElementById("bookId").focus();
            return;
        }
        if(siteId==""){
            alert("Please select site.");
            document.getElementById("siteId").focus();
            return;
        }
        if(bookType==""){
            alert("Please select book type.");
            document.getElementById("bookType").focus();
            return;
        }
        <g:remoteFunction controller="support" action="updateUser" params="'user='+user+'&bookId='+bookId+'&siteId='+siteId+'&bookType='+bookType" onSuccess = "userUpdated(data);"/>
        $('.loading-icon').removeClass('hidden');
    }

    function userUpdated(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("updateUserResult").innerHTML="<p> "+data.Status+"</p>";
        if(data.addedBooks){
            document.getElementById("updateUserResult").innerHTML+="<p> Books added: "+data.addedBooks+"</p>";
        }
        $("#updateUserResult").show();
    }
</script>

</body>
</html>
