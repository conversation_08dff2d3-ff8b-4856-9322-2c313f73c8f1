<%
    String levelLabel = "Select Level"
    String syllabusLabel = "Select Board"
    String gradeLabel = "Select Grade"
    String subjectLabel = "Select Subject"
    if(session["levelLabel"]!=null) levelLabel = session["levelLabel"]
    if(session["syllabusLabel"]!=null) syllabusLabel = session["syllabusLabel"]
    if(session["gradeLabel"]!=null) gradeLabel = session["gradeLabel"]
    if(session["subjectLabel"]!=null) subjectLabel = session["subjectLabel"]
%>
<style>
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
    .ebooks .store-list-layout {
        min-height: 3350px;
    }
}
h2{
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 1rem;
}
</style>
<%if("27".equals(""+session["siteId"])){%>
<style>
.minibanner {
    min-height: 100px;
    background: #05001F;
}
.minibannerText  {
    color: white !important;
}
</style>
<section class="page-main-wrapper mdl-js pb-5 pt-4 ebooks">
    <%}else{%>

<section class="page-main-wrapper mdl-js pb-5 ebooks" style="background-color: white">
    <%}%>



    <div class="banner-ws" style="border: 2px">
        <div id="slider-desktop" class="carousel slide w-100" data-ride="carousel">

            <div class="carousel-inner w-100" id="slider-desktop-views">

                <div class='carousel-item w-100 active rounded'>
                    <div class='d-flex justify-content-center align-items-center w-100 no-banners minibanner'>
            <div class='col-12 text-center p-4'>
            <h1 class="minibannerText">${onPageTitle}</h1>
            <p class="minibannerText">${onPageDescription}</p>
            </div>
            </div></div>

            </div>
        </div>
    </div>

    <div class="container"><br>


        <div class="d-none justify-content-center global-search searching-book-store">
            <form class="form-inline col-12 p-0">
                <input type="text" class="form-control form-control-modifier border-0 shadow typeahead w-100" name="search" id="search-book-store" autocomplete="off" placeholder="Search title, subject, publisher, author, ISBN etc.">
                <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="javascript:submitSearchTop();" id="search-btn-store"><i class="material-icons">search</i></button>
            </form>
        </div>

        <sec:ifAllGranted roles="ROLE_WS_SALES_TEAM">
            <div class="text-center mb-4">
                <div class="btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mb-2 mx-2" onclick="downloadCatalogue();">Download Catalogue</div>
            </div>
        </sec:ifAllGranted>

        <div class="store-list-layout d-flex align-items-start mt-md-4">

        <div class="col-12 col-md-4 col-lg-3 d-none justify-content-left ebooks_filter my-3 my-md-0 flex-wrap p-md-3 shadow" id="filters">
            <div class="col-12 d-flex align-items-center pb-2 px-0">
                <p class="mb-0 text-dark pl-0">Filters
                <% if("true".equals(session['prepjoySite'])){%>
                    <img class="mr-3" src="${assetPath(src: 'ws/filterP.svg')}"></p>
                <% } else {%>
                <img class="mr-3" src="${assetPath(src: 'wonderslate/filter.webp')}" width="29" height="19" alt="eBooks Filter Icon"></h5>
                <% }%>
                <button  onclick="resetFilters()" id="resetFilter" class="btn btn-sm bg-transparent px-0">Clear All Filters</button>
            </div>

            <%if(showPublishers!=null&&"true".equals(showPublishers)){%>
            <div class="col-6 col-md-12 pr-2 pl-0">
                <select id="publisher" onchange="publisherChanged(this);" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3" data-bs-display="static" data-dropup-auto="false" data-flip="false" id="dropdownMenuButton" aria-haspopup="false" aria-expanded="false"><option value="" selected="selected">Select Publishers</option></select>
            </div>
            <%}else{%>
            <div id="publisher" style="display: none"></div>
            <%}%>
            <%if("55".equals(""+session["siteId"])){%>
            <div class="col-6 col-md-12 pr-2 pl-0" style="display: none">
                <select id="level" onchange="levelChanged(this);" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3"><option value="" selected="selected">${levelLabel}</option>
                </select>
            </div>
            <%}else{%>
            <div class="col-6 col-md-12 pr-2 pl-0">
                <select id="level" onchange="levelChanged(this);" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3"><option value="" selected="selected">${levelLabel}</option>
                </select>
            </div>
            <%}%>

            <div class="col-6 col-md-12 pr-2 pl-0">
                <select id="syllabus" onchange="syllabusChanged(this)" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3" ><option value="" selected="selected">${syllabusLabel}</option> </select>
            </div>
            <%if(session['wileySite'] ){%>
            <div class="col-6 col-md-12 pr-2 pl-0" style="display: none">
                <select id="grade" onchange="gradeChanged(this)" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3" ><option value="" selected="selected">${gradeLabel}</option> </select>
            </div>
            <div class="col-6 col-md-12 pr-2 pl-0" style="display: none">
                <select id="subject" onchange="subjectChanged(this)" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3"><option value="" selected="selected">${subjectLabel}</option> </select>
            </div>
            <%} else {%>
            <div class="col-6 col-md-12 pr-2 pl-0">
                <select id="grade" onchange="gradeChanged(this)" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3" ><option value="" selected="selected">${gradeLabel}</option> </select>
            </div>
            <div class="col-6 col-md-12 pr-2 pl-0">
                <select id="subject" onchange="subjectChanged(this)" class="form-control form-control-modifier rounded rounded-modifier ebooks-selection mb-3"><option value="" selected="selected">${subjectLabel}</option> </select>
            </div>
            <%}%>

        </div>

        <div class="col-12 col-md-8 col-lg-9 books-list pb-4"  id="searchResults">

            <div class="col-12 text-center text-secondary text-secondary-modifier py-4 searchShowHide" style="display: none;" id="noResultsFound">
                <b>No results found for your search. Please use the filters to find your eBook.</b>
            </div>

            <div id="amazonBooksTitle" class="col-12 pb-3 px-0 px-md-3" style="display: none;">
            </div>

            <div class="col-12 mb-4 pr-0" id="content-data-books-ebooks" style="min-height: 350px;"></div>

            <div id="load-more-button" style="display: none; text-align: center;">
                <button class="view-more btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" id="view-more"> </button>
            </div>

        </div>

        </div>
        <%if(aboutTitle!=null){%>
        <div class="bookDetails__container" style="margin-top: 1.5rem;">
            <section class="ebookFeatures">
                <br> <div>
                <h2>About ${(""+aboutTitle).toUpperCase()}</h2>
                <p id="aboutDescription">${aboutDescription}</p>
            </div>
            </section>
        </div>

        <%}%>
        <%if(publisherDescription!=null&&!"".equals(publisherDescription)){%>
        <div class="bookDetails__container" style="margin-top: 1.5rem;">
            <section class="ebookFeatures">
                <br> <div>
                <h2>About ${publisherName}</h2>
                <p id="publisherDescription">${publisherDescription}</p>
            </div>
            </section>
        </div>

        <%}%>

        <%if("1".equals(""+session["siteId"]) || "27".equals(""+session["siteId"])){%>
        <div class="bookDetails__container" style="margin-top: 1.5rem;">
            <g:render template="/resources/ebookFeatures"></g:render>
        </div>
        <%}%>
    </div>
</section>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<%if(aboutTitle!=null){%>
<script>
    document.getElementById("aboutDescription").innerHTML=document.getElementById("aboutDescription").innerText;
</script>
<%}%>

<%if(publisherDescription!=null&&!"".equals(publisherDescription)){%>
<script>
    document.getElementById("publisherDescription").innerHTML=document.getElementById("publisherDescription").innerText;
</script>
<%}%>
