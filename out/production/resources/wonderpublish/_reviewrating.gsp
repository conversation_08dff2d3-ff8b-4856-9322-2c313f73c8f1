<div class="modal fade"   id="reviewrating" tabindex="-1" role="dialog" aria-labelledby="reviewrating" aria-hidden="true">

    <div class="modal-dialog modal-lg">
        <div class="modal-content ">
            <div class="modal-header sum-modal-header">
                <div class="row" style="margin-top:0px;margin-bottom: 0px">
                    <div class="col-md-12 text-right">
                        <button type="button" class="btn btn-success btn-sm" data-dismiss="modal"  aria-label="Close"> <span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <span class="quizTitle sum-modal-header-text">Review / Rate this book</span>
            </div>
            <div class="modal-body">

                        <div class="row"><div class="col-md-2 col-md-offset-1">
                           Your Rating : </div>
                            <div class="col-md-3" id="ratinginput"> <select name="rating" id="rating" class="form-control text-right">
                                <option value="">Select</option>
                                <option value="0.5">0.5</option>
                                <option value="1">1</option>
                                <option value="1.5">1.5</option>
                                <option value="2">2</option>
                                <option value="2.5">2.5</option>
                                <option value="3">3</option>
                                <option value="3.5">3.5</option>
                                <option value="4">4</option>
                                <option value="4.5">4.5</option>
                                <option value="5">5</option>
                                </select>
                            </div>
                        <div class="col-md-3" id="ratingsubmit"><button type="button" onclick="javascript:submitRating()" class="btn btn-success" >Submit Rating</button></div>
                        </div>
                <div class='row'><br><div class='col-md-10 col-md-offset-2 red' id='ratingalert' style='display: none'>Please select a rating</div></div>

                <div class="row"><div class="col-md-2 col-md-offset-1 ">
                    Your Review : </div>

                </div>
                <div class="row"><div class="col-md-10 col-md-offset-1 normaltext" id="reviewinput">
                    <textarea rows="4" cols="80" maxlength="2000" name="review" id="review"></textarea> </div>

                </div>
                <div class="row"><div class="col-md-3 col-md-offset-1" id="reviewsubmit"><button type="button" onclick="javascript:submitReview()" class="btn btn-success" >Submit Review</button></div></div>
                <div class='row'><br><div class='col-md-10 col-md-offset-2 red' id='reviewalert' style='display: none'>Please enter your review</div></div>

            </div>
            <div class="modal-footer">
                <div class="row text-center" style="display: none" id="loading1"><div class="col-md-7 orange" ><i class="fa fa-spinner fa-2x fa-spin"></i> </div></div>
                <div class="row text-center">
                    <button type="button" data-dismiss="modal" class="btn btn-primary">Close</button>
                </div>
            </div>
        </div>


        </div>
</div>


<script>
    function reviewrating(){
        $("#reviewrating").modal('show');
        $("#loading1").show();
        <g:remoteFunction controller="wonderpublish" action="reviewrating" onSuccess='displayReviewRating(data);' params="'bookId=${bookId}'"/>
    }

    function submitRating(){
        $("#ratingalert").hide(500);
        var field =  document.getElementById("rating");
        if(field.selectedIndex==0)
        {
            $("#ratingalert").show(500);
        }else{
            $("#loading1").show();
            <g:remoteFunction controller="wonderpublish" action="updateReviewRating" onSuccess='ratingsuccess(data);' params="'bookId=${bookId}&rating='+field[field.selectedIndex].value"/>
        }
    }

    function ratingsuccess(data){
        $("#loading1").hide();
        document.getElementById("ratinginput").innerHTML="";
        document.getElementById("ratinginput").innerHTML = getStars(data.rating);
        $("#ratingsubmit").hide();
    }

    function displayReviewRating(data){
        console.log("Rating="+data.rating+" review="+data.review);
        $("#loading1").hide();
        if(data.rating =="-1" || data.rating==""|| data.rating=="null"|| data.rating==null) {
        }else{
            document.getElementById("ratinginput").innerHTML="";
            document.getElementById("ratinginput").innerHTML = getStars(data.rating);
            $("#ratingsubmit").hide();
        }
        if(data.review=="-1"|| data.review==""|| data.review=="null"|| data.review==null) {
        }else{
            document.getElementById("reviewinput").innerHTML="";
            document.getElementById("reviewinput").innerHTML = data.review;
            $("#reviewsubmit").hide();
        }
    }

    function submitReview(){
        $("#reviewalert").hide(500);
        var field =  document.getElementById("review");
        if(field.value=="")
        {
            $("#reviewalert").show(500);
        }else{
            $("#loading1").show();
            <g:remoteFunction controller="wonderpublish" action="updateReviewRating" onSuccess='reviewsuccess(data);' params="'bookId=${bookId}&review='+field.value"/>
        }
    }

    function reviewsuccess(data){
        $("#loading1").hide();
        document.getElementById("reviewinput").innerHTML="";
        document.getElementById("reviewinput").innerHTML = data.review;
        $("#ratingsubmit").hide();
    }

</script>