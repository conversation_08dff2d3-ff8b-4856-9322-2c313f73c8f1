<style>
.form-control{
    height: auto !important;
}
</style>

<div class="modal fade createBook-modal modal-modifier" id="readingMaterials" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <g:uploadForm name="resource3Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">

                    <div class="form-group col-12">
                        <h5 class="mt-3 mb-4 pb-2 border-bottom">Reading Material</h5>
                        <label>Resource Name</label>
                        <g:textField id="resourceName" class="form-control" name="resourceName"  placeholder="Enter Resource Name" />
                        <div class="invalid-feedback" id="notesUploadAlert">Please enter reading material name.</div>
                    </div>
                    <%if("books".equals(session['entryController'])){%>
                        <div class="form-group col-12">
                            <label>Resource Type</label>
                            <select name="subType" id="subType" class="form-control"><option value="">Select</option>
                                <option value="lesson">Lesson</option>
                                <option value="mindmap">Mind Map</option>
                                <option value="modelquestionpaper">Model question paper</option>
                                <option value="modelquestionpapersolved">Model question paper solved</option>
                                <option value="questionpaper">Previous year question paper</option>
                                <option value="questionpapersolved">Previous year question paper solved</option>
                                <option value="solution">Solution</option>
                                <option value="trick">Trick to remember</option>
                            </select>
                        </div>
                    <%}%>
                    <div class="form-group col-12">
                        <label>Upload File</label>
                        <input id="file3" type="file" class="form-control" name="file"  accept=".epub , .pdf" />
                        <div class="invalid-feedback" id="UploadSizeAlert"></div>
                    </div>
                    <div class="form-group col-12">
                        <label>Set Zoom Level</label>
                        <select name="zoomLevel" id="zoomLevel" class="form-control">
                            <option value="">Default</option>
                            <option value="1">130%</option>
                            <option value="2">160%</option>
                            <option value="3">190%</option>
                        </select>
                    </div>
                    <div class="form-group col-12">
                        <i>Render as pdf only (not secure)</i>
                        <div id="render_as_pdf" class="d-flex align-items-center">
                            <input type="radio" name="convert" value="false" id="pdfyes" class="d-none">
                            <label for="pdfyes" class="pdfyes">Yes</label>
                            <input type="radio" name="convert" value="true" id="pdfno"  class="d-none" checked>
                            <label for="pdfno" class="pdfno">No</label>
                        </div>
                    </div>
                    <div id="allow_pdf_download" class="form-group col-12">

                    </div>

                    <input type="hidden" name="resourceType" value="Notes">
                    <input type="hidden" name="useType" value="notes">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">

                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="uploadNotes()">Submit</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="referenceVideoLinks" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <g:uploadForm id="resource5Form-id" name="resource5Form" url="[action:'addlink',controller:'resourceCreator']"  method="post">
                    <div class="form-group col-12">
                        <h5 class="mt-3 mb-4 pb-2 border-bottom">Reference Video Links</h5>
                        <label>link</label>
                        <g:textField id="videolink" class="form-control" name="link"  placeholder=""  />
                        <div class="invalid-feedback" id="videolinksUploadAlert">Please enter the video link.</div>
                    </div>
                    <div class="form-group col-12">
                        <label>Title</label>
                        <g:textArea id="videolinksresourceName" class="form-control" name="resourceName"  placeholder="" />
                        <div class="invalid-feedback" id="videolinksNameUploadAlert">Please enter the title.</div>
                    </div>
                    <div class="form-group col-12">
                        <label>Start Date</label>
                        <div class="input-group">
                            <g:textField id="videolinksresourceStartDate" class="form-control" name="resourceStartDate"  placeholder="" autocomplete="off"/>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary readonly-datepicker" type="button" id="resourceStartDatePicker"><asset:image src="ws/icon-calendar.svg"/></button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-12">
                        <label>End Date</label>
                        <div class="input-group">
                            <g:textField id="videolinksresourceEndDate" class="form-control" name="resourceEndDate"  placeholder="" autocomplete="off"/>
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary readonly-datepicker" type="button" id="resourceEndDatePicker"><asset:image src="ws/icon-calendar.svg"/></button>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="resourceType" value="Reference Videos">
                    <input type="hidden" name="useType" value="notes">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">

                    <div id="videoPlayer">

                        <div class="form-group col-12 mb-2">
                            <p class="mb-0">Video Player</p>
                            <label class="clickable-label">
                                <input type="radio" name="videoPlayer" value="youtube"  onchange="videoPlayerChanged(this)"> Youtube
                            </label>
                            <label class="clickable-label">
                                <input type="radio" name="videoPlayer" value="custom" checked onchange="videoPlayerChanged(this)"> Custom player
                            </label>
                        </div>
                        <div id="allowCommentsSection">
                            <div class="form-group col-12">
                                <label class="clickable-label mb-2">
                                    <input type="checkbox" name="allowComments" checked> Allow comments for live video
                                </label>
                                <label class="clickable-label">
                                    <input type="checkbox" name="displayComments"> Display comments after live video
                                </label>
                            </div>
                        </div>
                        <div class="form-group col-12 mb-2">
                            <p class="mb-0">Download video links:</p>
                        </div>
                        <div class="form-group d-flex align-items-center col-12">
                            <label class="control-label col pl-0">  360p: </label>
                            <g:textField id="downloadlink1" class="form-control" name="downloadlink1"/>
                        </div>
                        <div class="form-group d-flex align-items-center col-12">
                            <label class="control-label col pl-0">  540p: </label>
                            <g:textField id="downloadlink2" class="form-control" name="downloadlink2"/>
                        </div>
                        <div class="form-group d-flex align-items-center col-12">
                            <label class="control-label col pl-0">  720p: </label>
                            <g:textField id="downloadlink3" class="form-control" name="downloadlink3"/>
                        </div>

                    </div>

                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="uploadVideoLinks()">Add</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="referenceWebLinks" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <g:uploadForm name="resource4Form" url="[action:'addlink',controller:'resourceCreator']"  method="post">
                   <div class="form-group col-12">
                       <h5 class="mt-3 mb-4 pb-2 border-bottom">Reference Web Links</h5>
                       <label>Web link</label>
                        <g:textField id="weblink" class="form-control" name="link"  placeholder="Enter web link" />
                        <div class="invalid-feedback" id="linksUploadAlert">Please enter the web link.</div>
                    </div>
                    <div class="form-group col-12">
                        <label>Resource Name</label>
                        <g:textField id="linksresourceName" class="form-control" name="resourceName"  placeholder="Enter resource name" />
                        <div class="invalid-feedback" id="linksUploadNameAlert">Please enter the name of the resource.</div>
                    </div>

                    <input type="hidden" name="resourceType" value="Reference Web Links">
                    <input type="hidden" name="useType" value="notes">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">
                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="uploadLinks()">Add</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal createBook-modal" id="fillBlanks" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                <h4>Fill in the Blanks</h4>
            </div>

            <!-- Modal body -->
            <div class="modal-body">


                <div class="row justify-content-center">
                    <g:uploadForm name="resource6Form" url="[action:'addFIBByFile',controller:'resourceCreator']"  method="post">


                        <div class="form-group col-12">
                            <label>Resource Name</label>
                            <g:textField id="quizresourceName" class="form-control" name="resourceName"  placeholder="Name" />
                        </div>
                        <div class="form-group col-12 filelink">
                            <label>Upload</label>
                            <input id="file4" class="form-control" type="file"  name="file"  accept=".xls,.xlsx,.xml" />
                        </div>
                        <div class="pl-3">
                            <button type="button" onclick="javascript:uploadQuiz()" class="btn btn-primary">Add</button>
                        </div>


                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="quizUploadAlert">
                            ** Enter a name for this quiz.
                        </div>
                        <input type="hidden" name="resourceType" value="">
                        <input type="hidden" name="useType" value="quiz">
                        <input type="hidden" name="chapterId">
                        <input type="hidden" name="bookId">
                    </g:uploadForm>
                </div>

            </div>

        </div>
    </div>
</div>
<div class="modal createBook-modal" id="imagesZip" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                <h4>Images Zip</h4>
            </div>

            <!-- Modal body -->
            <div class="modal-body">

                <div class="row justify-content-center">
                    <g:uploadForm name="resource7Form" url="[action:'addImagesZip',controller:'resourceCreator']"  method="post">

                        <div class="form-group col-12 filelink">
                            <label>Upload</label>
                            <input id="file5" class="form-control" type="file"  name="file"  accept=".zip" />
                        </div>
                        <div class="buk3 mt-2 pl-3">
                            <button type="button" onclick="javascript:uploadImageZip()" class="btn btn-primary">Add</button>
                        </div>

                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="zipUploadAlert">
                            ** Please select the images zip file to upload.
                        </div>
                        <input type="hidden" name="chapterId">
                        <input type="hidden" name="bookId">
                    </g:uploadForm>
                </div>

            </div>

        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="paidVideos" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <g:uploadForm name="resource10Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">
                    <div class="form-group col-12">
                        <h5 class="mt-3 mb-4 pb-2 border-bottom">Audio/Video</h5>
                        <label>Resource Name</label>
                        <g:textField id="mediaresourceName" class="form-control" name="resourceName"  placeholder="Enter resource name" />
                        <div class="invalid-feedback" id="paidVideoNameAlert">Please enter the name of the resource.</div>
                    </div>
                    <div class="form-group col-12 filelink">
                        <label>Upload</label>
                        <input id="file10" type="file"  name="file" class="form-control" accept="video/mp4,video/x-m4v,video/*,audio/*" />
                        <div class="invalid-feedback" id="paidVideoAlert">Please select the file to upload.</div>
                    </div>

                    <input type="hidden" name="resourceType" value="Uploaded Media">
                    <input type="hidden" name="useType" value="notes">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">
                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="uploadPaidVideo()">Add</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="addPublisher" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <div class="form-group col-12">
                    <h5 class="mt-3 mb-4 pb-2 border-bottom">Add Publisher</h5>
                    <label>Publisher Name</label>
                    <input type="text" name="newpublisher" id="newpublisher" class="form-control mt-0">
                    <div class="invalid-feedback" id="newpublisher-error" style="display: none;"></div>
                </div>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="submitPublisher()">Add</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="addAuthor" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier">
                <div class="form-group col-12">
                    <h5 class="mt-3 mb-4 pb-2 border-bottom">Add Author</h5>
                    <label>Author Name</label>
                    <input type="text" name="new author" id="newauthor" class="form-control mt-0">
                    <div class="invalid-feedback" id="newauthor-error" style="display: none;">Please enter author name.</div>
                </div>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="submitAuthor()">Add</button>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="copyQuiz" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier">
                <g:uploadForm name="resource8Form" url="[action:'copyQuiz',controller:'resourceCreator']"  method="post">
                    <div class="form-group col-12">
                        <h5 class="mt-3 mb-4 pb-2 border-bottom">Copy Quiz</h5>
                        <label>Quiz Number</label>
                        <g:textField id="resId"  name="resId"  placeholder="Enter the quiz number to copy" class="form-control mt-0" onkeypress="return onlyNumberKey(event)"/>
                        <div class="invalid-feedback" id="quizCopyAlert"></div>
                    </div>
                    <div class="form-group col-12 mt-3">
                        <label class="clickable-label">
                            <input type="checkbox" name="reAttempt" id="reAttempt" checked> Re-Attempt
                        </label>
                    </div>

                    <input type="hidden" name="resourceType" value="">
                    <input type="hidden" name="useType" value="quiz">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">
                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="copyQuiz()">Copy</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade createBook-modal modal-modifier" id="readQuiz" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier">
                <g:uploadForm name="resource9Form" url="[action:'copyQuiz',controller:'resourceCreator']"  method="post">
                    <div class="form-group col-12">
                        <h5 class="mt-3 mb-4 pb-2 border-bottom">Copy Reading Material</h5>
                        <label>Read Material Number</label>
                        <g:textField id="readResId"  name="resId"  placeholder="Enter the read material number to copy"  class="form-control mt-0" onkeypress="return onlyNumberKey(event)"/>
                        <div class="invalid-feedback" id="readCopyAlert"></div>
                    </div>

                    <input type="hidden" name="resourceType" value="">
                    <input type="hidden" name="useType" value="notes">
                    <input type="hidden" name="chapterId">
                    <input type="hidden" name="bookId">
                </g:uploadForm>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" onclick="copyRead()">Copy</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="publishSuccess" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3 text-success">Success!</h5>
                <p>This eBook has been published successfully.</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" data-dismiss="modal" aria-label="Close">Ok</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="transferBookModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to transfer this eBook?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" id="bookTransferBtn">Yes, Transfer</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="deleteCategoryTagModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to delete this category tag?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-shadow border-0 col-5 col-md-4 ml-2" id="deleteCategoryTagBtn">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="copiedBookModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3 text-success">Success!</h5>
                <p>The eBook is successfully copied. <br>Do you want to edit the new eBook now?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-success btn-shadow border-0 col-5 col-md-4 ml-2" id="editCopiedBook">Yes, Open</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="deleteChapterModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to delete this chapter?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-shadow border-0 col-5 col-md-4 ml-2" id="deleteChapterBtn">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal fade createBook-modal modal-modifier" id="deleteResourceModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p>You want to delete this resource?</p>

                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-shadow border-0 col-5 col-md-4 ml-2" id="deleteResourceBtn">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

