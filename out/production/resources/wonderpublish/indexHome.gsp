<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
def newCookie = new javax.servlet.http.Cookie("siteName", "books");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/books/navheader_new"></g:render>
<link href="https://fonts.cdnfonts.com/css/righteous" rel="stylesheet">
<style>
@media (min-width:400px) and (max-width:600px)
{
    #bestSellerBooks .img-wrapper{
        width: 200px;
    }
}
@media (min-width:325px) and (max-width:395px)
{
    #bestSellerBooks .img-wrapper{
        width: 180px;
    }
}
.bannerPrepJoyBg .btn-primary:not(:disabled):not(.disabled):active:focus, .bannerPrepJoyBg .btn-primary:focus
{
    box-shadow:none !important;
}
.db-main.pt-3 .container{
    display:none;
}
.prepJoyBannerBg {
    border-radius: 10px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center bottom;
}


@media (min-width:400px) and (max-width: 575px){
    .nextexam_section .monthly-test-btn
    {
        font-size:14px;
        width: 187px;
    }
}
@media (min-width:325px) and (max-width: 399px) {
    .nextexam_section .monthly-test-btn {
        font-size: 12px;
        width: 160px;
    }
}
@media (max-width: 320px) {
    .nextexam_section .monthly-test-btn {
        font-size: 10px;
        width: 143px;
    }
}
@media (max-width: 575px){
    .bannerPrepJoyBg .text-center
    {
        width:100% !important;
    }

}
@media (max-width:575px)
{
    .logo img{
        width:130px;
    }
    .logo
    {
        padding-bottom:5%;
    }
    .text-center.align-items-center.w-100.mx-auto.p-3.p-md-4.p-lg-5 {
        padding-bottom:0% !important;
    }
}
@media (min-width:576px)
{
    .logo img{
        width:200px;
    }
    .logo
    {
        padding-bottom:5%;
    }
    .text-center.align-items-center.w-100.mx-auto.p-3.p-md-4.p-lg-5 {
        padding-bottom:0% !important;
    }
    .nextexam_section .monthly-test-btn
    {
        width:190px;
    }
}



</style>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js pb-5 pt-4 nextexam_section">
    <div class="container">
        <g:render template="/books/bannerSlider"></g:render>
    </div>
    <%if(!(("android".equals(session["appType"]))||("ios".equals(session["appType"])))){%>
    <div class="container mt-4 text-center">
        <a href="/testgenerator/monthlyQuiz" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect monthly-test-btn mb-2">Monthly Tests</a>
        <a href="https://www.prepjoy.com/prepjoy"  target="_blank" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect monthly-test-btn mb-2">Prepjoy Current Affairs</a>
    </div>
    <%}%>

    <div id="student-problems-section" class="store1_index store1_index_accordion">
        <div class="container px-3">
            <div class="accordion pt-4" id="store1Accordion"></div>
            <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
            <div id="nextExamPrice" class="d-flex justify-content-end" ></div>
            <%}else{%>
            <div id="nextExamPrice" class="d-flex justify-content-end" ><a class="text-primary text-primary-modifier mr-3" onclick='showRecurringBlocks()' id="showMoreRecurring" >Show More... </a> <a class="text-primary text-primary-modifier" id="showAllBooks" href="/ebooks?publisher=${publisher.name.replaceAll("\\s", "-")}" style="display: none">Show all...</a></div>
            <%}%>
        </div>
    </div>


</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<%if(params.tokenId==null&&session["appType"]==null){%>
<div class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>
<%}%>
</section>

<g:render template="/books/footer_new"></g:render>


<script>

    <%if((("android".equals(session["appType"]))||("ios".equals(session["appType"])))){%>
    $('#goBack').addClass('d-none');
    <%}%>

    var urlTag="";
    var allBooksData;
    var subjectFilter=false;
    var filteredSubject;
    var bookInLibrary;
    var libraryBookId;


    $('.loading-icon').removeClass('hidden');
    function getBooksList(){
        <g:remoteFunction controller="wonderpublish" action="getPublisherBooksList"  onSuccess='booksReceived(data);'
                 params="'urlName=${urlName}'"/>

    }

    function getBooksLists(){
        <g:remoteFunction controller="wonderpublish" action="getBooksListForUser"  onSuccess='libraryBook(data);'
                 params="'urlName=${urlName}'"/>
    }
    var userLibraryBookList=[];
    var tabview = false;
    var isResponsive = false;

    var tabresponsive = window.matchMedia("(max-width:900px)");
    if(tabresponsive.matches){
        tabview = true;
    }
    else tabview = false;

    var mq = window.matchMedia( "(max-width: 570px)" );
    if (mq.matches) {
        isResponsive = true;
    }
    else isResponsive = false;

    function libraryBook(data){
        var libraryBook=data.books;
        for(var i=0;i<libraryBook.length;i++){
            userLibraryBookList.push(libraryBook[i].id);
        }
    }
    getBooksLists();



    function booksReceived(data){
        allBooksData = data;
        var  books = JSON.parse(data.books);
        displayBooksList(books);

    }
    var currentIndex=0;
    function displayBooksList(books){

        var htmlStr="";
        //changing the logic to show only current month's book and that only last five days.
        for(var i=0;i<1;i++){
            libraryBookId=books[0].id;
            if(!(userLibraryBookList.includes(libraryBookId))){
                bookInLibrary=false;
            }
            else if(userLibraryBookList.includes(libraryBookId)){
                bookInLibrary=true;
            }

            htmlStr +="<h5>Daily Current Affairs</h5><div class=\"card border-0 mb-3\">\n" ;


            if(i==0)
                htmlStr += "<div id=\"collapse"+i+"\" class=\"collapse show\" aria-labelledby=\"heading"+i+"\">\n" +
                    "                    <div class=\"card-body row justify-content-start align-items-center mx-0 px-0\" id=\"card"+i+"\">\n" +
                    "                    </div>\n" +
                    "                </div>\n" +
                    "            </div>";
            else
                htmlStr += "<div id=\"collapse"+i+"\" class=\"collapse\" aria-labelledby=\"heading"+i+"\">\n" +
                    "                    <div class=\"card-body row justify-content-start align-items-center mx-0 px-0\" id=\"card"+i+"\">\n" +
                    "                    </div>\n" +
                    "                </div>\n" +
                    "            </div>";

        }
        document.getElementById("store1Accordion").innerHTML=htmlStr;

        <g:remoteFunction controller="funlearn" action="getAllChapterDetails"  onSuccess='displayChapterDetails(data);'
                 params="'bookId='+books[0].id"/>
    }


    function bookClicked(index,bookId){
        libraryBookId=bookId;
        if(!(userLibraryBookList.includes(libraryBookId))){
            bookInLibrary=false;
        }
        else if(userLibraryBookList.includes(libraryBookId)){
            bookInLibrary=true;
        }
        currentIndex = index;
        var indexContent = document.getElementById("card"+index).innerText.trim();
        if(indexContent.length==0){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="funlearn" action="getAllChapterDetails"  onSuccess='displayChapterDetails(data);'
                 params="'bookId='+bookId"/>
        }


    }
    function displayChapterDetails(data){
        var allChapters =  JSON.parse(data.allChapters);
        var currentChapterId=-1;
        var htmlStr = "";
        var count=0;
        for(var i=0;i<allChapters.length;i++){
            if(currentChapterId!=allChapters[i].chapterId){
                currentChapterId=allChapters[i].chapterId;
                count +=1;

                <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>

                htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                    "<a href='javascript:openCurrentAffairs("+allChapters[i].bookId+","+allChapters[i].chapterId+")'>"+allChapters[i].chapterName+"</a>" +
                    "</div>";

                <%}else{%>
                if(bookInLibrary && isResponsive != true && tabview != true && count<=6) {
                    $('#showMoreRecurring').hide();
                    $('#showAllBooks').show();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g,"&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary && isResponsive == true && count<=2) {
                    $('#showMoreRecurring').hide();
                    $('#showAllBooks').show();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' >" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary && isResponsive == true && count>2) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary && tabview == true && count<=4) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' >" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary && tabview == true && count>4) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary && isResponsive != true  && tabview != true && count>6)
                {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(bookInLibrary){
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g,"&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && isResponsive != true && tabview != true && count<=6) {
                    $('#showMoreRecurring').hide();
                    $('#showAllBooks').show();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g,"&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && isResponsive == true && count<=2) {
                    $('#showMoreRecurring').hide();
                    $('#showAllBooks').show();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' >" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && isResponsive == true && count>2) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && tabview == true && count<=4) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' >" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && tabview == true && count>4) {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary && isResponsive != true  && tabview != true && count>6)
                {
                    $('#showMoreRecurring').show();
                    $('#showAllBooks').hide();
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter' style='display:none'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g, "&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&preview=true&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                else if(!bookInLibrary){
                    htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                        "<a href='/" + encodeURIComponent(allChapters[i].chapterName).replace(/'/g,"&#39;") + "/ebook?bookId=" + allChapters[i].bookId + "&chapterId=" + allChapters[i].chapterId + "&nextexam=true'>" + allChapters[i].chapterName + "</a>" +
                        "</div>";
                }
                <%}%>

            }


        }
        document.getElementById("card"+currentIndex).innerHTML=htmlStr;

        $('.loading-icon').addClass('hidden');
    }
    getBooksList();
    var loggedInUser = false;

    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>


    function playSliderVideo(videoLink,id){
        var videoSRC="https://www.youtube.com/embed/"+videoLink;

        $("#videoUpdate" + ' iframe').attr('src', videoSRC);
        $("#VideoModal").modal('show');

        if(loggedInUser){
            updateUserView(id,"index","videos");
        }
        else{
            updateView(id,"index","videos");
        }


    }

    function showRecurringBlocks()
    {
        $('.nextexam_section .store1_index_accordion .card-body .name_of_chapter').show();
        $('#showAllBooks').show();
        $('#showMoreRecurring').hide();
    }

    function downloadPDF(id){

        if(loggedInUser){
            updateUserView(id,"index","pdf");
        }
        else{
            updateView(id,"index","pdf");
        }
        window.open('/funlearn/download?id='+id,'_blank');
    }

    function openExternalPDF(id,link){

        if(loggedInUser){
            updateUserView(id,"index","weblinks");
        }
        else{
            updateView(id,"index","weblinks");
        }
        window.open(link,'_blank');
    }

    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    $('#VideoModal').on('hidden.bs.modal', function () {
        $("#videoUpdate" + ' iframe').attr('src', '');
    });


    function openCurrentAffairs(bookId,chapterId) {
        <%if(("android".equals(session["appType"]))){%>
        JSInterface.openCurrentAffairs(bookId,chapterId);
        <%}else{%>
        var json = {'bookId':bookId,'chapterId':chapterId};
        webkit.messageHandlers.openCurrentAffairs.postMessage(JSON.stringify(json));
        <%}%>

    }

</script>

</body>
</html>
