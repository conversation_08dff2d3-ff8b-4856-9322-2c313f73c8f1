<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="fileuploadwithpreview.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>
<style>
.image-upload > input {
    display: none;
}



</style>
<div class="qandaCreator my-4 my-md-5 px-2 px-md-4 px-lg-5">
    <div class="container-fluid">
        <div class="row mx-0 mobile_column_swap">
            <div id="static-content" class=" col-md-9 rounded border pb-4 ">
                <div class="row headerQandA">
                    <div class="col-md-12">
                        <h4>Questions and Answers</h4><br>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 px-0">
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
                            <input type="hidden" name="resourceType">
                            <input type="hidden" name="chapterId">
                            <input type="hidden" name="quizId">
                            <input type="hidden" name="mode">
                            <input type="hidden" name="resourceDtlId">
                            <input type="hidden" name="objectiveMstId">
                            <input type="hidden" name="finished">
                            <input type="hidden" name="bookId" value="${bookId}">

                            <div class="row mx-0 mb-3">
                                <div class="col-sm-12">
                                    <div class="form-group resourceName float-label-control">
                                        <div class="cktext">
                                            <input type="text" id="resourceName" name="resourceName" placeholder="Description" value="" maxlength="255">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-2">
                                </div>
                                <div class="col-sm-12  qanda1">
                                    <div class="form-group">
                                    <label  for="question">QUESTION</label>
                                    <div class="cktext">
                                        <textarea  rows="4"  id="question" name="question" placeholder="Type your question here"></textarea>
                                    </div>
                                    </div>
                                </div>
                                <div id="target"></div>
                            </div>

                            <div class="col-sm-12">&nbsp;<br><br></div>

                            <div class="row">
                                <div class="col-sm-12 qanda1">
                                    <div class="form-group">
                                        <label for="answer">ANSWER </label>
                                        <div class="cktext">
                                            <textarea  rows="4"  id="answer" name="answer" placeholder="Answer Explanation"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            // create a text field called marks which should accept only numbers
                            <div class="row">
                                <div class="col-sm-12 qanda1">
                                    <div class="form-group">
                                        <label for="marks">Marks</label>
                                        <div class="cktext">
                                            <input type="number" id="marks" name="marks" placeholder="Marks" value="" maxlength="255">
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="row qandaAlert">
                                <div class="alert  alert-danger mr-3" id="alertbox" style="display: none">
                                    Please enter all fields.
                                </div>
                            </div>
                            <div class="row buttonsQandA">
%{--                                <div class="form-group">--}%
                                    <div class=" col-sm-12 text-center">
                                        <button type="button" onclick="javascript:deleteQuestion()" class="btn btn-primary btn-lg" style="display: none;" id="deletebutton">Delete</button>
                                        <button type="button" onclick="javascript:formSubmit('Save')" class="btn btn-primary btn-lg">Save</button>
                                        <button type="button" onclick="javascript:formSubmit('Next')" class="btn btn-primary btn-lg">Save and Add Next</button>
                                        <button type="button" onclick="javascript:formSubmit('Done')" class="btn btn-primary btn-lg finishBtn">Save and Finish</button>
                                    </div>
%{--                                </div>--}%
                            </div>
                            <div class="row" id="savedNotification" style="display: none">
                                <div class="col-md-8 col-md-offset-4 smallText green"><b>Question and Answer saved</b></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div id="sidebar" class="col-md-2 hidden-xs hidden-sm text-left"></div>
            

        </div>

    </div>
    <% if("sage".equals(session["entryController"])){%>
    <g:render template="/${session['entryController']}/footer"></g:render>
    <%}else {%>
    <g:render template="/${session['entryController']}/footer_new"></g:render>
    <%}%>

</div>
<asset:javascript src="jquery-1.11.2.min.js"/>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<asset:javascript src="qandacreator.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>

<script>
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var chapterId = "${params.chapterId}";
    var bookId = "${params.bookId}";

    CKEDITOR.disableAutoInline = true;
    CKEDITOR.replace( 'question',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        height: 150,
    });

    CKEDITOR.replace( 'answer',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType,
        height: 350,
    });

    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }

    <% 
        if("edit".equals(params.mode)){
            objectives.each { 
                objective ->
    %>
    
    var quizItem={};
    quizItem.resourceName=htmlDecode("${resourceDtl.resourceName}");
    quizItem.question =htmlDecode( "${objective.question.replaceAll("(\\r|\\n)", "")}");
    quizItem.answer =htmlDecode("${objective.answer!=null?(new String(objective.answer)).replaceAll("(\\r|\\n)", ""):""}");
    quizItem.objectiveMstId = "${objective.id}";
    quizItem.marks = "${objective.marks}";
    quizItems.push(quizItem);
    <%      }  %>
    resourceDtlId = "${resourceDtl.id}";
    quizId = "${resourceDtl.resLink}";
    quizName = quizItems[0].resourceName;
    gotoQuestion(0);
    document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
    for(i=0;i<quizItems.length;i++){
        document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px' >" + (i + 1)  + "</a>&nbsp;&nbsp;";
    }
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
    <%  } %>

    function formSubmit(submitType) {
        if (validate()) {

        $('#savedNotification').hide();

            document.addquiz.mode.value = mode;
            document.addquiz.resourceType.value = resourceType;
            document.addquiz.chapterId.value = chapterId;
            document.addquiz.quizId.value = quizId;
            document.addquiz.resourceDtlId.value = resourceDtlId;
            document.addquiz.objectiveMstId.value = objectiveMstId;
            document.addquiz.resourceName.value = quizName;
            
            if('Next' == submitType) {
                var oData = new FormData(document.forms.namedItem("addquiz"));

                var url = "${createLink(controller:'resourceCreator',action:'addQuiz')}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionAdded(req);
                    }
                });
                
                document.getElementById("addquiz").reset();
                CKEDITOR.instances.question.setData('');
                CKEDITOR.instances.answer.setData('');

                document.getElementsByName('question')[0].placeholder = 'Question ' + (quizItems.length + 1);
                $('#deletebutton').hide();
                $("html, body").animate({scrollTop: 0}, "slow");
            } else if ('Save' == submitType) {
                var oData = new FormData(document.forms.namedItem("addquiz"));

                var url = "${createLink(controller:'resourceCreator',action:'addQuiz')}";

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionSaved(req);
                    }
                });
            } else {
                document.addquiz.finished.value = 'true';
                document.addquiz.submit();
            }
        }
    }
    
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };


    function deleteQuestion(){
        if(confirm("Are you sure to delete this question?")){
            showLoader();
            <g:remoteFunction controller="funlearn" action="deleteQuestion"  onSuccess='reloadPage();'
                params="'objectiveId='+objectiveMstId+'&quizId='+quizId" />
        }
    }
    function reloadPage(){
        location.reload();
    }

</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
            // Select all CKEditor warning notifications
            let notifications = document.querySelectorAll(".cke_notification_warning");

            notifications.forEach(notification => {
                let closeButton = notification.querySelector(".cke_notification_close");
                if (closeButton) {
                    closeButton.click(); // Simulate a click to close the notification
                }
            });
        }, 1000); // Delay to ensure CKEditor has loaded
    });

</script>
</body>
</html>
