<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/articles.css" async="true" media="all"/>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
<body>
<main class="blog_container">
    <article>
        <header>
            <h1>${title}</h1>
            <div id="introduction">${info.introductionHindi}</div>
        </header>
        <aside>
            <h2>Table of Contents</h2>
            <ol class="tableOfContent">
                <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
                <li><a href="#fullForm">${topLevelTitle} पूर्ण प्रपत्र - Full Form</a></li>
                <%}%>
                <li><a href="#introductionMain">${topLevelTitle} - परिचय</a></li>
                <%if(info.eligibilityHindi!=null&&!"".equals(info.eligibilityHindi)){%>
                <li><a href="#eligibility">${topLevelTitle} पात्रता - Eligibility</a></li>
                <%}%>
                <%if(info.examPatternHindi!=null&&!"".equals(info.examPatternHindi)){%>
                <li><a href="#examPattern">${topLevelTitle} परीक्षा पैटर्न - Exam Pattern</a></li>
                <%}%>
                <%if(info.fullSyllabusHindi!=null&&!"".equals(info.fullSyllabusHindi)){%>
                <li><a href="#fullSyllabus">${topLevelTitle} पूरा सिलेबस - Full Syllabus</a></li>
                <%}%>
                <%if(info.applicationProcessHindi!=null&&!"".equals(info.applicationProcessHindi)){%>
                <li><a href="#applicationProcess">${topLevelTitle} आवेदन प्रक्रिया - Application Process</a></li>
                <%}%>
                <%if(info.examDatesHindi!=null&&!"".equals(info.examDatesHindi)){%>
                <li><a href="#examDates">${topLevelTitle} परीक्षा तिथियाँ - Exam Dates</a></li>
                <%}%>
                <li><a href="#bestEBooks">${fullTitle} के लिए सर्वश्रेष्ठ विक्रेता ई-पुस्तकें (Best price and Immediate Access)</a></li>
                <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
                <li><a href="#bestBooks">${fullTitle} के लिए सर्वोत्तम पुस्तकें</a></li>
                <%}%>
                <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
                <li><a href="#youtubeChannels">${fullTitle} के लिए सर्वश्रेष्ठ यूट्यूब चैनल</a></li>
                <%}%>
                <%if(info.pqpLinksHindi!=null&&!"".equals(info.pqpLinksHindi)){%>
                <li><a href="#pqpLinks">${fullTitle} पिछले वर्षों के प्रश्न पत्र</a></li>
                <%}%>
                <%if(info.generalLinksHindi!=null&&!"".equals(info.generalLinksHindi)){%>
                <li><a href="#generalLinks">${topLevelTitle} के लिए उपयोगी लिंक</a></li>
                <%}%>
                <%if(info.generalInfoHindi!=null&&!"".equals(info.generalInfoHindi)){%>
                <li><a href="#generalInfo">${topLevelTitle} सामान्य जानकारी</a></li>
                <%}%>
                <%if(info.faqHindi!=null&&!"".equals(info.faqHindi)){%>
                <li><a href="#faq">${topLevelTitle} अक्सर पूछे जाने वाले प्रश्नों</a></li>
                <%}%>

            </ol>
        </aside>
        <div>
            <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
            <section id="fullForm">
                <h3>${topLevelTitle} पूर्ण प्रपत्र - Full Form</h3>
                <div id="fullFormText">${info.fullForm}</div>
            </section>
            <%}%>
            <section id="introductionMain">
                <h3>${topLevelTitle} - परिचय</h3>
                <div id="introductionMainText">${info.introductionHindi}</div>
            </section>
            <%if(info.eligibilityHindi!=null&&!"".equals(info.eligibilityHindi)){%>
            <section id="eligibility">
                <h3>${topLevelTitle} पात्रता - Eligibility</h3>
                <div id="eligibilityText">${info.eligibilityHindi}</div>
            </section>
            <%}%>
            <%if(info.examPatternHindi!=null&&!"".equals(info.examPatternHindi)){%>
            <section id="examPattern">
                <h3>${topLevelTitle} परीक्षा पैटर्न - Exam Pattern</h3>
                <div id="examPatternText">${info.examPatternHindi}</div>
            </section>
            <%}%>
            <%if(info.fullSyllabusHindi!=null&&!"".equals(info.fullSyllabusHindi)){%>
            <section id="fullSyllabus">
                <h3>${topLevelTitle} पूरा सिलेबस - Full Syllabus</h3>
                <div id="fullSyllabusText">${info.fullSyllabusHindi}</div>
            </section>
            <%}%>
            <%if(info.applicationProcessHindi!=null&&!"".equals(info.applicationProcessHindi)){%>
            <section id="applicationProcess">
                <h3>${topLevelTitle} आवेदन प्रक्रिया - Application Process</h3>
                <div id="applicationProcessText">${info.applicationProcessHindi}</div>
            </section>
            <%}%>
            <%if(info.examDatesHindi!=null&&!"".equals(info.examDatesHindi)){%>
            <section id="examDates">
                <h3>${topLevelTitle} परीक्षा तिथियाँ - Exam Dates</h3>
                <div id="examDatesText">${info.examDatesHindi}</div>
            </section>
            <%}%>
            <section id="bestEBooks">
                <h3>${fullTitle} के लिए सर्वश्रेष्ठ विक्रेता ई-पुस्तकें (Best price and Immediate Access)</h3>
                <div id="bestEBooksText"></div>
            </section>
            <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
            <section id="bestBooks">
                <h3>${fullTitle} के लिए सर्वोत्तम पुस्तकें</h3>
                <div id="bestBooksText">${info.bestBooks}</div>
            </section>
            <%}%>
            <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
            <section id="youtubeChannels">
                <h3>{fullTitle} के लिए सर्वश्रेष्ठ यूट्यूब चैनल</h3>
                <div id="youtubeChannelsText">${info.youtubeChannels}</div>
            </section>
            <%}%>
            <%if(info.pqpLinksHindi!=null&&!"".equals(info.pqpLinksHindi)){%>
            <section id="pqpLinks">
                <h3>${fullTitle} पिछले वर्षों के प्रश्न पत्</h3>
                <div id="pqpLinksText">${info.pqpLinksHindi}</div>
            </section>
            <%}%>
            <%if(info.generalLinksHindi!=null&&!"".equals(info.generalLinksHindi)){%>
            <section id="generalLinks">
                <h3>${topLevelTitle} के लिए उपयोगी लिंक</h3>
                <div id="generalLinksText">${info.generalLinksHindi}</div>
            </section>
            <%}%>
            <%if(info.generalInfoHindi!=null&&!"".equals(info.generalInfoHindi)){%>
            <section id="generalInfo">
                <h3>${topLevelTitle} सामान्य जानकारी</h3>
                <div id="generalInfoText">${info.generalInfoHindi}</div>
            </section>
            <%}%>
            <%if(info.faqHindi!=null&&!"".equals(info.faqHindi)){%>
            <section id="faq">
                <h3>${topLevelTitle} अक्सर पूछे जाने वाले प्रश्नों</h3>
                <div id="faqText">${info.faqHindi}</div>
            </section>
            <%}%>

        </div>
    </article>
    <%if("1".equals(""+session["siteId"]) || "27".equals(""+session["siteId"])){%>
    <div class="bookDetails__container" style="margin-top: 1.5rem;">
        <g:render template="/resources/ebookFeatures"></g:render>
    </div>
    <%}%>
</main>
<g:render template="/books/footer_new"></g:render>

</body>
<script>
    document.getElementById("introduction").innerHTML=document.getElementById("introduction").innerText;
    <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
    document.getElementById("fullFormText").innerHTML=document.getElementById("fullFormText").innerText;
    <%}%>
    document.getElementById("introductionMainText").innerHTML=document.getElementById("introductionMainText").innerText;
    <%if(info.eligibilityHindi!=null&&!"".equals(info.eligibilityHindi)){%>
    document.getElementById("eligibilityText").innerHTML=document.getElementById("eligibilityText").innerText;
    <%}%>
    <%if(info.examPatternHindi!=null&&!"".equals(info.examPatternHindi)){%>
    document.getElementById("examPatternText").innerHTML=document.getElementById("examPatternText").innerText;
    <%}%>
    <%if(info.fullSyllabusHindi!=null&&!"".equals(info.fullSyllabusHindi)){%>
    document.getElementById("fullSyllabusText").innerHTML=document.getElementById("fullSyllabusText").innerText;
    <%}%>
    <%if(info.applicationProcessHindi!=null&&!"".equals(info.applicationProcessHindi)){%>
    document.getElementById("applicationProcessText").innerHTML=document.getElementById("applicationProcessText").innerText;
    <%}%>
    <%if(info.examDatesHindi!=null&&!"".equals(info.examDatesHindi)){%>
    document.getElementById("examDatesText").innerHTML=document.getElementById("examDatesText").innerText;
    <%}%>
    <%if(info.faqHindi!=null&&!"".equals(info.faqHindi)){%>
    document.getElementById("faqText").innerHTML=document.getElementById("faqText").innerText;
    <%}%>
    <%if(info.pqpLinksHindi!=null&&!"".equals(info.pqpLinksHindi)){%>
    document.getElementById("pqpLinksText").innerHTML=document.getElementById("pqpLinksText").innerText;
    <%}%>
    <%if(info.generalLinksHindi!=null&&!"".equals(info.generalLinksHindi)){%>
    document.getElementById("generalLinksText").innerHTML=document.getElementById("generalLinksText").innerText;
    <%}%>
    <%if(info.generalInfoHindi!=null&&!"".equals(info.generalInfoHindi)){%>
    document.getElementById("generalInfoText").innerHTML=document.getElementById("generalInfoText").innerText;
    <%}%>
    <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
    document.getElementById("bestBooksText").innerHTML=document.getElementById("bestBooksText").innerText;
    <%}%>
    <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
    document.getElementById("youtubeChannelsText").innerHTML=document.getElementById("youtubeChannelsText").innerText;
    <%}%>
    var data = { "books":"${booksList.get("books")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publishers":"${booksList.get("publishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&')
    };

    var booksListTable = "<table cellpadding='5' cellspacing='5' class='table table-striped table-hover'><tr><th></th><th>Book Title</th><th>Publisher</th><th></th></tr>";

    var books = JSON.parse(data.books);

    var noOfBooks = books.length;
    if(noOfBooks>10) noOfBooks=10;
    var imgSrc;
    for(var i=0; i<noOfBooks;i++){
        imgSrc = books[i].coverImage;
        if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
            imgSrc = books[i].coverImage;
            imgSrc = imgSrc.replace("~", ":");
        } else {
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=thumbnail";
        }
        booksListTable +="<tr><td>" +"<img src='" + imgSrc + "' alt='Book Cover Image' width='50'/></td>"+
            "<td>"+books[i].title+"</td>"+
            "<td>"+books[i].publisher+"</td>" +
            "<td>"+"<a href='/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName=books&bookId=" + books[i].id + "&preview=true' target='_blank'>More details</a></td>"+

            "</tr>"
    }
    booksListTable +="</table>"
    if(noOfBooks>0)
        document.getElementById("bestEBooksText").innerHTML = booksListTable;
    else
        document.getElementById("bestEBooksText").innerHTML = "<b> Best seller list not available currently. Please try after some time.";

</script>
</html>
