<%@ page import="java.text.SimpleDateFormat; javax.servlet.http.Cookie" %>


<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>
<div class="modal fade" id="VideoModal">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title"></h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body" id="videoUpdate">
                <iframe id="wsVideo" width="560" height="315" src="" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">

            </div>

        </div>
    </div>
</div>
<div class="container pt-5">

    <h3 class="mb-3">Channel Information</h3>
    <div class="col-md-12 col-md-12 form-inline row mb-4">
        <input type="text" id="channelId" placeholder="Enter channel id" class="form-control col-md-4">
        <input type="button" onclick="getChannelDetails()" value="Get Details" class="btn btn-primary col-md-2 m-0 ml-3 form-control">
    </div>

    <div id="channelDetails" class="mb-4" style="display: none">
        <div class="row my-3 align-items-center">
            <div class="col-md-2"><b>Channel Name:</b></div>
            <div class="col-md-1" id="channelLogo"></div>
            <div class="col-md-7 pl-0" id="channelName"></div>
        </div>
        <div class="row my-3 align-items-start">
            <div class="col-md-2"><b>Custom URL:</b></div>
            <div class="col-md-8" id="customUrl"></div>
        </div>

        <div class="row my-3 align-items-start">
            <div class="col-md-2"><b>Channel Description:</b></div>
            <div class="col-md-8"><textarea id="channelDescription" rows="4" cols="80" class="form-control"></textarea></div>
        </div>
        <div class="row my-3 align-items-start">
            <div class="col-md-2"><b>Language:</b></div>
            <div class="col-md-8" ><g:select id="channelLanguage" class="form-control" name="channelLanguage"
                                             from="${com.wonderslate.data.LangMst.listOrderByLanguage()}"
                                             value=""
                                             optionKey="language" optionValue="language" noSelection="['':'Select']" /></div>
        </div>
        <div class="row my-3 align-items-start">
            <div class="col-md-2"><b>Channel Type:</b></div>
            <div class="col-md-8"><select id="channelType" class="form-control"  name="channelType"><option value="Kids">Kids</option></select></div>
        </div>

        <div class="row my-3 align-items-start">
            <div class="col-md-2"></div>
            <div class="col-md-2 text-left"><input type="button" value="Add" onclick="saveChannelInformation();" class="btn btn-primary form-control"> </div>
        </div>


    </div>

    <h4>Videos</h4>
    <div class="row align-items-center">
        <div class="col-md-2"><b>Language:</b></div>
        <div class="col-md-2" ><g:select id="languageForVideos" class="form-control" name="channelLanguage"
                                         from="${com.wonderslate.data.LangMst.listOrderByLanguage()}"
                                         value=""
                                         optionKey="language" optionValue="language" noSelection="['':'Select']" /></div>
        <div class="col-md-4 form-inline">Videos from last <input type="number" value="-1" size="3" id="numberOfDays" class="form-control col-5 mx-2">days</div>
        <div class="col-md-2"><input type="button" onclick="getVideos()" value="Get Videos" class="btn btn-primary form-control"> </div>
        <div class="col-md-2"><input type="button" onclick="getAddedVideos()" value="Get Added Videos" class="btn btn-primary form-control"> </div>
    </div>
    <div id="displayVideos" class="my-5" style="display: none">

    </div>
<br><br>
</div>
%{--<g:render template="/${session['entryController']}/footer"></g:render>--}%

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->


<script>
    function getVideos(){
        if(document.getElementById("languageForVideos").selectedIndex==0){
            alert("Please select the language");
            return;
        }
        if(document.getElementById("numberOfDays").value==""){
            alert("Please enter number of days from which you want video?");
            return;
        }

        var language = document.getElementById("languageForVideos")[document.getElementById("languageForVideos").selectedIndex].value;
        var numberOfDays = document.getElementById("numberOfDays").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="youtube" action="getLatestVideosOfAllChannels"  onSuccess='gotVideos(data);'
                params="'language='+language+'&numberOfDays='+numberOfDays" />

    }
    function getAddedVideos(){
        if(document.getElementById("languageForVideos").selectedIndex==0){
            alert("Please select the language");
            return;
        }
        if(document.getElementById("numberOfDays").value==""){
            alert("Please enter number of days from which you want video?");
            return;
        }

        var language = document.getElementById("languageForVideos")[document.getElementById("languageForVideos").selectedIndex].value;
        var numberOfDays = document.getElementById("numberOfDays").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="youtube" action="getVideos" params="'languages='+languages" onSuccess="displayVideos(data)"/>

    }
    var videos;
    function gotVideos(data){

       if(data.allVideos.length==0){
           alert("No videos found");
       }else{
            videos = data.allVideos;
           var htmlStr="<div class='row col-md-12'><p>** Select the videos to be added.</p></div><div class='row align-items-start'>";
           for(var i=0;i<videos.length;i++){
               htmlStr +="<div class='col-md-3 mb-4'><div class='card'>" +
                   "<a href='javascript:playSliderVideo(\"" + videos[i].id.videoId + "\")'>" +
                   "<img src='"+videos[i].snippet.thumbnails.default.url+"' class='card-img-top'></a>" +
                   "<div class='cord-body p-3'>" +
                   "<p class='card-text' style='min-height: 85px;'>"+videos[i].snippet.title+"</p>"+
                   "<p class='form-inline justify-content-center'><input type='checkbox' id='checkbox_"+i+"' class='mr-1' style='width: 16px;height: 16px;'><select class='form-control form-control-sm' id='select_"+i+"'>"+
                       "<option value='rhymes'>Rhymes</option>"+"<option value='stories'>Stories</option>"+"<option value='Learn'>Learn</option>"+
                       "</select></p>"+
                       "</div>" +
                   "</div></div>";
           }
           htmlStr +="</div><div class='row justify-content-center col-md-12 mt-3'><input type='button' value='Add Videos' onclick='addVideos();' class='btn btn-primary btn-lg col-md-3'></div>";
           document.getElementById("displayVideos").innerHTML=htmlStr;
           $("#displayVideos").show();
           $('.loading-icon').addClass('hidden');
       }
    }

    function addVideos(){
        for(var i=0;i<videos.length;i++){
            console.log("coming");
            if(document.getElementById("checkbox_"+i).checked){
                console.log("The video "+videos[i].snippet.title+" is selected to be added");

                var channelId=videos[i].snippet.channelId;
                var title = videos[i].snippet.title;
                var language = document.getElementById("languageForVideos")[document.getElementById("languageForVideos").selectedIndex].value;
                var videoType = document.getElementById("select_"+i)[document.getElementById("select_"+i).selectedIndex].value;
                var videoId = videos[i].id.videoId;
                <g:remoteFunction controller="youtube" action="addVideosForLanguage"  onSuccess='videoAdded(data);'
                params="'channelId='+channelId+'&title='+title+'&language='+language+'&videoType='+videoType+'&videoId='+videoId " />
            }
        }
        document.getElementById("displayVideos").innerHTML="";
        $("#displayVideos").hide();
    }

    function videoAdded(data){
        console.log("status="+data.status);

    }
    function getChannelDetails(){
        var channelId=document.getElementById("channelId").value;
        <g:remoteFunction controller="youtube" action="getChannelDetails"  onSuccess='displayChannelDetails(data);'
                params="'channelId='+channelId " />
    }

    function displayChannelDetails(data){
        console.log(data.searchResultList[0]);
    if("OK"==data.status){
        document.getElementById("channelName").innerHTML=data.searchResultList[0].snippet.title;
        document.getElementById("customUrl").innerHTML=data.searchResultList[0].snippet.customUrl;
        document.getElementById("channelDescription").innerHTML=data.searchResultList[0].snippet.description;
        document.getElementById("channelLogo").innerHTML="<img src='"+data.searchResultList[0].snippet.thumbnails.default.url+"' width='100' class='img-thumbnail'>";
        $("#channelDetails").show();

    }
    else{
        console.log("failed");
    }
    }

    function saveChannelInformation(){
        if(document.getElementById("channelLanguage").selectedIndex==0){
            alert("Please select the language");
            return;
        }

        var channelName = document.getElementById("channelName").innerHTML;
        var channelDescription = document.getElementById("channelDescription").value;
        var channelLanguage = document.getElementById("channelLanguage")[document.getElementById("channelLanguage").selectedIndex].value;
        var channelType = document.getElementById("channelType")[document.getElementById("channelType").selectedIndex].value;
        var channelId=document.getElementById("channelId").value;
        <g:remoteFunction controller="youtube" action="addChannelDetails"  onSuccess='channelDetailsSaved(data);'
                params="'channelId='+channelId+'&channelType='+channelType+'&channelLanguage='+channelLanguage+'&channelName='+channelName+'&channelDescription='+channelDescription " />

    }

    function channelDetailsSaved(data){
        alert("status="+data.status);
    }

    function playSliderVideo(videoLink){
        var videoSRC="https://www.youtube.com/embed/"+videoLink;
        var videoSRCauto = videoSRC + "?autoplay=1";
        $("#videoUpdate" + ' iframe').attr('src', videoSRCauto);
        $("#VideoModal").modal('show');
    }

    $('#VideoModal').on('hidden.bs.modal', function () {
        $("#videoUpdate" + ' iframe').attr('src', '');
    });
</script>


</body>
</html>