<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Editing the firmware with the Arduino IDE</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Editing the firmware with the Arduino IDE"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec127"/>Editing the firmware with the Arduino IDE</h1></div></div></div><p>The <code class="literal">configuration.h</code> folder is usually the only file that most RepRap printers will need to adjust. This file contains<a id="id716" class="indexterm"/> the data that describes your<a id="id717" class="indexterm"/> particular electronics and stores the numerical data that calibrates your printer.</p><p>The line that is most commonly changed with a working unmodified 3D printer contains the <span class="strong"><strong>X</strong></span>, <span class="strong"><strong>Y</strong></span>, <span class="strong"><strong>Z</strong></span>, and <span class="strong"><strong>E</strong></span> steps per unit values. These values calibrate the movement of the printer and the amount of filament extruded.</p><p>In Sprinter, this can be easily found at the beginning of the <code class="literal">configuration.h</code> file. In Marlin, this is closer to the bottom-third of the file. Marlin can be seen open in the Arduino IDE in the following screenshot:</p><div class="mediaobject"><img src="graphics/9888OS_AppendixA_04.jpg" alt="Editing the firmware with the Arduino IDE"/></div><p>Any data recorded with two forward slashes <code class="literal">//</code> is the data that is commented out and disabled. It can also precede comments for information purposes or records. When changing values<a id="id718" class="indexterm"/> in the firmware, it's a good idea to comment out<a id="id719" class="indexterm"/> your old values with the <code class="literal">//</code> for record-keeping in case you would like to revert to the old values.</p><p>There are many other variables that can be adjusted. These are usually made during the initial setup of a newly built printer. Most kits and fully-assembled printers will have the necessary changes made in its firmware and uploaded to your controller. For more information, one of the best overviews of the options available in the <code class="literal">configuration.h</code> file can be<a id="id720" class="indexterm"/> found at <a class="ulink" href="http://airtripper.com/1145/marlin-firmware-v1-basic-configuration-set-up-guide/">http://airtripper.com/1145/marlin-firmware-v1-basic-configuration-set-up-guide/</a>.</p></div></body></html>
