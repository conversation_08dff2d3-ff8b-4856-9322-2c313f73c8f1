<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Uploading the firmware to the controller</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Uploading the firmware to the controller"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec128"/>Uploading the firmware to the controller</h1></div></div></div><p>Once the firmware is edited, the <a id="id721" class="indexterm"/>firmware needs to be compiled and uploaded to the controller. If you are concerned about the possibility of a mistake in<a id="id722" class="indexterm"/> the syntax of your firmware, you can choose <span class="strong"><strong>Sketch</strong></span> in the menu and then select <span class="strong"><strong>Verify/Compile</strong></span>. If there are any errors, they will be noted in the window at the bottom.</p><p>Most syntax errors in a line will be highlighted in yellow. This will make it easier to locate. Most of the values, if wrong, may be more difficult to discover. They will not generate compiling errors, but they could create faulty or erratic machine operation.</p><p>If all is well, the firmware can now be uploaded to the controller. Select the right-pointing arrow for the <span class="strong"><strong>Upload</strong></span> option in the row of icons under the menu. Within a minute, the firmware is loaded.</p><p>Some host software can make changes to firmware settings by sending the G-code instructions. This will be examined in <a class="link" href="apb.html" title="Appendix&#xA0;B.&#xA0;Taking a Closer Look at G-code">Appendix B</a>, <span class="emphasis"><em>Taking a Closer Look at G-code</em></span>.</p></div></body></html>
