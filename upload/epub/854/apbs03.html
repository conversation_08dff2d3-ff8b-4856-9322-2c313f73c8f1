<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Basic G-code commands</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Basic G-code commands"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec131"/>Basic G-code commands</h1></div></div></div><p>Most people do not need to learn G-code. This is why we have a slicer to do all of the work. However, there are<a id="id731" class="indexterm"/> a handful of G-code commands that are useful to know. All of these commands are useful while printing and can be entered by way of the host command line. Refer to the following commands:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc"><code class="literal">G1</code>: This<a id="id732" class="indexterm"/> command designates a controlled move. Here's an<a id="id733" class="indexterm"/> example of the <code class="literal">G1</code> command-line syntax: <code class="literal">G1 X200 Y200 Z100 F500</code>. The X and Y motors should move the extruder 200 mm from the home position and at 500 mm/min (F500). We'll see shortly how this command is a useful one.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M302</code>: This command <a id="id734" class="indexterm"/>disables the cold extrusion prevention. <a id="id735" class="indexterm"/>This is very useful when testing the extruder movement without heating the filament. This allows the extruder motor to run when the hot end is colder than a firmware-specified temperature.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M106</code>: This<a id="id736" class="indexterm"/> command turns the cooling fan on. When used with<a id="id737" class="indexterm"/> an <code class="literal">S</code> parameter between 0-255, the speed can be adjusted. Here's an example of the <code class="literal">M106</code> command-line syntax: <code class="literal">M106 S127</code>.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M107</code>: This <a id="id738" class="indexterm"/>command turns<a id="id739" class="indexterm"/> the cooling fan off.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M503</code>: This command<a id="id740" class="indexterm"/> echoes the current settings from<a id="id741" class="indexterm"/> memory (not from EEPROM). This is very useful when you need to see the current calibration settings stored in the firmware.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M501</code>: This command <a id="id742" class="indexterm"/>echoes the current settings from EEPROM<a id="id743" class="indexterm"/> (if you need to reset them after you changed them temporarily).</li><li class="listitem" style="list-style-type: disc"><code class="literal">M92</code>: This<a id="id744" class="indexterm"/> command sets <code class="literal">axis_steps_per_unit</code> to the same parameters as entered in the firmware. This is a good way to tweak <a id="id745" class="indexterm"/>the axes and E-motor calibration on the fly. Here's an example of the <code class="literal">M92</code> command-line syntax: <code class="literal">M92 X80 Y80 Z3200 E945</code>.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M303</code>: This command<a id="id746" class="indexterm"/> is the PID autotune. This generates<a id="id747" class="indexterm"/> the Proportional, Integral, and Derivative (PID) values for the hot end. Run this test every time you make a temperature change. The variables C = number of cycles to run test and S = target temperature. Here's an example of the <code class="literal">M303</code> command-line syntax: <code class="literal">M303 C8 S220</code>. Testing should be started with a cold hot end.</li><li class="listitem" style="list-style-type: disc"><code class="literal">M500</code>: This command <a id="id748" class="indexterm"/>stores <a id="id749" class="indexterm"/>parameters in EEPROM.</li></ul></div><p>More commands can<a id="id750" class="indexterm"/> be found at <a class="ulink" href="http://reprap.org/wiki/G-code">http://reprap.org/wiki/G-code</a> and <a class="ulink" href="http://www.thingiverse.com/thing:21546/">http://www.thingiverse.com/thing:21546/</a>.</p></div></body></html>
