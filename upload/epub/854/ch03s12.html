<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Using Skeinforge with a 0.35 mm nozzle</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Using Skeinforge with a 0.35 mm nozzle"><div class="titlepage"><div><div><h1 class="title"><a id="ch03lvl1sec46"/>Using Skeinforge with a 0.35 mm nozzle</h1></div></div></div><p>In this recipe, we will <a id="id228" class="indexterm"/>run a series of tests using the 0.35 mm nozzle to print the 0.3 mm, 0.2 mm, and 0.1 mm layers using a 3 mm filament.</p><p>Registering a bore size<a id="id229" class="indexterm"/> in Skeinforge is not a simple task. It requires you to calculate a ratio for each layer of a different thickness. Following the steps in this recipe will help you understand the process and make it easy to make adjustments for any change in the nozzle size or print layer.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note15"/>Note</h3><p>If you don't have a 0.35 mm nozzle, then refer to the recipe that covers your nozzle size.</p></div></div><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec103"/>Getting ready</h2></div></div></div><p>The Thingiverse website is a file-sharing site of user-created digital designs. It has many small models that can be used to make quick and efficient print tests. We'll use the one that's designed for surface calibration. It's a good shape for testing resolutions and small enough to print quickly, without much filament waste. It can be downloaded at <a class="ulink" href="http://www.thingiverse.com/thing:39050">www.thingiverse.com/thing:39050</a>.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec104"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Reopen the <span class="strong"><strong>Normal</strong></span> profile in Skeinforge. Under <span class="strong"><strong>Carve</strong></span>, adjust the <span class="strong"><strong>Edge Width over Height (ratio)</strong></span> to <code class="literal">1.4</code> and the <span class="strong"><strong>Layer Height (mm)</strong></span> to <code class="literal">0.3</code>.</li><li class="listitem">Under <span class="strong"><strong>Inset</strong></span>, adjust the <span class="strong"><strong>Infill Width over Thickness (ratio)</strong></span> to <code class="literal">1.4</code>.</li><li class="listitem">Click on <span class="strong"><strong>Save All</strong></span> and exit.</li><li class="listitem">Print the model.</li><li class="listitem">Reopen the <span class="strong"><strong>Normal</strong></span> profile in Skeinforge. Under <span class="strong"><strong>Carve</strong></span>, adjust the <span class="strong"><strong>Edge Width over Height (ratio)</strong></span> to <code class="literal">2.1</code> and the <span class="strong"><strong>Layer Height (mm)</strong></span> to <code class="literal">0.2</code>.</li><li class="listitem">Under <span class="strong"><strong>Inset</strong></span>, adjust the <span class="strong"><strong>Infill Width over Thickness (ratio)</strong></span> to <code class="literal">2.1</code>.</li><li class="listitem">Click on <span class="strong"><strong>Save All</strong></span> and exit.</li><li class="listitem">Print the model.</li><li class="listitem">Reopen the <span class="strong"><strong>Normal</strong></span> profile in Skeinforge. Under <span class="strong"><strong>Carve</strong></span>, adjust the <span class="strong"><strong>Edge Width over Height (ratio)</strong></span> to <code class="literal">4.2</code> and the <span class="strong"><strong>Layer Height (mm)</strong></span> to <code class="literal">0.1</code>.</li><li class="listitem">Under <span class="strong"><strong>Inset</strong></span>, adjust the <span class="strong"><strong>Infill Width over Thickness (ratio)</strong></span> to <code class="literal">4.2</code>.</li><li class="listitem">Click on <span class="strong"><strong>Save All</strong></span> and exit.</li><li class="listitem">Print the model.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec105"/>How it works...</h2></div></div></div><p>When we calculated a width over height ratio for the 0.5 mm nozzle, we added 0.1 mm to our nozzle size to allow <a id="id230" class="indexterm"/>swelling of the object. This is an increase of 20 percent. With a 0.35 mm nozzle, we're going to also add an additional amount using the same percentage that was used with the 0.5 mm nozzle. <a id="id231" class="indexterm"/>This will give us an extrusion width of 0.42 mm for our 0.35 mm nozzle. We'll use this value to calculate each of our ratios.</p><div class="informaltable"><table border="1"><colgroup><col style="text-align: left"/><col style="text-align: left"/></colgroup><thead><tr><th style="text-align: left" valign="bottom">
<p>0.35 mm nozzle</p>
</th><th style="text-align: left" valign="bottom">
<p>Ratio</p>
</th></tr></thead><tbody><tr><td style="text-align: left" valign="top">
<p>0.4 mm layer</p>
</td><td style="text-align: left" valign="top">
<p>x</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>0.3 mm layer</p>
</td><td style="text-align: left" valign="top">
<p>1.4</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>0.2 mm layer</p>
</td><td style="text-align: left" valign="top">
<p>2.1</p>
</td></tr><tr><td style="text-align: left" valign="top">
<p>0.1 mm layer</p>
</td><td style="text-align: left" valign="top">
<p>4.2</p>
</td></tr></tbody></table></div><p>The preceding chart provides a good starting point for each layer thickness using a 0.35 mm nozzle.</p></div></div></body></html>
