<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Let's print!</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Let's print!"><div class="titlepage"><div><div><h1 class="title"><a id="ch04lvl1sec55"/>Let's print!</h1></div></div></div><p>In the previous recipes, we learned how to make a model with precise measurements. Now, we'll take a look at how we can print a model with better accuracy. We'll start our printing session by first calibrating our <span class="emphasis"><em>x</em></span>, <span class="emphasis"><em>y</em></span>, and <span class="emphasis"><em>z</em></span> axes. This is one of the most important calibrations we can make, if we expect to print a part that is exactly the size of our model.</p><p>Then, we'll experiment with some slicer options that will help us tweak the precision of our part. These options are only available in Skeinforge. Conveniently, most of these plugins will have specific functions, and they can be left deactivated until a specific problem is encountered. Some of them are quick cheats to solving a problem, which may be elusive to fix elsewhere. We'll examine these options as we work our way through them.</p><p>The recipe named <span class="emphasis"><em>Controlling print warping</em></span> (in this chapter) will cover the methods to solve the warping of the parts printed using ABS. We'll use our warp test model that we made in SketchUp to help us solve this problem. Some of the mechanical tricks can be applied with the use of any slicer, but the Raft plugin option in Skeinforge will be our primary focus.</p><p>Slic3r has some limited raft options and another option called <span class="strong"><strong>Brim</strong></span>. We'll look at this feature as well.</p></div></body></html>
