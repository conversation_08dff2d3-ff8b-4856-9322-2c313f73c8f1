<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Wireframe modeling with TopMod</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Wireframe modeling with TopMod"><div class="titlepage"><div><div><h1 class="title"><a id="ch05lvl1sec68"/>Wireframe modeling with TopMod</h1></div></div></div><p>Two of the exciting features of TopMod are the wireframe<a id="id341" class="indexterm"/> and column modeling modes. With these tools, we can make a 3D model by converting each edge<a id="id342" class="indexterm"/> of a polygon mesh into a 3D pipe. This will translate the structure of our mesh into a fully formed 3D model that's watertight and is possible to 3D print.</p><p>In the past two recipes, we did a lot of work increasing and decreasing the number of polygons in a model. We'll see how this is important when we use the wireframe modeling tool with the model of the toy block.</p><div class="mediaobject"><img src="graphics/9888_05_16.jpg" alt="Wireframe modeling with TopMod"/></div><p>It's fairly easy to see in the preceding comparison, what would happen if we were to make a 3D pipe with each polygon edge in the toy block on the left. The edges of the block and the two dowels on top would be a jumbled mass of 3D pipes. The model we manipulated with subdivision schemes will make a more uniform structure of 3D pipes.</p><p>In this recipe, we will<a id="id343" class="indexterm"/> use wireframe and column tools to make some test<a id="id344" class="indexterm"/> models to test the concept of bridging, later in this chapter.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch05lvl2sec159"/>Getting ready</h2></div></div></div><p>You'll need to open a workspace in TopMod. You'll also need to load the lowest-decimated block file you made in the last recipe.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch05lvl2sec160"/>How to do it...</h2></div></div></div><p>First, we'll create our test models from a cube and then proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">
Go to the menu and select the <span class="strong"><strong>Cube Primitive</strong></span> icon. Once you have it sized on your workspace, choose the <span class="strong"><strong>Wireframe Modeling Mode</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888_05_17.jpg" alt="How to do it..."/></span>]. There are two similar icons. Choose the one on the left. At the floating <span class="strong"><strong>Tool Options</strong></span> window, keep the default value <code class="literal">0.250</code>. Select <span class="strong"><strong>Create Wireframe</strong></span> and save the model as <code class="literal">wireframe_model_default.obj</code>. Keep the model loaded on the workspace.
</li><li class="listitem">Select <span class="strong"><strong>Undo</strong></span> from the menu.</li><li class="listitem">Choose the <span class="strong"><strong>Wireframe Modeling Mode 2</strong></span> icon. This time, choose the one on the right. At the <span class="strong"><strong>Tool Options</strong></span> window, keep the default value <code class="literal">0.250</code> for <span class="strong"><strong>Width</strong></span> and change the default value <code class="literal">0.250</code> in <span class="strong"><strong>Thickness</strong></span> to <code class="literal">0.740</code>. Select <span class="strong"><strong>Create Wireframe</strong></span> and save the model as <code class="literal">wireframe_model_thick.obj</code>. Keep the model loaded on the workspace.</li><li class="listitem">Select <span class="strong"><strong>Undo</strong></span> from the menu.</li><li class="listitem">Choose the <span class="strong"><strong>Wireframe Modeling Mode 2</strong></span> icon. At the <span class="strong"><strong>Tool Options</strong></span> window, change the default value <code class="literal">0.250</code> for <span class="strong"><strong>Width</strong></span> to <code class="literal">0.750</code> and change back the <span class="strong"><strong>Thickness</strong></span> value to the default value <code class="literal">0.250</code>. Select <span class="strong"><strong>Create Wireframe</strong></span> and save the model as <code class="literal">wireframe_model_wide.obj</code>.</li><li class="listitem">Now, we'll work with our toy block. Go to the <span class="strong"><strong>File</strong></span> icon and open the <code class="literal">.obj</code> file of your lowest decimated saves of the toy block.</li><li class="listitem">Go to the menu and select <span class="strong"><strong>Remeshing</strong></span> and then <span class="strong"><strong>3-Conversion</strong></span>. From the next tiled window, select <span class="strong"><strong>Vertex Truncation</strong></span>. On the workspace, there's a floating <span class="strong"><strong>Tool Options</strong></span> window. Change the default value <code class="literal">0.250</code> in <span class="strong"><strong>Offset</strong></span> to <code class="literal">0.500</code>. Select <span class="strong"><strong>Perform Remeshing</strong></span> and save the model with a filename (maybe use a name such<a id="id345" class="indexterm"/> as <code class="literal">block_vertex_truncated.obj</code>). Don't close out the model from your desktop.</li><li class="listitem">Go to the menu and select the <span class="strong"><strong>Wireframe Modeling Mode</strong></span> icon from the <span class="strong"><strong>Tool Options</strong></span> window and change the default value <code class="literal">0.250</code> in <span class="strong"><strong>Width</strong></span> to <code class="literal">1.0</code> Select <span class="strong"><strong>Perform Remeshing</strong></span>, save the model, and close out.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch05lvl2sec161"/>How it works...</h2></div></div></div><p>When we used the<a id="id346" class="indexterm"/> wireframe-modeling tool, we made a three-sided polygon of each edge of our mesh. As we can see with our recipe experiments, we have a lot of control over the shape of this polygon by controlling the dimension of its width and thickness. The following image shows the different shapes of the polygon:</p><div class="mediaobject"><img src="graphics/9888_05_18.jpg" alt="How it works..."/></div><p>There are varieties of mesh schemes that can give us interesting mesh patterns. Sometimes the results can be very subtle, for example, when we compare the vertex truncation and honeycomb schemes. On the toy block, if you follow the diagonal line from the bottom-left corner moving up to the top-right corner on both meshes, you can see that the vertex truncation mesh is smoother in comparison to the honeycomb mesh, as shown in the following image:</p><div class="mediaobject"><img src="graphics/9888_05_19.jpg" alt="How it works..."/></div><p>Sometimes the choice of mesh <a id="id347" class="indexterm"/>can be instrumental in giving us a<a id="id348" class="indexterm"/> structure that works well with the wireframe mode, or simply be for aesthetic reasons, as shown in the following image:</p><div class="mediaobject"><img src="graphics/9888_05_20.jpg" alt="How it works..."/></div><p>The choice to use the vertex truncated mesh for the toy block was made because it created an interesting pattern that would translate well with the wireframe modeling tool. The following image shows the comparison between the loop and vertex truncated mesh:</p><div class="mediaobject"><img src="graphics/9888_05_21.jpg" alt="How it works..."/></div><p>There's a lot of room for <a id="id349" class="indexterm"/>exploring here. Depending on the form of the <a id="id350" class="indexterm"/>model and the manner in which the mesh is structured, there are many possibilities for very interesting results.</p></div><div class="section" title="There's more..."><div class="titlepage"><div><div><h2 class="title"><a id="ch05lvl2sec162"/>There's more...</h2></div></div></div><p>The <span class="strong"><strong>Column Modeling</strong></span> mode, displayed by the icon [<span class="inlinemediaobject"><img src="graphics/9888_05_22.jpg" alt="There's more..."/></span>], is another way to create a 3D pipe of each edge of a polygon-based mesh. The wireframe gave us a three-sided polygon, whereas the column mesh gives us four sides or more. With this tool, by generating a high number of sides, we can create a cylinder from each edge.</p><div class="mediaobject"><img src="graphics/9888_05_23.jpg" alt="There's more..."/></div></div></div></body></html>
