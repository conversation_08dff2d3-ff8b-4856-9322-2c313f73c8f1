<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Creating a hollow model with MeshLab</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Creating a hollow model with Mesh<PERSON>ab"><div class="titlepage"><div><div><h1 class="title"><a id="ch08lvl1sec111"/>Creating a hollow model with MeshLab</h1></div></div></div><p>Creating a hollow model with <PERSON>sh<PERSON>ab is not about repair issues, but it is about the modifications necessary<a id="id601" class="indexterm"/> for proper preparation of a model that is intended for a commercial 3D printing service. Most of these services charge by<a id="id602" class="indexterm"/> calculating the volume of material required to print the design. In this case, by making a model hollow, it will decrease the volume of material necessary and greatly decrease the cost of the print job.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec270"/>Getting ready</h2></div></div></div><p>You'll need MeshLab and the support test model we made in <a class="link" href="ch06.html" title="Chapter&#xA0;6.&#xA0;Making the Impossible">Chapter 6</a>, <span class="emphasis"><em>Making the Impossible</em></span>. It looks like the following image:</p><div class="mediaobject"><img src="graphics/9888OS_08_30.jpg" alt="Getting ready"/></div></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec271"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Select the <span class="strong"><strong>Show Layer Dialog</strong></span> icon.</li><li class="listitem">Go to <span class="strong"><strong>Filters</strong></span> and select <span class="strong"><strong>Remeshing</strong></span>, <span class="strong"><strong>Simplification</strong></span>, and <span class="strong"><strong>Reconstruction</strong></span>. Scroll down to the next cascaded window and select <span class="strong"><strong>Uniform Mesh Resampling</strong></span>.</li><li class="listitem">From the pop up, change the <span class="strong"><strong>Precision (abs and %)</strong></span> field to <code class="literal">1.0</code> in <span class="strong"><strong>World Unit</strong></span>. Change <span class="strong"><strong>Offset (abs and %)</strong></span> to <code class="literal">-3.0</code>. Check <span class="strong"><strong>Clean Vertices</strong></span> and <span class="strong"><strong>Multisample</strong></span>. Click on <span class="strong"><strong>Apply</strong></span>.</li><li class="listitem">Change the view to Wireframe. Toggle the view of the two layers from the layer box (remember that by clicking on the green eye icon, it shows/hides the layer). We can<a id="id603" class="indexterm"/> see the results in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_31.jpg" alt="How to do it..."/></div><p>The image on the right-hand side is the new interior wall we made. We can see it nested within the model on the left-hand side.</p></li><li class="listitem">Now, let's<a id="id604" class="indexterm"/> check the normals. Go to the menu, select <span class="strong"><strong>Render</strong></span>, and choose <span class="strong"><strong>Show Normal/Curvature</strong></span> from the list. Hide the outer layer. We can see the new part in the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_08_32.jpg" alt="How to do it..."/></div></li><li class="listitem">All of the <a id="id605" class="indexterm"/>normals are facing outward as we normally would want them to. In this case, we want them<a id="id606" class="indexterm"/> pointing inward for this model to be understood as a hollow object. We'll do this by keeping the first layer hidden and making sure that the second layer is selected in the layer box. Then, go to the menu and select <span class="strong"><strong>Filters</strong></span>. Choose <span class="strong"><strong>Normals</strong></span>, <span class="strong"><strong>Curvatures</strong></span>, and <span class="strong"><strong>Orientation</strong></span> and choose <span class="strong"><strong>Invert Faces Orientation</strong></span> from the cascaded window. From the pop up, keep the default settings. Click on <span class="strong"><strong>Apply</strong></span> and then on <span class="strong"><strong>Close</strong></span>. Now, the normals are facing inward. Refer to the following image:<div class="mediaobject"><img src="graphics/9888OS_08_33.jpg" alt="How to do it..."/></div></li><li class="listitem">Now, we have to merge the two layers together to make a complete model that is hollow. Go to <span class="strong"><strong>Menu</strong></span> and select <span class="strong"><strong>Filters</strong></span>. Scroll down and select <span class="strong"><strong>Mesh Layer</strong></span>. Scroll down to the next cascaded window and select <span class="strong"><strong>Flatten Visible Layers</strong></span>.</li><li class="listitem">A pop-up window appears. Keep the options at default and select <span class="strong"><strong>Apply</strong></span>. When the filter is complete, click on <span class="strong"><strong>Close</strong></span> to close the window. The model can now be exported as a <code class="literal">.stl</code> file.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec272"/>How it works...</h2></div></div></div><p>By setting the <span class="strong"><strong>Offset (abs and %)</strong></span> field to <code class="literal">-3.0</code>, we essentially determined that the inset would begin <a id="id607" class="indexterm"/>3 mm from the outer shell. This creates a shell<a id="id608" class="indexterm"/> that is 3 mm thick. The offset variable can be set as a negative or positive integer. For instance, by setting the value to <code class="literal">3.0</code>, we can increase the model's size by 3 mm from the original shell. Refer to the following image:</p><div class="mediaobject"><img src="graphics/9888OS_08_34.jpg" alt="How it works..."/></div><p>However, when we<a id="id609" class="indexterm"/> create a shell extending outward<a id="id610" class="indexterm"/> from the original surface, the model becomes distorted.</p></div></div></body></html>
