<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Creating a drain hole with Meshmixer</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Creating a drain hole with Meshmixer"><div class="titlepage"><div><div><h1 class="title"><a id="ch08lvl1sec112"/>Creating a drain hole with Meshmixer</h1></div></div></div><p>One of the most popular 3D printing<a id="id611" class="indexterm"/> services is <span class="strong"><strong>Shapeways</strong></span>. It can be <a id="id612" class="indexterm"/>found at <a class="ulink" href="https://www.shapeways.com/">https://www.shapeways.com/</a>. This service requires the model to have a drain hole if it is hollow or else it<a id="id613" class="indexterm"/> will be considered a solid. This is to<a id="id614" class="indexterm"/> allow the support material that's utilized to be removed.</p><p>In this recipe, we'll learn how to do this using Meshmixer and a fabricated part using SketchUp.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec273"/>Getting ready</h2></div></div></div><p>You'll need to make a small part with SketchUp. A dowel with a 3-mm diameter and a length of about 20 mm will suffice. It should resemble the following image:</p><div class="mediaobject"><img src="graphics/9888OS_08_35.jpg" alt="Getting ready"/></div><p>You'll also need Meshmixer<a id="id615" class="indexterm"/> and the hollow <a id="id616" class="indexterm"/>support model you made in the previous recipe.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec274"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Import the hollow support model. Adjust the orientation of the model with a good view of its bottom. The least obtrusive area for a small hole would be at the bottom. This is marked in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_36.jpg" alt="How to do it..."/></div></li><li class="listitem">Now, go to <span class="strong"><strong>File</strong></span> and select <span class="strong"><strong>Import</strong></span> and then <span class="strong"><strong>Append</strong></span>. Choose the dowel model.</li><li class="listitem">In this particular case (it may be different in yours), a warning may appear. The imported dowel is too far away from the current scene. Meshmixer prompts that<a id="id617" class="indexterm"/> an automatic repair can be made. Choose <span class="strong"><strong>Yes</strong></span>.</li><li class="listitem">Initially, after<a id="id618" class="indexterm"/> loading the dowel, only the support model can be seen. At the bottom-right corner, there is an <span class="strong"><strong>Object Browser</strong></span> window. It shows that the dowel has been imported. Click on the eye icon of the support model. We can now see the dowel as shown in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_37.jpg" alt="How to do it..."/></div></li><li class="listitem">Now, we want to position the dowel where it extrudes halfway between the shells of the model at the future hole's position. We do this by selecting the dowel layer at the <span class="strong"><strong>Object Browser</strong></span> window. We'll need to see the outer surface of the model, so unhide the support model layer. Go to <span class="strong"><strong>Edit</strong></span> and choose <span class="strong"><strong>Transform</strong></span>. You should see a set of navigation controls, as seen in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_38.jpg" alt="How to do it..."/></div></li><li class="listitem">By left-clicking<a id="id619" class="indexterm"/> and dragging any of the three axes' arrows, the dowel can be positioned accurately. This is illustrated in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_39.jpg" alt="How to do it..."/></div></li><li class="listitem">Now, we need<a id="id620" class="indexterm"/> to subtract the dowel in order to make a hole in its place. Before we do this, it's very important to make sure that the mesh between the two parts is fairly uniform in density. Go to <span class="strong"><strong>View</strong></span> in the menu and select <span class="strong"><strong>Toggle Wireframe</strong></span>. In the following image on the left-hand side, we can see that the mesh between the dowel and model is not uniform.</li><li class="listitem">We can make the two components more uniform using a sculpting brush. Go to <span class="strong"><strong>Sculpt</strong></span>. In <span class="strong"><strong>Volume selected</strong></span>, choose <span class="strong"><strong>Brushes</strong></span>. Choose the <span class="strong"><strong>Refine Brush</strong></span> (it's a triangle with a plus symbol). Make sure that only the dowel part is selected in the <span class="strong"><strong>Object Browser</strong></span> window. Refer to the following image:<div class="mediaobject"><img src="graphics/9888OS_08_40.jpg" alt="How to do it..."/></div><p>Adjust the size and strength by trial and error, moving the brush over the dowel. The end result should be similar to the preceding image on the right-hand side.</p></li><li class="listitem">Now, we're ready<a id="id621" class="indexterm"/> to remove the dowel. Press <span class="emphasis"><em>Shift</em></span> and select both parts in the <span class="strong"><strong>Object Browser</strong></span> window. Make sure that the model is selected first, and then the dowel. A new menu with<a id="id622" class="indexterm"/> Boolean functions appears. We want to select <span class="strong"><strong>Boolean Difference</strong></span>. Once the selection is made, the dowel disappears and a hole is created. This can be seen in the following image:<div class="mediaobject"><img src="graphics/9888OS_08_41.jpg" alt="How to do it..."/></div></li><li class="listitem">Choose <span class="strong"><strong>Accept</strong></span> and the model is ready for export.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec275"/>How it works...</h2></div></div></div><p>Using the Boolean functions in Meshmixer can be a little quirky, so it's a good idea to save your model before performing the Boolean difference. The trick is to make sure that the mesh of the two parts is relatively uniform. In most cases, this will ensure a good cut.</p></div><div class="section" title="There's more..."><div class="titlepage"><div><div><h2 class="title"><a id="ch08lvl2sec276"/>There's more...</h2></div></div></div><p>How successful was<a id="id623" class="indexterm"/> the modification of the support model? How much does it save in material costs?</p><p>In the following<a id="id624" class="indexterm"/> image, we can see that by Shapeways' calculations, we essentially saved half the cost by printing a hollow object rather than a solid:</p><div class="mediaobject"><img src="graphics/9888OS_08_42.jpg" alt="There's more..."/></div></div></div></body></html>
