<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Getting Windows to recognize the controller</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Getting Windows to recognize the controller"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec122"/>Getting Windows to recognize the controller</h1></div></div></div><p>This appendix will cover two<a id="id698" class="indexterm"/> basic controller designs that are common for RepRap-based printers:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc">Arduino Mega 2560 is based on the ATmega2560 microchip (the older ATmega 1280 may also be used on older machines)</li><li class="listitem" style="list-style-type: disc">Sanguinololu is based on the ATMEGA644P microchip</li></ul></div><p>If this is the first time you have tried to upload some firmware, you'll discover that there is a need to install the drivers that are specific to the controller.</p><p>For the Arduino Mega<a id="id699" class="indexterm"/>, you'll need to follow these steps:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Plug the board in using the USB connection. Windows will attempt to find a device driver for the board and will most likely fail.</li><li class="listitem">Open <span class="strong"><strong>Device Manager</strong></span> in your system. Locate the yellow exclamation mark labeled <span class="strong"><strong>Arduino Mega 2560</strong></span> under <span class="strong"><strong>Ports</strong></span> or <span class="strong"><strong>Other Devices</strong></span>.</li><li class="listitem">Choose the <span class="strong"><strong>"Browse my computer for driver software"</strong></span> option. Locate the <code class="literal">drivers</code> folder in the <code class="literal">Arduino</code> folder. Select the <code class="literal">drivers</code> folder and then click on <span class="strong"><strong>Next</strong></span> to install. The Arduino Mega should now be recognized and assigned with a port number. Take note of this number.</li></ol></div><p>If you plug in a controller and it's not recognized<a id="id700" class="indexterm"/> at all, you'll need to install a Virtual COM Port driver<a id="id701" class="indexterm"/>. You can download this from <a class="ulink" href="http://www.ftdichip.com/Drivers/VCP.htm">http://www.ftdichip.com/Drivers/VCP.htm</a>. Extract all the folders and install the driver.</p></div></body></html>
