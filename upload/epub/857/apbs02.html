<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Viewing G-code</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Viewing G-code"><div class="titlepage"><div><div><h1 class="title"><a id="ch09lvl1sec130"/>Viewing G-code</h1></div></div></div><p>There are many options <a id="id729" class="indexterm"/>available to see the written G-code generated by your slicer. Both Repetier-Host and Printrun have the ability to show and edit the text of G-code.</p><p>An ordinary text application such as Windows Notepad can also be used. The following example is a G-code of a 20 mm test cube generated by Skeinforge and Slic3r:</p><div class="mediaobject"><img src="graphics/9888OS_AppendixB_06.jpg" alt="Viewing G-code"/></div><p>The obvious difference between the two slicer versions is the inclusion of comments in the header of the Slic3r G-code.</p><p>Slic3r creates a list at the beginning. This list overviews the parameters that have been chosen in the profile. Each line is commented out using a semicolon (;) before each comment line. Skeinforge doesn't create any comments.</p><p>Adding comments can be a useful feature when experimenting with different slicer profiles. By adding a description of short settings to the G-code, the attached documentation won't be lost. <a id="id730" class="indexterm"/>A comment can be added by simply typing in the information after a semicolon and saving the change. Just be careful that the text editor saves the file with the <code class="literal">.gcode</code> extension.</p></div></body></html>
