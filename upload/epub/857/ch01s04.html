<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Viewing your scene with 123D Catch</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Viewing your scene with 123D Catch"><div class="titlepage"><div><div><h1 class="title"><a id="ch01lvl1sec13"/>Viewing your scene with 123D Catch</h1></div></div></div><p>In this recipe, we will cover <a id="id17" class="indexterm"/>some of the basic operations <a id="id18" class="indexterm"/>of 123D Catch. These include operations such as loading a scene and navigation.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec14"/>Getting ready</h2></div></div></div><p>You'll need a processed 3D scene of your object. Autodesk will e-mail you with a notification that your capture has been successful. A link will be provided to download the Photo Scene data file (<code class="literal">.3dp</code>). The filename will start with the word <code class="literal">Capture</code>, the date it was created, and a series of numbers. Create a new folder and download the file into it.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec15"/>How to do it...</h2></div></div></div><p>Open 123D Catch and proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Select the middle-left button, <span class="strong"><strong>Open an Existing Capture</strong></span>. From the <span class="strong"><strong>Select the project to open</strong></span> window, find the directory containing the folder with your<a id="id19" class="indexterm"/> Photo Scene data file. Select the file and click on <span class="strong"><strong>Open</strong></span>. The workspace appears, and Autodesk loads the project files. This takes several minutes. When the scene appears, it should look similar to following screenshot:<div class="mediaobject"><img src="graphics/9888OS_01_06.jpg" alt="How to do it..."/></div></li><li class="listitem">At the <a id="id20" class="indexterm"/>center of the workspace, we see the object that we scanned and the area that surrounds it. This is the content of the 3D scene. A series of cameras connected by a line surround the model. This shows the chronology of the photos taken. Below the workspace is a thumbnail strip containing the photos you took. Click on <span class="strong"><strong>Animation Path</strong></span> at the top of the first thumbnail. From the drop-down menu, choose <span class="strong"><strong>Create default animation path</strong></span>.<div class="mediaobject"><img src="graphics/9888OS_01_07.jpg" alt="How to do it..."/></div></li><li class="listitem">The camera track of the scene is now highlighted, as shown in the preceding screenshot. A<a id="id21" class="indexterm"/> new thumbnail series also appears. These are the keyframes of the animation. To play, click on the arrow pointing to the right, and to stop, click on the square icon.<div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip03"/>Tip</h3><p>If the animation is not playing in fullscreen, slightly move the corner of the program's window in or out.</p></div></div></li><li class="listitem">Let's return to<a id="id22" class="indexterm"/> the global view. We'll do this by pressing Space bar twice.</li><li class="listitem">We can view the individual thumbnails by scrolling along the bottom. Each thumbnail will be highlighted if we hover over it. Select a thumbnail by left- clicking on it. The photo will now be in view.</li><li class="listitem">To return to the global view, press Space bar once. The view will show the particular camera within the chronology track (which took the selected thumbnail) highlighted in green.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch01lvl2sec16"/>How it works...</h2></div></div></div><p>When you opened the Photo Scene data file, two things happened. First, there were additions made to the folder where the <code class="literal">.3dp</code> file is stored; photos that comprised your scene were added, and another folder was created with the same name as your <code class="literal">.3dp</code> file (this folder contains two Autodesk files that we should not touch). Secondly, the scene was loaded on your workspace.</p><p>This scene includes a visual track of the movements you made when photographing your object. This enables a convenient review of the photos for the purpose of editing or fixing stitching problems<a id="id23" class="indexterm"/> that may occur. Stitching is the term that<a id="id24" class="indexterm"/> 123D Catch uses for describing the correlation of photos in a sequence. We'll examine stitching in greater detail in the next recipe.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip04"/>Tip</h3><p>The animation that we played in this recipe can be saved to the desktop or uploaded on YouTube. While the animation is loading, right-click on the workspace for a pop-up window and choose <span class="strong"><strong>Export Video</strong></span>.</p></div></div></div></div></body></html>
