<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Scaling the model with Meshmixer</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Scaling the model with Meshmixer"><div class="titlepage"><div><div><h1 class="title"><a id="ch02lvl1sec25"/>Scaling the model with Meshmixer</h1></div></div></div><p>Measurement in the virtual space can be considered arbitrary; what matters is the relative scale. When a model is created by<a id="id98" class="indexterm"/> 123D Catch, the size of the object is <a id="id99" class="indexterm"/>measured in an arbitrary scale of units. These units can then be interpolated by other 3D modeling programs using metric or imperial units of measurement. We can see how Meshmixer interpolates the size of the pyramid in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_02_08.jpg" alt="Scaling the model with Meshmixer"/></div><p>The pyramid measures roughly 23 x 17 x 33 millimeters. In reality, the pyramid is more than 4 meters high.<a id="id100" class="indexterm"/> What is important for us is a scale that will print on our 3D printer.</p><p>In this recipe, we'll learn <a id="id101" class="indexterm"/>how to scale our model for printing with Meshmixer.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec45"/>Getting ready</h2></div></div></div><p>You'll need the models that you created with 123D Catch.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec46"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">Select <span class="strong"><strong>Import+</strong></span> to import a model into Meshmixer.</li><li class="listitem">Go to <span class="strong"><strong>Analysis</strong></span>. From the pop-up window, choose <span class="strong"><strong>Units/Scale</strong></span>.</li><li class="listitem">In the <span class="strong"><strong>Units/Scale</strong></span> properties window, enter the measurement that you want for your model. In this case, a measurement of 100 mm is entered for the <span class="emphasis"><em>z</em></span> axis. The other axes are adjusted automatically for proper scaling of all the axes.</li><li class="listitem">Save your model.</li></ol></div></div><div class="section" title="How it works..."><div class="titlepage"><div><div><h2 class="title"><a id="ch02lvl2sec47"/>How it works...</h2></div></div></div><p>When the model is<a id="id102" class="indexterm"/> scaled in Meshmixer, there's no visual indication<a id="id103" class="indexterm"/> of a change in size in the workspace. What changes are the measurement values and the <span class="strong"><strong>Grid Size</strong></span> value in the <span class="strong"><strong>Properties</strong></span> window.</p></div></div></body></html>
