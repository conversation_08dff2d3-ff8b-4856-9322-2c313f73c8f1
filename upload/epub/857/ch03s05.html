<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Viewing the model in MeshLab</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Viewing the model in MeshLab"><div class="titlepage"><div><div><h1 class="title"><a id="ch03lvl1sec39"/>Viewing the model in MeshLab</h1></div></div></div><p>In this recipe, we'll learn how <a id="id183" class="indexterm"/>to import/export a model into MeshLab and <a id="id184" class="indexterm"/>cover the basic navigation controls for the camera view. We'll also examine our model by changing the way we view its mesh.</p><div class="section" title="Getting ready"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec82"/>Getting ready</h2></div></div></div><p>You'll need to download MeshLab<a id="id185" class="indexterm"/> from <a class="ulink" href="http://meshlab.sourceforge.net/">http://meshlab.sourceforge.net/</a>. This program is available for both PC and Mac platforms. Install the correct version for your computer. You'll also need your <code class="literal">.obj</code> files that you saved in DAVID Laserscanner.</p></div><div class="section" title="How to do it..."><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec83"/>How to do it...</h2></div></div></div><p>We will proceed as follows:</p><div class="orderedlist"><ol class="orderedlist arabic"><li class="listitem">
When you open MeshLab, you will see a long bar displayed under <span class="strong"><strong>Menu</strong></span>. It contains a row of icons that provides quick access to various program functions. Select the <span class="strong"><strong>Import Mesh</strong></span> icon [<span class="inlinemediaobject"><img src="graphics/9888OS_03_15.jpg" alt="How to do it..."/></span>] and locate the folder containing your <code class="literal">.obj</code> files.
</li><li class="listitem">In this recipe, the first scan of the toy block is opened. This can be seen in the following screenshot:<div class="mediaobject"><img src="graphics/9888OS_03_16.jpg" alt="How to do it..."/></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip08"/>Tip</h3><p>You can also simply drag-and-drop the <code class="literal">.obj</code> file to the MeshLab's workspace.</p></div></div></li><li class="listitem">To freely orbit around the model, simply left-click and drag.</li><li class="listitem">To pan the model, hold down on the scroll wheel and drag. If you don't have a scroll wheel, hold <span class="emphasis"><em>Ctrl</em></span> on the keyboard.</li><li class="listitem">To zoom in on<a id="id186" class="indexterm"/> the model, rotate the scroll wheel. If you<a id="id187" class="indexterm"/> don't have a scroll wheel, hold <span class="emphasis"><em>Alt</em></span> on the keyboard and drag.</li><li class="listitem">Position and size your model until it fills your screen. At the icon bar, select each of the mesh views and observe the model, starting on the right-hand side with <span class="strong"><strong>Smooth</strong></span>, <span class="strong"><strong>Flat</strong></span>, <span class="strong"><strong>Flat Lines</strong></span>, and <span class="strong"><strong>Hidden Lines</strong></span>, and ending with <span class="strong"><strong>Wireframe</strong></span>.<div class="mediaobject"><img src="graphics/Image1.jpg" alt="How to do it..."/></div></li></ol></div></div><div class="section" title="See also"><div class="titlepage"><div><div><h2 class="title"><a id="ch03lvl2sec84"/>See also</h2></div></div></div><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc">There are more precise options for maneuvering the object, which can be found on the MeshLab site: <a class="ulink" href="http://sourceforge.net/apps/mediawiki/meshlab/index.php?title=Interacting_with_the_mesh">http://sourceforge.net/apps/mediawiki/meshlab/index.php?title=Interacting_with_the_mesh</a></li><li class="listitem" style="list-style-type: disc">For a video tutorial, Mister<a id="id188" class="indexterm"/> P offers <span class="emphasis"><em>MeshLab Basics: Navigation</em></span>, which can be accessed on<a id="id189" class="indexterm"/> YouTube at <a class="ulink" href="http://www.youtube.com/watch?v=Sl0vJfmj5LQ">http://www.youtube.com/watch?v=Sl0vJfmj5LQ</a></li></ul></div></div></div></body></html>
