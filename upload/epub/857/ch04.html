<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Chapter&#xA0;4.&#xA0;Modeling and Printing with Precision</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="chapter" title="Chapter&#xA0;4.&#xA0;Modeling and Printing with Precision"><div class="titlepage"><div><div><h1 class="title"><a id="ch04"/>Chapter&#xA0;4.&#xA0;Modeling and Printing with Precision</h1></div></div></div><p>In this chapter, we will cover the following recipes:</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem" style="list-style-type: disc">Warming up with SketchUp</li><li class="listitem" style="list-style-type: disc">Using 3D tools from the Extension Warehouse</li><li class="listitem" style="list-style-type: disc">Modeling with SketchUp</li><li class="listitem" style="list-style-type: disc">Using plugin extensions with SketchUp</li><li class="listitem" style="list-style-type: disc">Calibrating the x, y, and z axes</li><li class="listitem" style="list-style-type: disc">Controlling the flow rate in Skeinforge</li><li class="listitem" style="list-style-type: disc">Adjusting the scale in Skeinforge</li><li class="listitem" style="list-style-type: disc">Using Stretch in Skeinforge</li><li class="listitem" style="list-style-type: disc">Controlling print warping</li><li class="listitem" style="list-style-type: disc">Using brim with Slic3r</li></ul></div><div class="section" title="Introduction"><div class="titlepage"><div><div><h1 class="title"><a id="ch04lvl1sec50"/>Introduction</h1></div></div></div><p>In this chapter, we're going to use at a 3D CAD program called <span class="strong"><strong>SketchUp Make</strong></span><a id="id246" class="indexterm"/>. What makes this program different from most CAD programs is the ease of its use. SketchUp is not a parametric modeler such as AutoCAD or Solidworks. It doesn't require heavy training and a lot of investment in money. It's an easy, intuitive modeler that allows for direct interaction with the geometry. This provides greater flexibility in making a model on the spur of a moment when creativity sparks.</p><p>Later in this chapter, we'll learn how to calibrate the mechanics of our printing by making some changes in the motion of the printer. We'll also examine a variety of solutions for controlling print warping. Controlling warping of prints made with ABS is a very common issue. We'll take a look at different solutions for this problematic issue.</p></div></div></body></html>
