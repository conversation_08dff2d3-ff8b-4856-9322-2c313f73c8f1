<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Reviewing the print results</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Reviewing the print results"><div class="titlepage"><div><div><h1 class="title"><a id="ch04lvl1sec62"/>Reviewing the print results</h1></div></div></div><p>Remember all the versions we've made of the toy block? This also includes the toy block we made with SketchUp<a id="id311" class="indexterm"/> in this chapter. Here's a comparison of all the prints in the following image:</p><div class="mediaobject"><img src="graphics/9888OS_04_34.jpg" alt="Reviewing the print results"/></div><p>With each successive stage, we have made a better replication of the toy block. Our best result is the model we made with SketchUp. Not only is it better, but it was modeled faster and easier than the others. When mechanical- or geometric-based forms are needed, using a 3D modeling program such as SketchUp is the best solution.</p></div></body></html>
