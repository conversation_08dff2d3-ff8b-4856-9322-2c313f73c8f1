<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
.container-div {
    width: 80%;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.header-item {
    text-align: center;
    margin-bottom: 20px;
}

h1 {
    color: #333;
    font-size: 2rem;
}

.form-section, .table-section {
    margin-bottom: 20px;
}

.form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between;
}

.form-group {
    flex: 1 1 calc(30% - 20px);
    display: flex;
    flex-direction: column;
}

label {
    font-weight: bold;
    margin-bottom: 5px;
}

input {
    padding: 8px;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

button {
    padding: 10px 20px;
    background-color:#F79420;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #45a049;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table, th, td {
    border: 1px solid #ddd;
}

th, td {
    padding: 12px;
    text-align: center;
}

th {
    background-color: #F79420;
    color: white;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

.btn {
    margin-top: 20px;
    padding: 8px;
}

.btn-add {
    align-self: flex-start;
}

.btn-delete {
    background-color: #f44336;
}

.btn-delete:hover {
    background-color: #e53935;
}
</style>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-div">
    <div class="header-item">
        <h1>Difficulty Level Mapping</h1>
    </div>

    <section class="form-section">
        <form id="mappingForm" class="form">
            <div class="form-group">
                <label for="type">Type:</label>
                <input type="text" id="type" name="type" placeholder="e.g., Medium" required>
            </div>
            <div class="form-group">
                <label for="value">Value:</label>
                <input type="text" id="value" name="value" placeholder="e.g., Intermediate, Moderate" required>
            </div>
            <div class="form-group">
                <button type="button" class="btn btn-add" onclick="addMapping()">Add Mapping</button>
            </div>
        </form>
    </section>

    <section class="table-section">
        <table id="mappingTable">
            <thead>
            <tr>
                <th>Type</th>
                <th>Value</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            <!-- Dynamic Rows -->
            </tbody>
        </table>
    </section>
</div>
<script>
    async function addMapping(){
        const type  = document.getElementById('type').value
        const values  = document.getElementById('value').value

        const diffObj = {
            key:type,
            values
        }
        $('.loading-icon').removeClass('hidden');
        const response = await fetch("/admin/addDifficultyMapping",{
            method:"POST",
            body:JSON.stringify(diffObj),
            headers:{
                "Content-Type":"application/json"
            }
        })
        if(!response.ok){
            alert("Something went wrong")
            return;
        }
        const result = await response.json();
        $('.loading-icon').addClass('hidden');
        alert(result.message)
    }

    document.addEventListener('DOMContentLoaded', function () {
        fetchDifficultyMappings();
    });
    async function fetchDifficultyMappings() {
        try {
            $('.loading-icon').removeClass('hidden');
            const response = await fetch('/admin/getDifficultLevels');
            const data = await response.json();
            const difficultyLevels = data.difficultylevels;
            const tableBody = document.querySelector('#mappingTable tbody');
            tableBody.innerHTML = '';

            for (let type in difficultyLevels) {
                const row = document.createElement('tr');

                const typeCell = document.createElement('td');
                typeCell.textContent = type;
                row.appendChild(typeCell);

                const valueCell = document.createElement('td');
                valueCell.style.width = '350px'
                valueCell.textContent = difficultyLevels[type].join(', ');
                row.appendChild(valueCell);

                const actionsCell = document.createElement('td');
                const inputField = document.createElement('input');
                inputField.style.width= '300px'
                inputField.type = 'text';
                inputField.value = difficultyLevels[type].join(', ');

                const updateButton = document.createElement('button');
                updateButton.textContent = 'Update';
                updateButton.addEventListener('click', async function () {
                    const newValues = inputField.value;
                    await updateDifficultyMapping(type, newValues);
                });

                actionsCell.appendChild(inputField);
                actionsCell.appendChild(updateButton);
                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            }
            $('.loading-icon').addClass('hidden');
        } catch (error) {
            $('.loading-icon').addClass('hidden');
            console.error('Error fetching difficulty mappings:', error);
        }
    }

    async function updateDifficultyMapping(type, values) {
        try {
            $('.loading-icon').removeClass('hidden');
            const response = await fetch('/admin/addDifficultyMapping', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    key: type,
                    values: values
                })
            });
            const data = await response.json();
            if (data.status === 200) {

                alert('Mapping updated successfully!');
                await fetchDifficultyMappings();
                $('.loading-icon').addClass('hidden');
            } else {
                alert('Failed to update mapping.');
            }
        } catch (error) {
            $('.loading-icon').addClass('hidden');
            alert('Error updating mapping.');
            console.error('Error updating difficulty mapping:', error);
        }
    }
</script>
<g:render template="/${session['entryController']}/footer_new"></g:render>