<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <div class="form-group mb-4">
                    <label for="institutes" style="display: block;">Institute</label>
                    <div class="flex_st ad">
                        %{--                  <div class="col-md-4">--}%
                        <select name="institutes" id="institutes" class="form-control col-3" onchange="instituteChanged();">
                            <option value="" disabled="disabled" selected>Select Institute</option>
                            <g:each in="${institutes}" var="institute" status="i">
                                <option value="${institute.id}">${institute.name}</option>
                            </g:each>
                        </select>
                    </div>

                </div>
            </div>

            <div class="">
                <label class="text-secondary text-secondary-modifier">Batch</label>
                <div class="form-group form-inline">
                    <select name="batches" id="batches" class="form-control col-md-3 mr-4" style="">
                        <option value="" disabled="disabled" selected>Select batch</option>
                        <g:each in="${batchesList}" var="batch" status="i">
                            <option value="${batch.id}">${batch.name + (batch.endDate!=null?" - Completion date: "+(new SimpleDateFormat("dd-MM-yyyy")).format(batch.endDate):"")}</option>
                        </g:each>
                    </select>
                    <input type="number" min="1" max="99" class="form-control col-md-3 mr-4" name="month" id="month"  placeholder="Enter validity(months)" autocomplete="off" onkeypress="return onlyNumberKey(event)">
                </div>
                <div class="form-group form-inline">
                    <button class="btn btn-primary" onclick="showAllUsers('student')">Get Students</button>
                </div>
            </div>


            <div id="errormsg" class="alert alert-danger has-error mt-4" role="alert" style="display: none; background: none;"></div>
            <div id="successmsg" class="alert alert-success border-success mt-4" style="display: none; background: none;"></div>
            <div id="batchUsers" class="my-4" style="display: none"></div>
        </div>
    </div>
</div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var instituteBatches={};
    var siteId1 = "${session["siteId"]}"
    <%batchesList.each{batch->%>
    if(!instituteBatches[${batch.conductedFor}])
        instituteBatches[${batch.conductedFor}] = [];
    instituteBatches[${batch.conductedFor}].push(''+${batch.id});
    <%}%>

    function instituteChanged(){
        var batchesArray = instituteBatches[document.getElementById("institutes").value];
        var batches = document.getElementById('batches');

        for(var i=1; i < batches.length; i++)
        {
            if(batchesArray.includes(''+batches.options[i].value)){
                $("#batches option[value='"+batches.options[i].value+"']").show();
            }else{
                $("#batches option[value='"+batches.options[i].value+"']").hide();
            }
        }
    }

    var batchId="";
    function showAllUsers(userType){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();
        var batch = document.getElementById("batches");
        if(batch.selectedIndex==0){
            document.getElementById("errormsg").innerHTML="Please select a batch"
            $("#errormsg").show();
        }else if(document.getElementById("month").value==""){
            document.getElementById("errormsg").innerHTML="Please enter validity"
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
             batchId = batch.value;
            var month=document.getElementById("month").value;
            <g:remoteFunction controller="admin" action="getBatchUsersByMonths" params="'batchId='+batchId+'&month='+month" onSuccess = "showUsersForBatch(data);"/>
        }
    }

    $("#month").on('keypress', function () {
        if(this.value.length==2) return false;
    });

    function onlyNumberKey(evt) {
        // Only ASCII charactar in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }

    var userName="";
    function showUsersForBatch(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK"){
        var htmlStr="<hr><h4 class='pt-4 pb-3 d-flex align-items-center justify-content-between'>Users of this batch <button class='btn btn-primary' onclick='deleteUser()'>Delete Users</button></h4>\n" +
            "                    <table class='table table-bordered table-hover'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Batch Id</th>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Date Added </th>\n" +
            "                        </tr>\n" ;
            var users = data.batchUsers;
            for(i=0;i<users.length;i++){
                userName += "'"+users[i].username+"'"+","
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].batchId+"</td>"+
                    "<td>"+(users[i].username?users[i].username.split(siteId1+'_')[1]:"")+"</td>" +
                    "<td>"+users[i].dateAdded+"</td>" +
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "<span class='text-danger'>No records found</span>";
        }
        $("#batchUsers").show();
    }

    function  deleteUser(){
        var username=userName;
        username = username.substring(0, username.length-1);
        <g:remoteFunction controller="admin" action="deleteBatchUsers" params="'batchId='+batchId+'&username='+username" onSuccess = "usersDeleted(data);"/>
    }

    function  usersDeleted(data){
        if(data.status=="OK"){
            $('.loading-icon').addClass('hidden');
            alert("user deleted successfully");
        }
    }



</script>

</body>
</html>