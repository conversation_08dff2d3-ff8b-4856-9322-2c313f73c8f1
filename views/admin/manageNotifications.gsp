<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<style>
.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
div#loading-popup {
    opacity:1;
    z-index: 1000000;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
}

table.dataTable {
    width: 100% !important;
}
div#content-books .form-group.col-md-3 input#bookId {
    border:1px solid #EDEDED
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
%{--<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">--}%
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4">Test Notifications</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                    </div>
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <select name="notification" id="notification">
                                <option value="groupwall">Group Wall</option>
                                <option value="bookcreate">New Book Create</option>
                                <option value="silent">Silent Notifications</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6 mt-2">
                            <button type="button" id="search-btn" onclick="sendNotification()" class="btn btn-lg btn-primary col-3">Send</button>
                        </div>
                    </div>

                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                    </div>

                    <div id="codeList" class="pb-4" style="display: none;"></div>


                </div>
            </div>
        </div>
    </div>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="modal fade" id="loading-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title">Please wait while we generate the document.</p>
            </div>
        </div>
    </div>
</div>
<div class="push"></div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>

</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
%{--<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>--}%
%{--<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>--}%
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<style>

div.dataTables_wrapper div.dataTables_filter label
{
    width:100%;
    display:flex;
}
div#DataTables_Table_0_filter {
    text-align:left;
}
</style>
<script>

    $('#bookId').change(function () {
        $('#download-btn').attr('disabled','disabled');
    });
    $('#salesData').hide();
    $('#download-btn').attr('disabled','disabled');
    $('.loading-icon').addClass('hidden');
    function sendNotification() {
        $('#codeList').hide();
        $('.loading-icon').removeClass('hidden');
        document.getElementById("codeList").innerHTML='';
        var notification=$('#notification option:selected').val();
        <g:remoteFunction controller="admin" action="sendNotification" params="'notification='+notification" onSuccess = "notficationSent(data)"/>
    }
    function notficationSent(data){
        console.log(data)
        $('.loading-icon').addClass('hidden');
        if(data.status=="ok"){
            $('#codeList').show();


            document.getElementById("codeList").innerHTML= "<h6 class='mt-3'>Notification Sent!</h6>";
        }else{
            $('#codeList').show();


            document.getElementById("codeList").innerHTML= "<h6 class='mt-3'>Not able to send notification!</h6>";
        }
    }

</script>
</body>
</html>
