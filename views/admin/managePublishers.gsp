<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class='row'>
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group mpub">

                    <label for="publisherId" style="display: block;">Publisher</label>
                    <div class="dflex_pub">
                        <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name"
                                  name="publisherId" from="${publishers}" noSelection="['':'Select']"/>
                        <button class="btn btn-primary mr-2" onclick="updatePublisher()">Edit</button>
                        <%if(session.getAttribute("userdetails").publisherId==null){%>
                        <button class="btn btn-primary" onclick="manageDeliveryCharges()">Delivery Charges Manager</button>&nbsp;&nbsp;
                        <button class="btn btn-primary" onclick="addPublisher()">Add New</button>
                        <%}%>


                    </div>

                </div>
                <div class="form-group mpub">
                    <div class="dflex_pub">
                        <input type="text" class="form-control" name="userAdminEmail" id="userAdminEmail"  placeholder="Admin Email or mobile number"></input>
                        <button class="btn btn-primary" style="" onclick="addAdminUploader();">Add Publisher Admin</button><br>
                    </div>
                    <a href="javascript:showAllAdmins()" style="">Show publisher admin for this publisher</a>

                </div>
                <div class="form-group mpub">
                    <div class="dflex_pub">
                        <input type="text" class="form-control" name="userEmail" id="userEmail"  placeholder="Uploader Email or mobile number"></input>
                        <button class="btn btn-primary" style="" onclick="addUploader();">Add Uploader</button><br>
                    </div>
                    <a href="javascript:showAllUploaders()" style="">Show content uploaders for this publisher</a>

                </div>
                <div class="form-group mpub">
                    <div class="dflex_pub">
                        <input type="text" class="form-control" name="userEmail" id="svemail"  placeholder="Sales viewer Email or Mobile"></input>
                        <button class="btn btn-primary" style="" onclick="addSalesViewer();">Add Sales Viewer</button>
                    </div>
                    <a href="javascript:showAllSalesViewers()" style="display: inline-block;">Show sales viewers for this publisher</a>
                </div>
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                    <div class="form-group mpub">
                        <div class="dflex_pub">
                            <input type="text" class="form-control" name="mobile" id="mobile"  placeholder="Mobile No."></input>
                            <button class="btn btn-primary" style="" onclick="addWebChatAccess();">Add Web Chat Access</button>
                        </div>
                        <a href="javascript:showAllWebChatAccessUser()" style="display: inline-block;">Show all web chat Access User</a>
                    </div>
                </sec:ifAllGranted>



                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" class="mb-3 mx-2 text-success" style="display: none">Added Successfully!</div>
                <div id="batchUsers" style="display: none"></div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="bookId" style="display: block;">Add book</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="bookId"  placeholder="Book id"></input><br>
                <input type="text" class="form-control" name="emailId" id="emailId"  placeholder="User email id or mobile"></input><br>
                <input type="text" class="form-control" name="paymentId" id="paymentId"  placeholder="Payment Id"></input><br>
                <input type="text" class="form-control" name="discountId" id="discountId"  placeholder="Discount Id"></input><br>
                <% if(session["userdetails"].publisherId==null&&"1".equals(""+session["siteId"])) {%>
                <div class="col-md-3">
                    <g:select id="siteId" class="form-control w-100 m-0" optionKey="id" optionValue="clientName"
                              value="" name="siteId" from="${sitesList}" noSelection="['':'Select Site']"/>
                </div>
                <%  } else{%>
                <input type="hidden" name="siteId" id="siteId" value="null">
                <%}%>
                <select name="bookType" id="bookType" class="form-control w-100 m-0">
                    <option value="eBook">eBook</option>
                    <option value="testSeries">Online test series</option>
                    <option value="bookGPT">iBookGPT</option>
                </select>
                <button class="btn btn-primary" style="" onclick="addBook();">Add Book</button><br>

            </div>
        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    function addPublisher(){
        window.location.href = "/publisherManagement/addPublisher";
    }

    function manageDeliveryCharges(){
        if(document.getElementById("publisherId").value == ""){
            alert("Select Publisher")
        }else{
            window.location.href = "/deliveryCharges/manageDeliveryCharges?pubId="+document.getElementById("publisherId").value;
        }
    }
    function updatePublisher(){
        if(document.getElementById("publisherId").value == ""){
            alert("Select Publisher")
        }else{
            window.location.href = "/publisherManagement/addPublisher?pubId="+document.getElementById("publisherId").value;
        }
    }
    function addUploader(permissionType){
        var publisher = document.getElementById("publisherId");
        if(document.getElementById("userEmail").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email of the uploader."
            $("#errormsg").show();
        }
        else if(publisher.selectedIndex==0){
            alert("Please select the publisher");
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var userEmail = document.getElementById("userEmail").value;

            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="updateUserRole" params="'publisherId='+publisherId+'&user='+userEmail+'&permissionType=uploader'" onSuccess = "uploaderAdded(data);"/>



        }
    }

    function addAdminUploader(){
        var publisher = document.getElementById("publisherId");
        if(document.getElementById("userAdminEmail").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email of the admin."
            $("#errormsg").show();
        }
        else if(publisher.selectedIndex==0){
            alert("Please select the publisher");
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var userEmail = document.getElementById("userAdminEmail").value;

            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="updateUserRole" params="'publisherId='+publisherId+'&user='+userEmail+'&permissionType=admin'" onSuccess = "uploaderAdded(data);"/>



        }
    }

    function addBook(){

        if(document.getElementById("bookId").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the book id";
            $("#errormsg").show();
        }
        else if(document.getElementById("emailId").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email id of the user";
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var emailId = document.getElementById("emailId").value;
            var bookId = document.getElementById("bookId").value;
            var paymentId = document.getElementById("paymentId").value;
            var siteId = document.getElementById("siteId").value;
            var discountId = document.getElementById("discountId").value;
            var bookType=document.getElementById("bookType").value;
            <g:remoteFunction controller="admin" action="addBookToUser" params="'emailId='+emailId+'&bookId='+bookId+'&paymentId='+paymentId+'&discountId='+discountId+'&siteId='+siteId+'&bookType='+bookType" onSuccess = "uploadedBook(data);"/>

        }
    }
    function uploadedBook(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="error"){
            document.getElementById("errormsg").innerHTML="Error adding the book."
            $("#errormsg").show();
        }
        else{
            document.getElementById("emailId").value="";
            document.getElementById("bookId").value="";
            document.getElementById("paymentId").value="";
            $("#successmsg").show();
        }
    }
    function addSalesViewer(){
        var publisher = document.getElementById("publisherId");
        if(publisher.selectedIndex==0){
            document.getElementById("errormsg").innerHTML="You have to select the publisher";
            $("#errormsg").show();
        }
        else if(document.getElementById("svemail").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email of the Sales Viewer.";
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var userEmail = document.getElementById("svemail").value;
            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="updateSalesViewRole" params="'user='+userEmail+'&publisherId='+publisherId" onSuccess = "uploaderAdded(data);"/>



        }
    }

    function addWebChatAccess(){
        if(document.getElementById("mobile").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the Mobile No.";
            $("#errormsg").show();
            $("#batchUsers").hide();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var user =document.getElementById("mobile").value;
            <g:remoteFunction controller="admin" action="updateWebChatUserRole" params="'user='+user" onSuccess = "uploaderAdded(data);"/>
        }
    }



    function uploaderAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="error"){
            document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first."
            $("#errormsg").show();
        }
        else{
            document.getElementById("userEmail").value="";
            $("#errormsg").hide();
            $("#batchUsers").hide();
            $("#successmsg").show();
        }
    }

    function showAllUploaders(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        var publisher = document.getElementById("publisherId");
        if(publisher.selectedIndex==0){
            alert("Please select the publisher");

        }
        else{
            $('.loading-icon').removeClass('hidden');
            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="publisherUploaders" params="'publisherId='+publisherId+'&permissionType=uploader'" onSuccess = "showUploaders(data);"/>
        }
    }

    function showAllAdmins(){
        $("#errormsg").hide();
        $("#successmsg").hide();

        var publisher = document.getElementById("publisherId");
        if(publisher.selectedIndex==0){
            alert("Please select the publisher")
        }
        else{
            $('.loading-icon').removeClass('hidden');
            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="publisherUploaders" params="'publisherId='+publisherId+'&permissionType=admin'" onSuccess = "showUploaders(data);"/>
        }
    }

    function showAllWebChatAccessUser(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        var publisher = document.getElementById("publisherId")
        if(publisher.selectedIndex==0){
            <g:remoteFunction controller="admin" action="publisherWebChatAccessUsers"  onSuccess = "showAllWebChatUsers(data);"/>
        }else{
            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="publisherWebChatAccessUsers" params="'publisherId='+publisherId"  onSuccess = "showAllWebChatUsers(data);"/>
        }

    }


    function showAllSalesViewers(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        var publisher = document.getElementById("publisherId");

        if(publisher.selectedIndex==0){
            document.getElementById("errormsg").innerHTML="You have to select the publisher";
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            var publisherId = publisher.value;
            <g:remoteFunction controller="admin" action="publisherSalesViewers" params="'publisherId='+publisherId" onSuccess = "showSalesViewers(data);"/>
        }
    }

    function showSalesViewers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h4>Sales Viewers</h4>\n" +
            "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th class='text-center'>Delete</th>\n" +
            "                        </tr>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            console.log("no of users="+users.length);
            for(i=0;i<users.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].email+"</td>" +
                    "<td class='text-center'><a href='javascript:removeSalesViewAccess("+users[i].userId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No sales viewers added to this publisher yet";
        }
        $("#batchUsers").show();

    }

    function showAllWebChatUsers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h4>Web Chat Users</h4>\n" +
            "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th>Mobile</th>\n" +
            "                            <th class='text-center'>Delete</th>\n" +
            "                        </tr>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            for(i=0;i<users.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].email+"</td>" +
                    "<td>"+users[i].username+"</td>" +
                    "<td class='text-center'><a href='javascript:removeWebChatUserRole("+users[i].userId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No web chat users added to this publisher yet";
        }
        $("#batchUsers").show();

    }

    function showUploaders(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h4>Uploaders</h4>\n" +
            "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th class='text-center'>Delete</th>\n" +
            "                        </tr>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            console.log("no of users="+users.length);
            for(i=0;i<users.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].email+"</td>" +
                    "<td class='text-center'><a href='javascript:removePublisherAccess("+users[i].id+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No uploaders added to this publisher yet";
        }
        $("#batchUsers").show();

    }

    function removePublisherAccess(userId){
        if(confirm("Are you sure to remove publishing access to this user?")){
            <g:remoteFunction controller="admin" action="removePublisherRole" params="'userId='+userId" onSuccess = "publishAccessRemoved(data);"/>
        }
    }

    function removeSalesViewAccess(userId){
        if(confirm("Are you sure to remove sales viewing access to this user?")){
            <g:remoteFunction controller="admin" action="removeSalesViewRole" params="'userId='+userId" onSuccess = "publishAccessRemoved(data);"/>
        }
    }



    function removeWebChatUserRole(userId){
        if(confirm("Are you sure to remove web chat access to this user?")){
            <g:remoteFunction controller="admin" action="removeWebChatUserRole" params="'userId='+userId" onSuccess = "publishAccessRemoved(data);"/>
        }
    }






    function publishAccessRemoved(data){
        window.location.href = "/admin/managePublishers";
    }
</script>


</body>
</html>
