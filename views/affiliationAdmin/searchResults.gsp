<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Seller Information Admin</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div id="addInformation" class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <div class="form-row mb-4">

                        <div class="col-12 col-md-8" id="tagEditBox">
                             <div class="row">
                                <div class="col-md-12 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <table cellpadding="1" cellspacing="1" border="1">
                                            <tr><th width="30%">Search query</th>
                                            <th>Results found</th>
                                            <th>Site</th>
                                            <th>Username</th>
                                            <th>Search Date</th>
                                            </tr>
                                            <%for(int i=0;i<searchResults.size();i++){%>
                                            <tr>
                                            <td>${searchResults[i].search_string}</td>
                                            <td>${searchResults[i].results_found}</td>
                                            <td>${searchResults[i].username}</td>
                                            <td>${searchResults[i].site_name}</td>
                                            <td>${searchResults[i].date_created}</td>
                                            </tr>
                                            <%}%>
                                        </table>

                                    </div>
                                </div>






                            </div>
                        </div>
                        <div class="col-12 col-md-8" id="sellers">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
