<g:render template="/funlearn/navheader"></g:render>
    <!--Load the AJAX API-->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">

        google.charts.load('current', {'packages':['bar','gauge']});
        google.charts.setOnLoadCallback(drawChart);

        function drawChart() {
            var data = google.visualization.arrayToDataTable([
                ['Person', 'Topper', 'You'],
                ['2014', 10, 1]
            ]);

            var options = {
                chart: {
                    title: 'Test Performance',

                },
                bars: 'vertical',
                vAxis: {format: 'decimal'},
                height: 400,
                colors: ['#1b9e77', '#d95f02']
            };

            var chart = new google.charts.Bar(document.getElementById('chart_div'));

            chart.draw(data, google.charts.Bar.convertOptions(options));

            var data1 = google.visualization.arrayToDataTable([
                ['Label', 'Value'],
                ['Physics', 80],
                ['Chemistry', 55],
                ['Biology', 68]
            ]);

            var options1 = {
                width: 400, height: 120,
                redFrom: 0, redTo: 60,
                yellowFrom:61, yellowTo: 79,
                greenFrom:80,greenTo:100,
                minorTicks: 2
            };

            var chart1 = new google.visualization.Gauge(document.getElementById('chart_div1'));

            chart1.draw(data1, options1);


        }
    </script>

<!--Div that will hold the pie chart-->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-5 col-md-offset-2">
            <div id="chart_div"></div>
        </div>
        <div class="col-md-5">
            <div id="chart_div1"></div>
        </div>

    </div>
</div>

<g:render template="/funlearn/footer"></g:render>
<!--</div>-->




<asset:javascript src="bootstrap.min.js"/>
<script>
    function getProfilePage(){
        window.open("/creation/editProfile","_self");

    }
</script>
<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>





</body>
</html>