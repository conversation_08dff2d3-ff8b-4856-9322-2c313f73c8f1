<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/articles.css" async="true" media="all"/>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
<body>
<main class="blog_container">
    <article>
        <header><br>
            <h1>${title}</h1>
        </header>

        <div>


            <section id="bestEBooks">
                <h3>Books List</h3>
                <div id="bestEBooksText"></div>
            </section>

            <%if(wsAndPrepjoySite&&introduction!=null){%>
            <section id="introductionMain">
                <h3>About ${topLevelTitle}</h3>
                <div id="introductionMainText">${introduction}</div>
            </section>
            <%}%>
            <%if(wsAndPrepjoySite&&publishers!=null&&publishers.tagline!=null){%>
            <section id="bestBooks">
                <h3>About ${publishers.name}</h3>
                <div id="bestBooksText">${publishers.tagline}</div>
            </section>
            <%}%>
            <%if(wsAndPrepjoySite&&faq!=null){%>
            <section id="faqMain">
                <h3>Frequently asked questions</h3>
                <div id="faqMainText">${faq}</div>
            </section>
            <%}%>


        </div>
    </article>
</main>
<g:render template="/${session['entryController']}/footer_new"></g:render>

</body>
<script>

    <%if(wsAndPrepjoySite&&introduction!=null){%>
    document.getElementById("introductionMainText").innerHTML=document.getElementById("introductionMainText").innerText;
    <%}%>
    <%if(wsAndPrepjoySite&&faq!=null){%>
    document.getElementById("faqMainText").innerHTML=document.getElementById("faqMainText").innerText;
    <%}%>

    <%if(wsAndPrepjoySite&&publishers!=null&&publishers.siteDescription!=null){%>
    document.getElementById("bestBooksText").innerHTML=document.getElementById("bestBooksText").innerText;
    <%}%>

    var data = { "books":"${booksList.get("books")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publishers":"${booksList.get("publishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&')
    };

    var booksListTable = "<table cellpadding='5' cellspacing='5' class='table table-striped table-hover'>" +
        "<tr><th></th><th>Book Title</th>" +
        "<th>Subject</th>";
   <%if(wsAndPrepjoySite){%> booksListTable += "<th>Publisher</th>" ;<%}%>
    booksListTable +=   "<th></th></tr>";

    var books = JSON.parse(data.books);

    var noOfBooks = books.length;
    var imgSrc;
    var siteName;

    <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
    siteName="${session['siteName']}";
    <%}else{%>
    siteName="${session['entryController']}";
    <% }%>
    for(var i=0; i<noOfBooks;i++){
        imgSrc = books[i].coverImage;
        if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
            imgSrc = books[i].coverImage;
            imgSrc = imgSrc.replace("~", ":");
        } else {
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=thumbnail";
        }
        booksListTable +="<tr><td>" +"<img src='" + imgSrc + "' alt='Book Cover Image' width='50'/></td>"+
            "<td>"+books[i].title+"</td>"+
            "<td>"+books[i].subject+"</td>"
        <%if(wsAndPrepjoySite){%>  booksListTable +=      "<td>"+books[i].publisher+"</td>"<%}%>
        booksListTable +=   "<td>"
                    +"<a href='/" + replaceAll(books[i].title,' ','-')+"/free-pdf-download/" + books[i].id + "?siteName="+siteName+"' target='_blank'>Free PDF</a> | "
                    +"<a href='/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true' target='_blank'>Buy Now</a>" +
            "</td>"+

            "</tr>"
    }
    booksListTable +="</table>"
    document.getElementById("bestEBooksText").innerHTML = booksListTable;

</script>
</html>
