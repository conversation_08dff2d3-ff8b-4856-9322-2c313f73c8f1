<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
    <style>
    .card-icon {
        font-size: 3rem;
    }
    .card {
        margin: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: 0.3s;
        height: 250px; /* Ensure uniform box sizes */
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .card:hover {
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
    }
    .card-title {
        color: #007bff;
    }
    .card-text {
        color: #6c757d;
    }
    .btn-remove {
        background-color: #dc3545;
        color: white;
    }
    </style>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
</head>
<body>

<div class="container">
    <h1 class="text-center my-4">Your Bookmarks</h1>
    <div class="row">
        <g:each in="${bookmarks}" var="bookmark" status="index">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="${bookmark.resource.icon} card-icon" style="color: ${['#007bff', '#dc3545', '#28a745', '#ffc107', '#fd7e14', '#6f42c1', '#17a2b8', '#e83e8c', '#343a40', '#20c997'][index % 10]};"></i>
                        <h5 class="card-title" style="color: #007bff;">${bookmark.resource.name}</h5>
                        <p class="card-text"><a href="${bookmark.resource.link}" target="_blank" style="color: #17a2b8;">View</a></p>
                        <a href="${createLink(controller: 'bookmark', action: 'deleteBookmark', id: bookmark.id)}" class="btn btn-remove">Remove</a>
                    </div>
                </div>
            </div>
        </g:each>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
