<ul class="navbar-nav mr-auto d-none d-md-flex">
    <li class="nav-item active">
        <a class="nav-link" href="/store?mode=browse">Store</a>
    </li>
    <sec:ifNotLoggedIn>
        <li class="nav-item">
            <a class="nav-link" onclick="loginOpen()">My Library</a>
        </li>
    </sec:ifNotLoggedIn>
    <sec:ifLoggedIn>
        <li class="nav-item">
            <a class="nav-link" href="/library">My Library</a>
        </li>
        <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
            <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                    Publishing
                </a>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                    <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tabs</a>
                    <a class="dropdown-item" href="/wonderpublish/manageExams">Manage Exams</a>
                </div>
            </li>
        </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
            <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">
                    Digital Marketing
                </a>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="/header/index">Header Management</a>
                    <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">Print books</a>
                </div>
            </li>
        </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_FINANCE">
            <li class="nav-item">
                <a href="/publishing-sales" class="nav-link">Sales</a>
            </li>
        </sec:ifAllGranted>
        <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN">

            <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                    Admin
                </a>
                <div class="dropdown-menu">
                    <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_DELETE_USER">
                        <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                        <a class="dropdown-item" href="/institute/admin">Institute Management</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_PUBLISHER">
                        <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                        <a class="dropdown-item" href="/log/notification">Notification</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                        <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                        <a class="dropdown-item" href="/log/migrateuser">Migrate User</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                        <a class="dropdown-item" href="/log/userAccess">User Access</a>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_APP_ADMIN">
                        <a class="dropdown-item" href="/log/appVersionManagement">App Version Management</a>
                    </sec:ifAllGranted>
                </div>
            </li>
        </sec:ifAnyGranted>

    </sec:ifLoggedIn>
%{--            <li class="nav-item">--}%
%{--                <a class="nav-link" href="#ws-ebooks">Why WS eBooks?</a>--}%
%{--            </li>--}%
</ul>