<g:render template="/funlearn/pnavheader"></g:render>
<g:render template="/funlearn/topicinclude"></g:render>
<asset:javascript src="soundmanager2.js"/>
<asset:javascript src="bar-ui.js"/>
<asset:stylesheet src="bar-ui.css"/>


<asset:stylesheet href="flipbook.style.css"/>
<asset:stylesheet href="font-awesome.css"/>
<div class="container-fluid wplandingblueimage topnavblue">
    <div class="row">
        <div class="col-md-2">
            <a href="javascript:history.back()"><h4 class="whitetext">< BACK</h4></a>
        </div>
        <div class="col-md-8 text-center">
            <h4 class="darkgrey robotoslab">${raw(displayName)}</h4>
        </div>

    </div>
</div>
<div class="wrapper">
    <div class="container-fluid">

        <div  class='row'>
            <div class="col-md-2 wpsidebar" id="previous">
                <div class="row">
                    <div class="col-md-12 greytext"><br>
                        <b>Chapters</b>
                    </div>
                </div>
                <hr>
            <g:each in="${topicMst}" var="chapter" status="i">
                <div class="row">
                    <div class="col-md-12">
                      <a href="javascript:getChapterDetails('${chapter.id}','${chapter.name}')" class="greytext" id="chapter${chapter.id}">  ${i+1}. ${chapter.name}</a>
                    </div>
                </div>
                <hr>
                </g:each>
            </div>
            <g:render template="/wonderpublish/displayChapterDetails"></g:render>
            <g:render template="/wonderpublish/bookanalytics"></g:render>
<br>

            <div class="col-md-1 "style="margin-top:10px">

                <div class="wpboxifyquiz">
                    <div class="row">
                        <div class='col-md-12 '><br>&nbsp;<a href="javascript:testgen()" class="dark0text smallText"><b><i class="fa fa-gears fa-x"></i>&nbsp; CREATE NEW TEST</b></a></div>

                    </div>
                    <hr>
                    <div class="row">
                        <div class='col-md-12'>&nbsp;<a href="javascript:getBookAnalytics()" class="light1text smallText"><b><i class="fa fa-bar-chart fa-x"></i>&nbsp; CHECK MY PROGRESS</b></a><br></div>
                    </div>
                    <hr>
                  <!--  <div class="row">
                        <div class='col-md-12'>&nbsp;<a href="javascript:bookanalytics()" class="light1text smallText"><b><i class="fa fa-envelope-o fa-x"></i>&nbsp; WRITE TO PUBLISHER</b></a><br></div>
                    </div>
                    <hr> -->
                    <div class="row">
                        <div class='col-md-12'>&nbsp;<a href="javascript:reviewrating()" class="light9text smallText"><b><i class="fa fa-edit fa-x"></i>&nbsp; WRITE A REVIEW</b></a><br></div>
                    </div>
                    <hr>
                <!--    <div class="row">
                        <div class='col-md-12'>&nbsp;<span class="dark5text smallText"><b><i class="fa fa-gift fa-x"></i>&nbsp; GIFT TO A FRIEND</span><br></div>
                    </div>
                    <hr> -->
                </div>
                <br>
                <div class="row text-center">
                    <div class="col-md-12"><h4 class="light10text">Recommendations</h4> </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class='wpboxifychapter'>
                            <a href='bookdtl'><div class='wpboxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book6.jpg&type=books&imgType=passport' width='170' height="220"></div></a>
                            <p class="robotoslab greytext"><span class="orange"> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i></span>(2)&nbsp;&nbsp;<b>&#8377; 225</b></p>
                        </div>
                    </div>
                </div><br>
                <div class="row">
                    <div class="col-md-12">
                        <div class='wpboxifychapter'>
                            <a href='bookdtl'><div class='wpboxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book5.jpg&type=books&imgType=passport' width='170' height="220"></div></a>

                            <p class="robotoslab greytext"><span class="orange"> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-o fa-x'></i></span>(8)&nbsp;&nbsp;<b>&#8377; 320</b></p>
                        </div>
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-md-12">
                        <div class='wpboxifychapter'>
                            <a href='bookdtl'><div class='wpboxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book7.jpg&type=books&imgType=passport'  width='170' height="220"></div></a>
                            <p class="robotoslab greytext"><span class="orange"> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i></span>(14)&nbsp;&nbsp;<b>&#8377; 109</b></p>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <br>
            <br>

        </div>


    </div>
    <br>
    <div class="push"></div>
</div>
<div id="lightbook">

</div>
<g:render template="/books/bfooter"></g:render>
<!--</div>-->
<script>
    var pageType='book';
</script>
<asset:javascript src="pageflip/three.js"/>
<asset:javascript src="pageflip/flipbook.webgl.js"/>
<asset:javascript src="pageflip/pdf.js"/>
<asset:javascript src="pageflip/compatibility.js"/>
<asset:javascript src="pageflip/pdf.worker.js"/>
<asset:javascript src="pageflip/jquery.touchSwipe.js"/>
<asset:javascript src="pageflip/share.js"/>
<asset:javascript src="pageflip/flipbook.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>

<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>



<g:render template="/funlearn/topicscripts"></g:render>
<g:render template="/testgenerator/testgen"></g:render>
<g:render template="/wonderpublish/reviewrating"></g:render>
<g:render template="/wonderpublish/bookanalytics"></g:render>
<script>
    var previousChapterId=${topicId};
    getTopicDetails('${topicId}','chapter');



    function getChapterDetails(chapterId,chapterName){

        $('#chapatertabs a:first').tab('show');
        $("#displaybookanalytics").hide(500);
        $("#displaychapter").show(500);

       document.getElementById("content-data-weblinks").innerHTML="";
        $("#content-weblinks").hide();
        $("#chapter"+previousChapterId).addClass('greytext');
        previousChapterId=chapterId;

        document.getElementById("content-data-quiz").innerHTML="";
        document.getElementById("content-data-notes").innerHTML="";
        document.getElementById("content-data-relvideos").innerHTML="";
        document.getElementById("content-data-weblinks").innerHTML="";
        getTopicDetails(chapterId,'chapter');
        var chapterSelect = document.getElementById("addlevel");
        for(var i, j = 0; i = chapterSelect.options[j]; j++) {
            if(i.value == chapterId) {
                chapterSelect.selectedIndex = j;
                break;
            }
        }


    }

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href") // activated tab
        if(target=="#dforum"){
            getDFQuestions();
        }
    });
</script>


</body>
</html>