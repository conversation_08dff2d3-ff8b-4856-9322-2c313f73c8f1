<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<style>
    .footer-menus, .mobile-footer-nav {
        display: none;
    }
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 ebook_detail direct-sales pt-4 pt-md-5">
    <% if(instituteId != ""){%>
    <div class="container col-lg-10 px-4 px-md-5">
        <div class="institute-info d-flex justify-content-start align-items-end">
            <div class="institute-logo pr-3">
                <img class="border rounded shadow-sm" src="/institute/instituteImage?instituteId=${instituteId}">
            </div>
            <div class="institute-name">
                <h4>${instituteName}</h4>
            </div>
        </div>
        <hr class="w-100 pb-3">
    </div>
    <%}%>
    <div class="container d-flex flex-wrap align-items-center pt-4 pb-5 py-md-5">
        <div class="direct-sale-book-info col-md-8 mx-auto p-4 pt-5 p-md-5 mb-5">
            <h3 class="mb-3 text-center">${booksMst.title}</h3>
            <div class="book_info p-2 p-md-3 shadow">
                <div class="book_description d-block d-md-flex justify-content-center">
                    <div class="book_price d-flex justify-content-center align-items-center">
                        <% if(!("0".equals(""+booksMst.price)||"0.0".equals(""+booksMst.price))) {%>
                        <span class="offer_price pr-1">&#x20b9<span class="offp">${booksMst.price}</span></span>
                        <%}%>
                        <% if(booksMst.listprice != booksMst.price) {%>
                        <span class="list_price pl-1"><del>&#x20b9<span>${booksMst.listprice}</span></del></span>
                        <% } %>
                    </div>
                    <div class="d-flex align-items-center justify-content-start pl-3">
                        <button onclick="openRazorPay(${booksMst.price},'${booksMst.title}',${booksMst.id},'book','')" class="btn btn-lg pay-btn">Pay</button>
                    </div>
                </div>
            </div>

            <div id="packageBooksList" class="package-books"></div>
        </div>
    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/wonderpublish/buyOrAdd"></g:render>
<script>
    function getPackageBooks(){
            var bookId = "${booksMst.id}";
            <g:remoteFunction controller="wsshop" action="getPackageBooksDetail" params="'bookId='+bookId" onSuccess = "displayBooks(data);"/>
            $('.loading-icon').removeClass('hidden');
    }

    function displayBooks(data){

        var books = JSON.parse(data.books);

        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="<h5 class='text-center'>Package contains: <strong>"+books.length+" eBooks</strong></h5><br><div class='d-block d-md-flex flex-wrap'>";
        var imgSrc = "";
        for(var i=0; i<books.length && i<=9; i++){
            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=webp";

            }
            ebookHtmlStr += "<div class='col-12 col-md-6 fadein-animated mb-4'>" ;


            ebookHtmlStr +=    "<div class='img-wrapper shadow'>";

            ebookHtmlStr += "<div class='bookShadow'>";

            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                ebookHtmlStr +="<div class='uncover'>"+
                    "<p>"+books[i].title+"</p>"+
                    "</div>";
            }
            else {
                ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
            }
            ebookHtmlStr +="</div>";

            ebookHtmlStr += "</div>" +
                "</div>";
            noOfEbooks++;

        }

        ebookHtmlStr += "</div>";
        document.getElementById('packageBooksList').innerHTML=ebookHtmlStr;

        $('.loading-icon').addClass('hidden');

    }

    getPackageBooks();
</script>
