<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>

<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    <meta name="keywords" content="${keywords}">
    <meta name="description" content="Wonderslate is an effort to bring syllabus specific educational content to all.Engaging parents, teachers and students to create and share content. Be it mind maps, videos , quiz, solved question paper etc. CBSE,ICSE,state boards etc.">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />

    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto Slab:300,400,700' rel='stylesheet' type='text/css'>

    <asset:stylesheet href="hopscotch-0.1.1.css"/>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="flat-ui.css"/>
    <asset:stylesheet href="demo.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>
    <ckeditor:resources />
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));

%>

<style>
.modal.modal-wide .modal-dialog {
    width: 90%;

}
.show-on-hover:hover > div.dropdown-menu {
    display: block;
    width: 500px !important;
}
.wrapper {
    min-height: 70%;
    height: auto !important;
    height: 100%;
    margin: 0 auto -3em;
}
.footer, .push {
    height: 3em;
}
</style>
<body>

<nav class="navbar navbar-default navbar-fixed-top" style="background-color: white">
    <div class="container-fluid navbarfirstline">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <sec:ifNotLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}" id="addingcontent2">WONDERPUBLISH</a></span>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <span class="brand">&nbsp;&nbsp;&nbsp;&nbsp;<a style="padding-top:5px;" class="navbar-brand" href="/books/index"><img alt="brand" src="${assetPath(src: 'vtu.jpeg')}">Visvesvaraya Technological University</a></span>



            </sec:ifLoggedIn>

        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <div class="row row-right">
                <ul class="nav navbar-nav navbar-right top-nav">
                    <sec:ifNotLoggedIn>
                        <li><a href="javascript:showregister('login');">&nbsp;Sign in&nbsp;</a></li>
                        <li><a href="javascript:showregister('signup');">&nbsp;Register&nbsp;</a></li>
                    </sec:ifNotLoggedIn>
                    <sec:ifLoggedIn>

                        <li><a href="/books/books">My Library</a></li>
                        <li id="notification" class="dropdown"><a href="#"><i class="fa fa-bell-o fa-x"></i></a></li>
                        <li><g:link uri="/logoff">&nbsp;Logout&nbsp;&nbsp;&nbsp;&nbsp;</g:link></li>
                    </sec:ifLoggedIn>
                </ul>

            </div>
        </div>
    </div>
    <div class="popoverhome" style="display:none"></div>

    <!--<a href='#' id='example2' rel='popover' data-placement='left' data-content='Discover your subject by choosing these options' data-original-title='Find your subject'></a>-->


</nav>
<body>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>

<!--<div>-->
<div >
<div class="container-fluid" >
    <div class="row wpbluebackground"><br><br>
        <div class="col-md-2 col-md-offset-5"><h3 class="whitetext">MY LIBRARY</h3></div>
        </div>



    <br>
</div>
<div class="container-fluid" >
    <div  class='row maincontent' id="bookdtl">
        <div class="col-md-2 sidebar"><br>
            <div class="hidden-xs">
                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadprofile" id="uploadprofile" action="/creation/uploadprofile" method="post">
                    <input type="hidden" name="source" value="home">
                    <input type="hidden" name="type" value="user">
                    <input type="hidden" name="sourceController" value="funlearn">
                    <div class="row"><div class="col-md-12 text-center">
                        <%if(session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-circle" height="150">
                        <%}else{%> <a href="#" ><i class="fa fa-user fa-5x"></i></a><%}%>

                    </div></div>
                    <a href='#' id='popover1' rel='popover1' data-content='Add your profile picture here.'></a>
                </form>
                <br>

                <div></div>
                <div class="row"><div class="col-md-10 col-md-offset-2"><a href="/funlearn/profile?id=${session['userdetails'].id}">${session['userdetails'].name}</a></div></div>
                <div class="row"><div class="col-md-10 col-md-offset-2 "><b>Branch</b>&nbsp;&nbsp;Computer Science</div></div>
                <div class="row"><div class="col-md-10 col-md-offset-2 "><b>Semester</b>&nbsp;&nbsp;4th</div></div>
                <div class="row"><div class="col-md-10 col-md-offset-2 "><b>Reg No</b>&nbsp;&nbsp;AP97890453</div></div>

                <hr>
                <a href="/creation/editProfile" class="darkgrey"><div class="row"><div class="col-md-8 col-md-offset-2 "><i class="fa fa-edit fa-x"></i>&nbsp;&nbsp; Edit Profile</div>
                <div class="col-md-2"><i class="fa fa-angle-right fa-x darkgrey"></i> </div> </div></a>
                <hr>
                <a href="/books/vtuanalytics" class="darkgrey"> <div class="row"><div class="col-md-8 col-md-offset-2 "><i class="fa fa-bar-chart fa-x"></i>&nbsp;&nbsp; Insights</div>
                    <div class="col-md-2"><i class="fa fa-angle-right fa-x darkgrey"></i> </div> </div></a>
                <hr>
                <a href="/creation/editProfile" class="darkgrey"> <div class="row"><div class="col-md-8 col-md-offset-2 "><i class="fa fa-heart-o fa-x"></i>&nbsp;&nbsp; Favorites</div>
                    <div class="col-md-2"><i class="fa fa-angle-right fa-x darkgrey"></i> </div> </div></a>
                <hr>
                <a href="/books/vtuadmin" class="darkgrey"> <div class="row"><div class="col-md-8 col-md-offset-2 "><i class="fa fa-user fa-x"></i>&nbsp;&nbsp; Admin</div>
                    <div class="col-md-2"><i class="fa fa-angle-right fa-x darkgrey"></i> </div> </div></a>
                <hr>
            </div>

        </div>

        <div class='col-md-9 main'>
            <div id="content-books">
                <div class="row" style="background-color: white">
                    <div class="col-md-5 col-md-offset-7 boxifyexplore">
                        <div class="row"><a href="javascript:changeSem()">
                            <div class="col-md-2 text-right"><i class="fa fa-graduation-cap fa-2x"></i></div>
                            <div class="col-md-8 bigtext text-left">Semester 4</div>
                            <div class="col-md-2"> <i class="fa fa-angle-down fa-2x"></i></div> </a>
                        </div>
                        <div id="semchange" style="display: none;">
                            <div class="row">
                                <div class="col-md-12 text-center"></div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row"><br>
                    <div class="col-md-10 col-md-offset-1">
                        <ul class="nav nav-tabs lefttitle">
                            <li class="active"><a data-toggle="tab" href="#subject"><i class="fa fa-tasks fa-x>"></i>&nbsp;&nbsp;BY SUBJECTS</a></li>
                            <!--  <li ><a data-toggle="tab" href="#chapters">CHAPTERS</a></li> -->
                            <li><a data-toggle="tab" href="#year"><i class="fa fa-calendar fa-x>"></i>&nbsp;&nbsp; BY EXAM YEAR</a></li>

                        </ul>
                        <div class="tab-content">
                            <div id="subject" class="tab-pane fade in active">


                            </div>
                            <!--    <div id="chapters" class="tab-pane fade">
                            chapters
                            </div> -->
                            <div id="year" class="tab-pane fade">




                            </div>

                        </div>
                    </div>
                </div><BR>

                <br><br>



                <br>
            </div>



        </div>

    </div>

</div>
    <div class="push"></div>
</div>



<g:render template="/funlearn/wpfooter"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>

    function changeSem(){

        var htmlStr = "<div class='row'><div class='col-md-12 text-center'><span class='numberCircle'>1</span><span class='numberCircle'>2</span><span class='numberCircle'>3</span>" +
                "<span class='numberCircle selected'><a href=''>4</a></span></div></div> ";

        document.getElementById("semchange").innerHTML = htmlStr;
        $('#semchange').toggle(1000);
    }
    function getBooksList(getBooksList){

        <g:remoteFunction controller="books" action="getBooksList"  onSuccess='mainDisplayBooks(data);'
                params="'status=demovtu'" />
    }

    function mainDisplayBooks(data){
        displayBooks(data,'subject');
        displayBooks(data,'year');
    }
    function displayBooks(data,booksType){
        var books = data.books;
        var randomColor,randomViews,randomHours,randomMinutes;
        var htmlStr="<br><br> <div class='row'>";
        var rowclosed=false;
        var columnCount=0;
        for(var i = 0; i <books.length; ++i){
            if(booksType==books[i].subjectyear) {

                randomColor = columnCount % 10;
                randomViews = Math.floor(Math.random() * 32) + 1;
                randomHours = Math.floor(Math.random() * 50) + 1;
                randomMinutes = Math.floor(Math.random() * 59) + 1;
                columnCount++;
                htmlStr += " <div class='col-md-3 text-center'>" +
                        "                                       <a href='/books/vtubook?bookId="+books[i].id+"'> <div class='boxifynoborder dark" + randomColor + " whitetext'>" +
                        "                                            <div class='boxifytop light" + randomColor + " wpbooktitle'><br><br><b>" + books[i].title + "</b></div>" +
                        "                                        <br><div class='row smallText'><div class='col-md-11 col-md-offset-1 text-left'><i class='fa fa-eye fa-x'></i>&nbsp;" + randomViews + "&nbsp;&nbsp;" +
                        " <i class='fa fa-clock-o fa-x'></i>&nbsp;" + randomHours + "h&nbsp;" + randomMinutes + "m </div></div></div>" +
                        "                                  </a> </div>";
                // logic to close the row and create a new one
                if ((columnCount) % 4 == 0 && i < books.length) htmlStr += "</div><br><br> <div class='row'>";
            }

        }
        htmlStr+="</div>";
        document.getElementById(booksType).innerHTML=htmlStr;
        //$("#content-books").css("display","block");
    }

  getBooksList();
    //getTopicsMap('topic');
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>


</body>
</html>