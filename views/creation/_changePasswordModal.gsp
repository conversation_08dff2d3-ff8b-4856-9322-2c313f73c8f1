


<div class="modal fade" id="change-password-modal">
  <div class="modal-dialog modal-dialog-centered d-flex justify-content-center">
    <div class="modal-content" data-content="form">
      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="login">Change Password</h4>
      </div>
      <!-- Modal body -->
      <div class="modal-body text-center mx-auto">
        <div class="password-content" id="password-change-form">
          <g:form method="post">
%{--            <span class="input-login">--}%
%{--              <input class="input-field input-field-login password-reset-input" type="password" name="oldPassword" id="old-password">--}%
%{--              <label class="input-label input-label-login password-reset-input input-label-login-color-1" for="old-password">--}%
%{--                <span class="input-label-content input-label-content-login">Old Password</span>--}%
%{--              </label>--}%
%{--            </span>--}%
            <span class="input-login">
              <input class="input-field input-field-login password-reset-input" type="password" id="newpassword" name="password">
              <label class="input-label input-label-login password-reset-input input-label-login-color-1" for="newpassword">
                <span class="input-label-content input-label-content-login">New Password</span>
              </label>
            </span>
            <span class="input-login">
              <input class="input-field input-field-login password-reset-input" type="password" id="cnf-password">
              <label class="input-label input-label-login password-reset-input input-label-login-color-1" for="cnf-password">
                <span class="input-label-content input-label-content-login">Confirm Password</span>
              </label>
            </span>
            <div class="d-flex justify-content-end">
              <button type="button" class="btn" data-dismiss="modal" onclick="javascript:resetPasswordData();">Cancel</button>
              <button type="button" onclick="javascript:changeSubmitAction();" class="btn">Submit</button>
            </div>
          </g:form>
          <div class="input-error-tooltip password-error-tooltip" style="padding: 0; display: none;">
            <div class="input-error-tooltip-inner password-match-error">Password does not match the new password</div>
          </div>
        </div>
        <div class="password-success">
            <div>
                <p class="text-success mb-0">Password changed successfully!</p>
            </div>
        </div>
    </div>
    </div>
    </div>
</div>

<script>
function updateImage() {
$('#fileoption1').trigger('click');
}
function formSubmit() {
var flds = new Array ('name','syllabus');
if (genericValidate(flds)) {
document.userProfile.submit();
}
}
function getFile(filePath) {
  return filePath.substr(filePath.lastIndexOf('\\') + 1).split('.')[0];

}

function getoutput() {

}
function updateProfile(){
  var oFile = document.getElementById("fileoption1").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

  if(oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1){
    alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
    return
  }

  if (oFile.size > 2097152) // 2 mb for bytes.
  {
    $('#file-error').css('color','red');
    return;
  }
  else {
   // var filename=getFile(fileoption1.value);
   // filename +='test';
   // oFile.value=filename;
    document.uploadProfileImage.submit();
  }
}
function changeSubmitAction() {
submitPasswordChange();
}
function submitPasswordChange() {
var password = $("#newpassword").val();
  var oldPassword  ="<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
// alert($('#newpassword').val());
//   alert($('#cnf-password').val());
if($('#newpassword').val() != $('#cnf-password').val()) {
$('#cnf-password').addClass('has-error');
$('.password-error-tooltip').show().addClass('new-password-error').removeClass('empty-input-error');
$('.password-match-error').html("Password does not match the new password");
$('.password-match-error').css({'display': 'block'});
// } else if($('.password-reset-input').val() == "") {
// $('.password-error-tooltip').show().addClass('empty-input-error');
// $('.password-match-error').html("Input fields can't be empty");
// $('#cnf-password').addClass('has-error');
 } else {
$('.password-match-error').css({'display': 'none'});
$('.password-error-tooltip').hide();
  <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='profileUpdateSuccess(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
}
}

function profileUpdateSuccess(data) {
  if(data.status=='failed'){
    alert(data.message)
  }
if(data.status=="success") {
    $('#change-password-modal').modal('show');
    $('#change-password-modal .modal-header .login').hide();
    $('#change-password-modal .modal-content ').attr('data-content', 'success');
    $('.password-success').css({'display' : 'block'});
setTimeout(function(){
$('#change-password-modal').modal('hide');
},1000);
$('.password-reset-input').val("");
setTimeout(function(){
$('#password-change-form').css({
'display' : 'block'
});
$('.password-success').css({
'display' : 'none'
});
$('.password-reset-input').val("");
},1500);
}
else {
$('.password-error-tooltip').show().addClass('empty-input-error');
$('.password-match-error').html("Old password did not match");
$('#cnf-password').addClass('has-error').removeClass('is-correct');
}
}
// Reset form data
function resetPasswordData() {
  $('.password-reset-input').val("");
  $('.password-success').css({'display' : 'none'});
  $('.password-match-error').css({'display' : 'none'});
}
</script>
<%if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% }%>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.1.1/jquery.min.js"></script>
