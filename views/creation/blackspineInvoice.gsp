<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Welcome</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700,900" rel="stylesheet">
</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())

%>
<body>
<div style="max-width: 800px;padding:1rem;margin:0 auto;border:2px solid #000;font-family: 'Roboto', sans-serif; font-size: 14px; padding-bottom: 2rem">
    <!--header starts-->
    <table cellpadding="20" style="width: 100%;">
        <tr>
            <td>
                <h1 style="text-align: center; font-weight: 900;font-size: 18px;margin:0;padding-bottom:10px;padding-top: 10px;">TAX INVOICE</h1>
            </td>
        </tr>
    </table>
    <!--header finishes-->

    <!--Address section-->
    <table style="width: 100%;margin-top: 1rem;">
        <tr>
            <td style="width: 70%;">
                <p style="font-family: 'Roboto', sans-serif;font-weight: 500;margin-top: 0;margin-bottom: 10px; font-size: 20px;line-height: 20px;">Blackspine Publishing Private Limited</p>

                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;"> B-143/1, Ground Floor, </p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;"> Sector-6, Noida - 201301.</p>
                %{--<p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">[CIN : U72900RJ2018PTC063026]</p>--}%
                %{--<p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">GST No. : 08AAFCE2658C1ZT</p>--}%
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">Helpline Number : +91 98102-28588, +91 120-242-1010</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">E-mail : <EMAIL></p>
                %{--<p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">Website : www.utkarsh.com </p>--}%
            </td>
            <td style="width: 30%;text-align: right;">
                <img src="${serverURL}/assets/landingpageImages/blackspine_logo.png" width="100" height="100" style="width: 100px;height: 100px;float: right;margin-right: 1rem;"/>
            </td>
        </tr>
    </table>
    <%
        Float cgst = 0
        Float sgst = 0
        Float igst = 0

        //Total Amount Is 50
        //Taxable Amount= 100/118*50(Book’s Total Price)= 42.37
        //Cgst= 9% Of Taxable Amount If Customer From Madhya Pradesh
        //Sgst= 9% Of Taxable Amount If Customer From Madhya Pradesh
        //Igst= 18% Of Taxable Amount If Customer From Outside Madhya Pradesh
        Float tamount = (Float)((new Float(amount))*(100/118)).trunc(2)

        if(state!=null && state.toLowerCase().trim().equals("Madhya Pradesh")) {
            cgst = (Float)((9*tamount)/100)
            sgst = (Float)((9*tamount)/100)
        } else {
            igst = (Float)((18*tamount)/100)
        }
    %>
    <div style="margin-top:2rem;border-top: 1px solid #000; padding-top: 1rem;">
        <table cellpadding="10" style="margin-top:5px;float: right;">
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;">Date :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${createdAt}</span>
                </td>
            </tr>
        </table>
        <table cellpadding="10" style="margin-top:5px;">
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Invoice No. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${poNo}</span>
                </td>
            </tr>
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Name :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${name}</span>
                </td>
            </tr>
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Contact No. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${mobile}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> E-mail :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${account}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Address :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${state}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Payment Mode :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${method}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Customer IP :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${ipAddress}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;">Bank Ref. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;margin-right: 4px;line-height: 16px;">${paymentId}</span>
                </td>
            </tr>
        </table>
    </div>

    <div style="">
        <table cellpadding="10" style="border-collapse: collapse; width: 100%;margin-top: 4px;">
            <tr>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse; line-height: 16px;">Qty.</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Description</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Gross Amount</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Discount</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Taxable Amount</th>
                <%if(state!=null && state.toLowerCase().trim().equals("Madhya Pradesh")){%>
                <th colspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Tax</th>
                <%}else{%>
                <th colspan="1" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Tax</th>
                <%}%>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Total Amount</th>
            </tr>
            <%if(state!=null && state.toLowerCase().trim().equals("Madhya Pradesh")){%>
            <tr>
                <th style="font-family: 'Roboto', sans-serif;border:1px solid #000;border-collapse: collapse; line-height: 16px;">CGST 9%</th>
                <th style="font-family: 'Roboto', sans-serif;border:1px solid #000;border-collapse: collapse;line-height: 16px;">SGST 9%</th>
            </tr>
            <%}else{%>
            <tr>
                <th style="font-family: 'Roboto', sans-serif;border:1px solid #000;border-collapse: collapse;line-height: 16px;">IGST 18%</th>
            </tr>
            <%}%>

            <%if(state!=null && state.toLowerCase().trim().equals("Madhya Pradesh")){%>
            <tr>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">1</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse;line-height: 16px;">${title}</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0.00</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse;line-height: 16px;text-align: center;"><g:formatNumber number="${cgst}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${sgst}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">${amount}</td>
            </tr>
            <%}else{%>
            <tr>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">1</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse;line-height: 16px;">${title}</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0.00</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${igst}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">${amount}</td>
            </tr>
            <%}%>
            <%if(state!=null && state.toLowerCase().trim().equals("Madhya Pradesh")){%>
            <tr style="border:none;">
                <td colspan="4" style="font-family: 'Roboto', sans-serif;text-align: center;font-size: 16px;color:red;font-weight: 900;"><div style="display: flex;justify-content: center;align-items: center;padding: 0.5rem;">IN WORDS:<span style="text-transform: uppercase;font-weight: 500;">&nbsp;${amtStr}</span></div></td>
                <td  colspan="3" style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">Grand Total(INR)</td>
                <td  style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">${amount}</td>
            </tr>
            <%}else{%>
            <tr style="border:none;">
                <td colspan="4" style="font-family: 'Roboto', sans-serif;text-align: center;font-size: 16px;color:red;font-weight: 900;"><div style="display: flex;justify-content: center;align-items: center;padding: 0.5rem;">IN WORDS:<span style="text-transform: uppercase;font-weight: 500;">&nbsp;${amtStr}</span></div></td>
                <td  colspan="2" style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">Grand Total(INR)</td>
                <td  style="font-family: 'Roboto', sans-serif;font-size: 16px;color:red;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">${amount}</td>
            </tr>
            <%}%>
        </table>
    </div>
    <div style="display: flex;margin-top: 4rem;">
        <table cellpadding="10" style="width: 100%;">
            <tr>
                <td style="width: 60%;">
                    <h1 style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;margin:0;margin-bottom: 20px;line-height: 16px;">Terms & Conditions:</h1>
                    <p style="font-family: 'Roboto', sans-serif;font-size: 14px;margin:0;color:#444444;line-height: 16px;">
                        (a)The terms of this Agreement shall be binding for any further goods/services supplied by Company to Client.
                        (b)Upon execution of this Agreement, Client is agreeing to pay to Company, the full amount of the Fee.
                        (c) If client does not cancel attendance at, or participation in, the Program for any reason whatsoever,
                        Client will not be entitled to receive a refund.
                        d) Amount is inclusive of Tax.


                    </p>
                </td>
            </tr>
        </table>
    </div>
    <div style="font-family: 'Roboto', sans-serif;margin-top: 1rem;">
        <table cellpadding="10" style="width: 100%; border-top:1px solid #000;border-bottom: 1px solid #000;">
            <tr>
                <td align="center">
                    <h1 style="font-family: 'Roboto', sans-serif;font-weight: 700;font-size: 16px;margin:0;line-height: 16px;">This is a computer generated Invoice hence Stamp and Signature not required.</h1>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
