<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Welcome</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,400i,500,600,700|Work+Sans:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Exo:300,400" rel="stylesheet">

    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
    .download-apps table td a {
        text-decoration: none;
    }
    @media only screen and (max-width: 767px) {
        .main-wrapper {
            padding: 30px !important;
        }
        .main-wrapper table {
            width: 100% !important;
        }
        .email-logo tr td img.libwonder-logo-img {
            width: 200px !important;
        }
        .download-apps {
            display: block !important;
        }
        .download-apps table.app-links td {
            padding-top: 0 !important;
        }
        .download-apps table td a {
            margin-bottom: 7px !important;
            display: inline-block;
        }
        .download-apps table {
            width: 100% !important;
        }
        .download-apps table br {
            display: none !important;
        }
        .seperator {
            border-bottom: 1px solid #eeeeee !important;
        }
        .seperator img {
            display: none !important;
        }
    }
    </style>

</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +

                    request.getServerPort())
%>

<body>

<div style="background: #f1f1f1; padding: 30px 15px;">
    <div class="main-wrapper" style="max-width:700px;margin:0 auto;font-family: 'Poppins', sans-serif;font-size: 16px;color: #000;background: #ffffff;padding: 40px;border-radius: 10px;">
        <div>
            <table class="email-logo" style="width: 100%;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/arihant/footer-logo.png" class="libwonder-logo-img" alt="arihant-logo" width="100" height="100" style="width: 100px;height: 100px;margin-right: 8px;">
                    </td>
                </tr>
            </table>
            <table style="width: 100%;margin-top: 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: #EF7215;font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1.5rem;">
                            Your requested OTP from Arihant is ${otp}.
                        </p>
                        <p style="color: #EF7215;font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1.5rem;">
                            If you did not request an OTP, then you can ignore this email.
                        </p>
                    </td>
                </tr>
            </table>
            <table class="seperator" style="width: 100%;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <img src="${serverURL}/assets/ws/horizontal-separator-line.png" width="100%" height="2" style="width: 100%;height: 2px;">
                    </td>
                </tr>
            </table>
            <table style="width: 100%;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 13px; margin-bottom: 1rem;">
                            Arihant enables you to experience our next-gen smart eBooks where you can read, practice and analyze all your curriculum books in smarter way.
                        </p>
                    </td>
                </tr>
            </table>
            <table class="seperator" style="width: 100%;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/ws/horizontal-separator-line.png" width="100%" height="2" style="width: 100%;height: 2px;">
                    </td>
                </tr>
            </table>
            <div class="download-apps" style="display: flex; align-items: center;">
                <table style="width: 50%;">
                    <tr>
                        <td style="width:100%;padding-top: 1rem;display: flex;">
                            <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 13px; margin-bottom: 1rem;">
                                Take your smart eBooks with you.<br>
                                Download Arihant App from App Store and Play Store now.
                            </p>
                        </td>
                    </tr>
                </table>
                <table class="app-links" style="width: 50%;">
                    <tr>
                        <td style="width:50%;text-align: center; padding-top: 1.5rem;">
                            <a href="https://bit.ly/3By90Tv" target="_blank">
                                <img src="${serverURL}/assets/ws/arihant_Android.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                            <a href="javascript:void(0);">
                                <img src="${serverURL}/assets/ws/arihant_iOS.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
            <table style="width: 100%;margin: 0.5rem auto 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; text-align: center; line-height: 16px; font-size: 10px; margin-top: 1rem; margin-bottom: 0;">
                            This email has been sent to you by Arihant Publications India Limited.<br> Ramchhaya Building, 15, Daryaganj,  New Delhi - 110002.
                        </p>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
</body>
</html>
