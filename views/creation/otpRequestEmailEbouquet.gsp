<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Welcome</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,400i,500,600,700|Work+Sans:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Exo:300,400" rel="stylesheet">
</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())

%>
<body class="welcome-email-body" style="max-width: 600px; font-family: 'Work Sans', sans-serif; font-size: 16px; color: #000; background: #f8f8f8; padding: 30px 16px 16px; margin: 0 auto;">
<div class="welcome-email" style="background: #fff; padding: 32px;">
    <div class="welcome-email-logo">
        <div class="welcome-email-logo-img" style="width: 250px; height: auto;">
            <img src="${serverURL}/assets/${siteName}/logo.png" alt="" class="wonderslate-logo" style="display: block; width: 100%; max-width: 100%;">
        </div>
    </div>
    <p class="user-name" style="color: #F05A2A; font-weight: 500;">
        Your requested OTP!
    </p>
    <p class="welcome-email-head" style="font-size: 14px; font-weight: 300; color: #888888;">
        Your OTP to change password from ${clientName} is ${otp}.
    </p>
    <p class="welcome-email-head" style="font-size: 14px; font-weight: 300; color: #888888;">
        If you did not request an OTP, then you can ignore this email.
    </p>
    %{--<div class="download-app" style="text-align: center; padding: 16px 0; margin-top: 16px; border-top: 1px solid rgba(68, 68, 68, 0.4); border-bottom: 1px solid rgba(68, 68, 68, 0.4);">--}%
        %{--<p class="take-smart-book" style="font-size: 15px; font-weight: 500; color: #444444; margin: 0;">Take your smart eBooks with you.</p>--}%
        %{--<p class="download-wonderslate-app" style="font-size: 15px; font-weight: 500; color: #444444; margin: 0; font-weight: 300;">Download <%=grailsApplication.config.grails.appServer.siteName%> App from Google Play Store now.</p>--}%
        %{--<a href='https://play.google.com/store/apps/details?id=<%=(grailsApplication.config.grails.appServer.siteName=="e-Utkarsh")?"com.utkarshnew.android":"com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE"%>' class="download-app-btn" target="_blank" style="display: block; width: 122px; height: 40px; margin: 16px auto 0;">--}%
            %{--<img alt='Get it on Google Play' src='https://play.google.com/intl/en_us/badges/images/generic/en_badge_web_generic.png' class="download-app-btn-img" style="display: block; width: 100%;"/>--}%
        %{--</a>--}%
    %{--</div>--}%
    %{--<div class="imp-links" style="font-family: 'Montserrat', sans-serif; text-align: center; margin-top: 16px; margin-bottom: 32px;">--}%
        %{--<a href="${serverURL}/books/index?mode=browse" class='wonderslate-browse' target="_blank" style="display: inline-block; font-weight: 300; line-height: normal; font-size: 14px; text-align: right; letter-spacing: -0.01em; color: #F05A2A; text-decoration: none; margin-right: 10px;">Browse</a>--}%
        %{--<p class="blank-p" style="display: inline-block; width: 4px; height: 4px; background: rgba(68, 68, 68, 0.54); border-radius: 100px; margin: 0; margin-right: 10px; vertical-align: middle;"></p>--}%
        %{--<a href="${serverURL}/wonderpublish/mylibrary" class="wonderslate-library" target="_blank" style="display: inline-block; font-weight: 300; line-height: normal; font-size: 14px; text-align: right; letter-spacing: -0.01em; color: #F05A2A; text-decoration: none; margin-right: 10px;">My Library</a>--}%
    %{--</div>--}%
    %{--<div class="wonderslate-address" style="text-align: center;">--}%
        %{--<p class="address" style="font-weight: normal; line-height: normal; font-size: 10px; text-align: center; letter-spacing: -0.02em; color: rgba(68, 68, 68, 0.54);">--}%
            %{--This email has been sent to you by <%=(grailsApplication.config.grails.appServer.siteName=="e-Utkarsh")?"e-Utkarsh Innovations Private Limited, Jodhpur, Rajasthan":"Wonderslate Technologies Pvt Ltd. Wonderslate Technologies Pvt Ltd, No. 107, NGEF Layout, 4th Main Road, Sanjayanagar, Bangalore KA 560094, INDIA"%>.--}%
        %{--</p>--}%
    %{--</div>--}%
</div>
</body>
</html>
