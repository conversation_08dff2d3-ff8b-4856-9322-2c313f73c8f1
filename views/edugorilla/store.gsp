<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));


session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "edugorilla");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/edugorilla/navheader_new"></g:render>
<g:render template="/whitelabel/storeDisplay"></g:render>

<g:render template="/edugorilla/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<g:render template="/whitelabel/storeLogic"></g:render>
<script>
  $('.book-wrapper').on('mouseenter', '.book-item', function() {
    $(this).find('.book-btns-home').removeClass('hidden');
  }).on('mouseleave', '.book-item', function() {
    $(this).find('.book-btns-home').addClass('hidden');
  });
</script>
