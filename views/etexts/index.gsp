<%@ page import  = 'javax.servlet.http.Cookie' %>
<%  String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring (0, requestURL.indexOf(servletPath));
session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie( "siteName", "etexts");
newCookie.path = "/"
response.addCookie newCookie;
%>

<g:render template="/etexts/navheader_new"></g:render>

<section class="content-Preview">
    <div class="container-fluid position-relative pt-5 px-4 pb-4" style="overflow: hidden;">
        <div class="bg_circle"></div>
        <div class="bg_outline_circle"></div>
        <div class="searchbar input-group input-group-lg col-12 col-sm-8 col-md-6 pl-5">
            <input type="text" class="form-control typeahead" placeholder="Search by ISBN, Title or Author" autocomplete="off" id="search-book">
            <div class="input-group-append">
                <button class="btn" type="button"><img src="${assetPath(src: 'etexts/search-icon.png')}" class="img-responsive" alt=""></button>
            </div>
        </div>
        <div class="row mx-0">
            <div class="index-left col-12 col-sm-6 py-4">
                <div class="desktop_image text-center">
                    <figure>
                        <img src="${assetPath(src: 'etexts/desktop_image.png')}" class="img-responsive" alt="">
                    </figure>
                </div>
                <div class="index_buttons pt-2">
                    <ul class="list-inline row justify-content-center">
                        <li><a href="/etexts/store?grade=Humanities and Social Science&homepage=filter" class="btn btn-lg humanities">Humanities and</br>Social Science</a> </li>
                        <li><a href="/etexts/store?grade=Management&homepage=filter" class="btn btn-lg management">Management</a> </li>
                    </ul>
                    <ul class="list-inline row justify-content-center">
                        <li><a href="/etexts/store?grade=UPSC Resources&homepage=filter" class="btn btn-lg management upsc">UPSC Resources</a> </li>
                    </ul>
                </div>
                <div class="index_quick_links pt-3">
                    <ul class="list-inline row justify-content-center">
                        <li>*Annual Subscription</li>
                        <li>Quick Access</li>
                        <li>Offline Support</li>
                    </ul>
                </div>
            </div>
            <div class="index-right col-12 col-sm-4 ml-auto pl-5">
                <div class="heading text-left mb-4">
                    <h1>SAGE e-texts</h1>
                    <h4>Available 24/7, 365 days</h4>
                </div>
                <div class="row icon_texts mb-3">
                    <div class="icon mr-3">
                        <img src="${assetPath(src: 'etexts/icon-01.png')}" class="img-responsive" alt="">
                    </div>
                    <div class="texts">
                        <h5>STUDY ON THE GO</h5>
                        <p>IP based access on<br>laptops and smartphones</p>
                    </div>
                </div>
                <div class="row icon_texts mb-3">
                    <div class="icon mr-3">
                        <img src="${assetPath(src: 'etexts/icon-02.png')}" class="img-responsive" alt="">
                    </div>
                    <div class="texts">
                        <h5>NOTES</h5>
                        <p>Add notes in your e-textbook<br>to refer back later</p>
                    </div>
                </div>
                <div class="row icon_texts mb-3">
                    <div class="icon mr-3">
                        <img src="${assetPath(src: 'etexts/icon-03.png')}" class="img-responsive" alt="">
                    </div>
                    <div class="texts">
                        <h5>TEXT SIZE</h5>
                        <p>Adjust the text size of the content<br>to suit your reading preference</p>
                    </div>
                </div>
                <div class="row icon_texts mb-3">
                    <div class="icon mr-3">
                        <img src="${assetPath(src: 'etexts/icon-04.png')}" class="img-responsive" alt="">
                    </div>
                    <div class="texts">
                        <h5>SEARCH FOR CONTENT</h5>
                        <p>Within the title or across<br>your entire library</p>
                    </div>
                </div>
                <div class="row icon_texts mb-3">
                    <div class="icon mr-3">
                        <img src="${assetPath(src: 'etexts/icon-05.png')}" class="img-responsive" alt="">
                    </div>
                    <div class="texts">
                        <h5>LOOKUP</h5>
                        <p>Unfamiliar words using Google<br>or Wikipedia search</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<g:render template="/etexts/footer_new"></g:render>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>

<script>
    var updatesignup=false;
    var updateUserId=0;
    jQuery(document).ready(function ($) {
        if("${session["userdetails"]}"==null || "${session["userdetails"]}"=='' ) {
            if ("${userEmail}" != "" && "${sageLogin}"!="true") {
                var updateEmail = htmlDecode("${userEmail}")
                $('#username').val(updateEmail).attr('readonly', true);


                $('.evidyaLogin').show();

                $('.evidyaloginWrapper').hide();
                $('.evidyaSignup').show();
                updatesignup = true;
                updateUserId = "${userId}"
            }
            else if("${userEmail}" != "" && "${sageLogin}"=="true"){
                $(window).load(function () {
                    $("#signUpLimitModal").modal("show");
                });
            }
        }

    });
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            console.log("query="+query+" and process="+process);
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks1(data) {
                    console.log("data="+data.status);
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString = document.getElementById("search-book").value;
        window.location.href = "/etexts/store?search=true&searchString="+searchString;

    }


    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

</script>

<script>
    function SendLinkByMail() {
        var subject= "Free Trial | eTexts";
        var body = "";
        body += "";
        var uri = "mailto:<EMAIL>?subject=";
        uri += encodeURIComponent(subject);
        uri += "&body=";
        uri += encodeURIComponent(body);
        window.open(uri);
    }
    if('${params.fplink}'==='expired'){
        $('.evidyaLogin').show();
        alert("The reset password link is expired, Please try again by clicking on forgot password.")
    }
</script>
