<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
  <!-- Basic -->
  <meta charset="utf-8">
  <%if(header!=null&&!"notadded".equals(header)){%>
  <%= header %>
  <%}else{%>
  <title><%= title!=null?title:"SAGE | e-vidya"%></title>
  <meta name="description" content="SAGE | e-vidya">
  <%}%>
  <link rel="icon"  href="${assetPath(src: 'evidya/favicon.ico')}">
  <!-- Mobile Specific Metas -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

  <!-- End SmartBanner configuration -->
  <!-- Web Fonts  -->
  <asset:stylesheet href="font-awesome.min.css"/>
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.10/css/all.css" integrity="sha384-+d0P83n9kaQMCwj8F4RJB66tzIwOKmrdb46+porD/OvrJ+37WqIM7UoBtwHO6Nlg" crossorigin="anonymous">
  <asset:stylesheet href="bootstrap.css"/>
  <link rel="stylesheet" href="/assets/katex.min.css">
  <% if(testGenCss){ %>
  <asset:stylesheet href="common.css"/>
  <style>
  body,html {
    padding-top: 0;
  }

  </style>
  <%}%>

  <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,600,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Abril+Fatface" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <asset:stylesheet href="evidya.css"/>
  <asset:stylesheet href="landingpage/tempEvidya.css"/>

  <!-- Modernizr -->

  <asset:javascript src="jquery-1.11.2.min.js"/>
  <script src="/assets/katex.min.js"></script>
  <script src="/assets/auto-render.min.js"></script>

  <style>
  .btco-hover-menu .open > .dropdown-toggle::after{
    transform: rotate(-90deg);
  }
  .btco-hover-menu .show > .dropdown-toggle::after{
    transform: rotate(-90deg);
  }
  .btco-hover-menu ul li {
    position:relative;
  }

  .btco-hover-menu ul ul li {
    position:relative;
  }
  .btco-hover-menu ul ul li:hover> ul {
    display:block;
  }
  .btco-hover-menu ul ul ul {
    position:absolute;
    top:0;
    left:100%;
    min-width:200px;
    display:none;
  }

  @media screen and (max-width: 767px) {
    .evidya .navbar-right-mobile {
      margin-top: 15px;
    }
    .evidya .mobile-user {
      background-color: #fff;
      width: 32px;
      height: 32px;
    }
    .evidya .wonderslate-navbar {
      background: #1f419b;
    }
    .evidya .mobile-header-brand .navbar-brand {
      background: url(https://evidya.sagepub.in/assets/evidya/logo.png) center no-repeat;
      width: 200px;
      background-size: contain;
      background-color: #ffffff;
      padding: 1rem;
      border-top-right-radius: 50px;
      border-bottom-right-radius: 50px;
      display: flex;
      align-items: center;
      height: 55px;
      position: relative;
      left: -1rem;
    }
  }
  </style>

</head>
<%session['siteId'] = new Integer(12);%>
<body class="evidya">
<sec:ifNotLoggedIn>
  <g:render template="/funlearn/signIn"></g:render>
</sec:ifNotLoggedIn>

<header class="evidya mobile-header hidden-sm hidden-md hidden-lg">
  <%if(!hideTopNavigation){%>
  <nav class="navbar wonderslate-navbar">
    <div class="container-fluid navbar-container">
      <div class="navbar-header mobile-header-brand">

        %{--<a class="navbar-brand" href="/store?mode=browse">e-Utkarsh</a>--}%

        <a class="navbar-brand" href="/evidya/index">e-Vidya</a>

      </div>
      %{--<a href="https://play.google.com/store/apps/details?id=com.utkarshnew.android" class="download-app-btn" target="_blank" style="width: 100px; display: inline-block; margin-left: 16px;">--}%
          %{--<img alt='Get it on Google Play' src='${assetPath(src: 'landingpageImages/playstore.png')}' class="download-app-btn-img"/>--}%
        %{--</a>--}%
      <ul class="nav navbar-nav navbar-right-mobile">
        %{-- <li class="mobile-header-icon">
          <a class="mobile-search" href="#"></a>
        </li> --}%
        <sec:ifNotLoggedIn>
          <li class="mobile-header-icon">
            <a href="javascript:showSignInModal();" class="mobile-user"></a>
          </li>
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
          <li class="mobile-header-icon dropdown">
            %{--<a href="#" class="dropdown-toggle mobile-user" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"></a>--}%
            <a href="#" class="dropdown-toggle mobile-user" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"></a>
            <div class="dropdown-menu profile-dropdown">
              <a href="/creation/userProfile" style="display: block; float: left; width: 100%; font-size: 14px;">
                <div class="user-image">
                  <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                  <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport">
                  <%} else { %> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" alt="">
                  <%}%>
                  <p  class="user-edit-profile">Edit</p>
                </div>
                <div class="logged-in-user-details">
                  <p class="loggedin-user-name">Hello <span id="loggedin-user-name" class="user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                  <p class="loggedin-user-mobile"><%= session["userdetails"]!=null?session["userdetails"].mobile:""%></p>
                  <p class="loggedin-user-email"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                </div>
              </a>

              <div class="user-orders">
                <a href="\creation/userProfile#orders">Your Orders</a>
              </div>

              <div class="user-logout">
                <p>Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <a href="/logoff" id="logout">Sign out</a></p>
              </div>
            </div>
          </li>
        </sec:ifLoggedIn>
      </ul>
    </div>
  </nav>
</header>

<% if (!hideBottomIcons) { %>
  <ul class="mobile-bottom-menu-wrapper hidden-md hidden-sm hidden-lg">
    <sec:ifNotLoggedIn>
      %{--<li class="mobile-bottom-menu-item">--}%
        %{--<a href="/eutkarsh/store?mode=browse" class="mobile-bottom-menu-item-link mobile-store">Store</a>--}%
        %{--Store--}%
      %{--</li>--}%
      <li class="mobile-bottom-menu-item">
        <a href="javascript:showSignUpModal();" class="mobile-bottom-menu-item-link mobile-library">Library</a>
        Library
      </li>

    </sec:ifNotLoggedIn>

    <sec:ifLoggedIn>
      %{--<li class="mobile-bottom-menu-item">--}%
        %{--<a href="" class="mobile-bottom-menu-item-link mobile-store">Store</a>--}%
        %{--Store--}%
      %{--</li>--}%
      <li class="mobile-bottom-menu-item">
        <a href="/evidya/library" class="mobile-bottom-menu-item-link mobile-library">Library</a>
        Library
      </li>
      %{--<li class="mobile-bottom-menu-item">--}%
        %{--<a href="/test-generator" class="mobile-bottom-menu-item-link mobile-test-gen">Test Generator</a>--}%
        %{--Test Generator--}%
      %{--</li>--}%
    </sec:ifLoggedIn>
  </ul>
<% } %>





<header class="utkarsh sticky-header hidden-xs">
  <nav id="nav" class="navbar wonderslate-navbar btco-hover-menu">
    <div class="container-fluid navbar-container">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main-navbar" aria-expanded="false">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="/evidya/index">e-Vidya</a>
      </div>

      <div class="collapse navbar-collapse" id="main-navbar">
        <sec:ifNotLoggedIn>
        <ul class="nav navbar-nav header-menus">


          <%if(showSearch){%>
          <li class="header-menu-item" style="padding-left: 16px;">
            <input type="text" class="typeahead user-search-input" id="search-book" placeholder="Search for book title, subject etc.">
            <button class="search-btn" onclick="javascript:displayBooks();"><i class="material-icons">search</i></button>
          </li>
          <%}%>
        </ul>

        <ul class="nav navbar-nav navbar-right">
          %{--<li>--}%
            %{--<a href='https://play.google.com/store/apps/details?id=com.utkarshnew.android' class="download-app-btn" target="_blank" style="padding-top: 0;">--}%
              %{--<img alt='Get it on Google Play' src='https://play.google.com/intl/en_us/badges/images/generic/en_badge_web_generic.png' class="download-app-btn-img"/>--}%
            %{--</a>--}%
          %{--</li>--}%
          <li>
            <a href="javascript:showSignInModal();" class="login-btn waves-effect waves-teal waves-ripple">Sign in</a>
          </li>
          <li>
            <a href="javascript:showSignUpModal();" class="signup-btn waves-effect waves-ripple">Sign up</a>
          </li>
        </ul>
      </sec:ifNotLoggedIn>

      
      <ul class="nav navbar-nav header-menus">
        <li class="header-menu-item">
          <a href="/evidya/store" class="header-menu-item-link">Store</a>
        </li>
        %{--<li class="header-menu-item">--}%
          %{--<a href="" class="header-menu-item-link">Store</a>--}%
        %{--</li>--}%
        <% if(showLibrary){%>
        <li class="header-menu-item">
          <a href="/evidya/library" class="header-menu-item-link">Library</a>
        </li>
        <%}%>
        <sec:ifLoggedIn>
        <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
        <li class="header-menu-item dropdown">
          <a href="#" class="header-menu-item-link dropdown-toggle" id="publishing-link" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Publishing</a>
          <ul class="dropdown-menu" id="publishing-dropdown" aria-labelledby="dLabel">
            <li>
              <a href="/publishing-desk">Publishing Desk</a>
            </li>
            <li>
              <a href="/wonderpublish/manageTabs">Manage Tags</a>
            </li>
            <li>
              <a href="/wonderpublish/manageExams">Manage Exams</a>
            </li>
            <li>
              <a href="/institute/isbnKeyword">Manage Isbn/Keywords</a>
            </li>
          </ul>
        </li>
      </sec:ifAllGranted>
        <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
          <li class="header-menu-item dropdown">
            <a href="#" class="header-menu-item-link dropdown-toggle" id="digital-link" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Digital Marketing</a>
            <ul class="dropdown-menu" id="digital-dropdown" aria-labelledby="dLabel">
              <li>
                <a href="/header/index">Header Management</a>
              </li>
              <li>
                <a href="/wonderpublish/pubDesk?mode=print">Print books</a>
              </li>

            </ul>
          </li>
        </sec:ifAllGranted>

          <sec:ifAllGranted roles="ROLE_FINANCE">
            <li class="header-menu-item">
              <a href="/publishing-sales" class="header-menu-item-link">Sales</a>
            </li>
          </sec:ifAllGranted>
          <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">
            <li class="header-menu-item dropdown">
              <a href="#" class="header-menu-item-link dropdown-toggle" id="admin-link" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Admin</a>
              <ul class="dropdown-menu" id="admin-dropdown" aria-labelledby="dLabel">
                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                  <li>
                    <a href="/admin/managePublishers">Publisher Management</a>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                  <li>
                    <a href="/institute/admin">Institute Management</a>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                  <li>
                    <a href="/institute/libAdmin">Institute/Library Management</a>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                  <li>
                    <a  href="/wonderpublish/wseditor">WS Editor</a>
                  </li>
                </sec:ifAllGranted>
              </ul>
            </li>
          </sec:ifAnyGranted>
          <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
            <li class="header-menu-item dropdown">
              <a href="#" class="header-menu-item-link dropdown-toggle" id="admin-link1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Report</a>
              <ul class="dropdown-menu" id="admin-dropdown1" aria-labelledby="dLabel">
                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                  <li>
                    <a href="#" class="dropdown-item dropdown-toggle" >Usage Report</a>
                    <ul class="dropdown-menu">
                      <li><a class="dropdown-item" href="/institute/usageReport">Institute Report</a></li>
                      <li><a class="dropdown-item" href="/institute/registeredUserReport?report=usagebookview">Books Report</a></li>
                    </ul>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                  <li>
                    <a href="/institute/registeredUserReport?report=login">Users Report</a>
                  </li>
                </sec:ifAllGranted>
              </ul>
            </li>
          </sec:ifAnyGranted>
           <% if(showSearch) {%>
      <li class="header-menu-item" style="padding-left: 16px;">
        <input type="text" class="user-search-input" id="search-book" placeholder="Search for book title, subject etc.">
      </li>
    <% } %>

  </ul>
  <ul class="nav navbar-nav navbar-right">
    <li class="dropdown">
      <a href="#" class="browse-link" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">Browse</a>
      <div class="browseMenu dropdown-menu">
        <div class="col-md-6">
          <h5>Browse by Subject</h5>
          <div class="browse-icons">
            <div class="circle1">
              <img src="${assetPath(src: 'sage/Management_Icon.svg')}" class="img-responsive" alt="">
            </div>
            <a href="/evidya/store?grade=Business&homepage=filter"> Management </a>
          </div>
          <div class="browse-icons">
            <div class="circle1">
              <img src="${assetPath(src: 'sage/Social_Sciences_Icon.svg')}" class="img-responsive" alt="">
            </div>
            <a href="/evidya/store?grade=Social Science&homepage=filter">Social Science</a>
          </div>
          <div class="browse-icons">
            <div class="circle1">
              <img src="${assetPath(src: 'sage/Non-fiction.svg')}" class="img-responsive" alt="">
            </div>
            <a href="/evidya/store?grade=Non-Fiction&homepage=filter">Non-Fiction</a>
          </div>
        </div>
        <div class="col-md-6">
          <h5>Browse by Language</h5>
          <ul class="language">
            <li><a href="/evidya/store?language=English&homepage=filter">English</a></li>
            <li><a href="/evidya/store?language=Hindi&homepage=filter">Hindi</a></li>
            <li><a href="/evidya/store?language=Marathi&homepage=filter">Marathi</a></li>
            <li><a href="/evidya/store?language=Bengali&homepage=filter">Bengali</a></li>
          </ul>
        </div>
      </div>
    </li>
    <li class="dropdown">
      <a href="#" class="user-logged-in" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-responsive">
        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="">
        <%}%>
      </a>
      <div class="dropdown-menu profile-dropdown">
        <a href="/creation/userProfile" style="display: block; float: left; width: 100%; font-size: 14px;">
          <div class="user-image">
            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport">
            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="">
            <%}%>
            <p  class="user-edit-profile">Edit</p>
          </div>
          <div class="logged-in-user-details">
            <p class="loggedin-user-name">Hello <span id="loggedin-user-name" class="user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
            <p class="loggedin-user-mobile"><%= session["userdetails"]!=null?session["userdetails"].mobile:""%></p>
            <p class="loggedin-user-email"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
          </div>
        </a>

        %{--<div class="user-orders">--}%
          %{--<a href="\creation/userProfile#orders">Your Orders</a>--}%
        %{--</div>--}%

        <div class="user-logout">
          <p>Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <a href="/logoff" id="logout">Sign out</a></p>
        </div>
      </div>
    </li>
  </ul>
</sec:ifLoggedIn>
</div>
</div>
</nav>
  <%}%>
</header>
<script>
  var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
</script>
