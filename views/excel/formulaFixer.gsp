<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>

<style>
.sage-body .sage-banner {
    display: none;
}
.image-upload > input {
    display: none;
}

.cke_textarea_inline {
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}
</style>
<div class="notesCreator">
    <div class="container" >
        <div id="static-content" width="90%" class="rounded">
            <div class="row ">
                <div class="col-md-12">
                    <h4>Content Formatter</h4><br>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group notes float-label-control">
                        <div class="cktext">
                            <textarea  rows="30" class="form-control" id="inputContent" name="inputContent" placeholder="Enter the content to be formatted"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row notes mt-5">
                %{--                        <div class="form-group">--}%
                <div class=" col-sm-12 text-center">
                    <button type="button" onclick="javascript:callConverter()" class="btn btn-primary">Submit</button>
                </div>
                %{--                        </div>--}%
            </div>

            <div class="row">
                <div class="col-md-12">
                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addhtml" id="addhtml" action="/resourceCreator/addHTML" method="post">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group notes float-label-control">
                                    <div class="cktext">
                                        <textarea  rows="30" class="form-control" id="notes" name="notes" placeholder="Enter your notes here"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<g:render template="/${session['entryController']}/footer_new"></g:render>






<script>



        CKEDITOR.replace('notes', {
            height: 600,
            customConfig: '/assets/ckeditor/customConfig.js',
            extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog,autolink',
            mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',

              // The following options are not necessary and are used here for presentation purposes only.
            // They configure the Styles drop-down list and widgets to use classes.

            stylesSet: [
                {name: 'Narrow image', type: 'widget', widget: 'image', attributes: {'class': 'image-narrow'}},
                {name: 'Wide image', type: 'widget', widget: 'image', attributes: {'class': 'image-wide'}}
            ],



            // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
            // resizer (because image size is controlled by widget styles or the image takes maximum
            // 100% of the editor width).
            image2_alignClasses: ['image-align-left', 'image-align-center', 'image-align-right'],
        });

    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }

    CKEDITOR.on( 'dialogDefinition', function( ev ) {
        var dialogName = ev.data.name;
        var dialogDefinition = ev.data.definition;
        if ( dialogName == 'link' ) {
            var targetTab = dialogDefinition.getContents( 'target' );
            var targetField = targetTab.get( 'linkTargetType' );
            targetField[ 'default' ] = '_blank';
        }
    });






    function callConverter(){
        var inputContent = document.getElementById("inputContent").value;
        inputContent = replaceAll(inputContent,'&','~')
        <g:remoteFunction controller="excel" action="mathConverter"  onSuccess="displayHtmls(data);" params="'inputContent='+inputContent" />
    }




    function displayHtmls(data){
        var htmls = data.convertedContent;
         htmls = htmls.replace(/\\\\/g , '\\');
        htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');
        CKEDITOR.instances.notes.setData(htmls);

    }
</script>

<script>
    $('.navbar-wonderslate').css({
        'padding' : '0 0 0 10px'
    });
    $('.navbar-right').css({
        'padding-right' : '10px'
    });
    $('.user-profile-dropdown .login-signup-dropdown').css({
        'right' : '-40px'
    });
    $('.search-book').detach();

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
</script>
</body>
</html>
