<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
.custom_container_new{
    width: calc(100% - 10%);
    margin: 0 auto;
}
.priceListTable tr th,
.priceListTable tr td{
    padding: 10px;
}
.priceListTable tr td > *{
    width: 100%;
}


.processing {
    background-color: yellow;
}

.ok {
    background-color: white;
    color: black;
}

.error {
    background-color: red;
    color: white;
}
#tableContainer {
    overflow: scroll;
}
</style>
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<div class="custom_container_new">
    <h4 class="text-center my-4">Question and Answers upload manager</h4>
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 20px auto; float: none; padding: 15px;">
            <div id="content-books">

                <div class="row">
                    <div class="form-group" style="margin: 10px">
                        <label for="excelFileInput">Choose Excel File:</label>
                        <input type="file" id="excelFileInput" accept=".xlsx, .xls" class="form-control">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group"  style="margin: 10px">

                        <button type="button" id="download-btn" class="btn btn-primary btn-lg" style="border-width: 0px;" onclick="handleFileUpload()">Upload</button>

                    </div>
                </div>


            </div>
        </div>


    </div>
</div>
<div class="custom_container_new">
    <div class="row">
        <div id="tableContainer"></div><br><br>

    </div>
    <div class="row" >
        <button id="exportButton" class="btn btn-primary" style="display: none">Export updated file</button>
    </div>
    <div class="row" style="display: none">
        <div id="errorTableContainer"></div>
    </div>
</div>


<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="fileUploaderInclude"></g:render>
<script>
    var addAllRows=true;
    var mandatoryFields = ['Question'];
    var responseFields = ["loginId", "password", "BooksAdded"];


    function handleFileUpload() {
        if(document.getElementById('excelFileInput').files.length==0){
            alert("Please select the file to upload.");
            document.getElementById('excelFileInput').focus();
        }
        else {
            failedRows = [];
            var fileInput = document.getElementById('excelFileInput');
            var file = fileInput.files[0];
            var reader = new FileReader();

            reader.onload = function (e) {
                var data = new Uint8Array(e.target.result);
                var workbook = XLSX.read(data, {type: 'array'});

                var sheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[sheetName];
                var jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

                columnNames = jsonData[0]; // Assuming the first row contains column names

                // Create the initial table with column names
                createTable(columnNames);


                // Start the API calling process
                callServerAPI(jsonData, columnNames, 1);
            }


            reader.readAsArrayBuffer(file);
        }
    }
    function callServerAPIInternal(rowData) {
        // Make an AJAX request or use fetch() to call your server API
        // Adjust the URL and method as per your server implementation
        var url = '/excel/processQA';
        // Get a reference to the radio buttons using their name
        // Get a reference to the radio buttons using their name


        rowData["chapterId"]="${params.chapterId}";
        rowData["resId"] = resId;

        return fetch(url, {
            method: 'POST',
            body: JSON.stringify(rowData),
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json());
    }


</script>
</body>
</html>
