<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="wonderslate/flashcardHome.css" async="true"/>
<style>
.video-url .modal .modal-body input {
    margin-bottom: 0.5rem !important;
}
.web-url .modal .modal-body input {
    margin-bottom: 1rem !important;
}
</style>
<div  class="folder-flashcards">

<div class="modal fade " id="deleteSetModal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
            <div class="loaders">
                <div class="bar-line"></div>
            </div>
            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p>Are you sure to Delete this Whole Set?</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" onclick="javascript:cancelSetDelete();" class="btn cancel-btn">Cancel</button>
                <button type="button" onclick="javascript:deleteSetConfirm();" class="btn close-btn">Delete</button>
            </div>

        </div>
    </div>
</div>

<div id="overlay-bg"></div>
<section class="flashcard-folder">
    <div class="container">
        <div class="row justify-content-center mt-4">
            <div class="col-12 col-lg-11 row ml-lg-0 p-lg-0">

                <div class="d-flex mobile-stocker-web">
                    <h2 class="hero-title">
                        <a onclick="javascript:window.history.back();"><i class="material-icons mr-2">keyboard_backspace</i></a>
                        <span class="align-items-center">${params.folderName}</span></h2>
                    <div class='materialsAddDropdown dropdown'>
                        <button class='btn dropdown-toggle d-flex align-items-center justify-content-center' type='button' id='dropdownMenuButton' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>
                            <i class='material-icons'>add</i> <span>Add</span>
                        </button>
                        <div id='materialsAddDropdownMenus' class='dropdown-menu dropdown-menu-right border-0' aria-labelledby='dropdownMenuButton'>
                            <a class='dropdown-item d-flex align-items-center new-folder' href='/resources/createFlashCards?folderId=${params.folderId}'>
                                <i class='material-icons mr-0 pr-1'>style</i> <span>Flashcard</span>
                            </a>
                            <a class='dropdown-item d-flex align-items-center new-folder' href='/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=create&folderId=${params.folderId}'>
                                <i class='material-icons mr-0 pr-1'>help_outline</i> <span>MCQs</span>
                            </a>

                            <a class='dropdown-item d-flex align-items-center new-folder' href='/resourceCreator/addNotes?resourceType=Notes&mode=create&folderId=${params.folderId}'>
                                <i class='material-icons mr-0 pr-1'>description</i> <span>Notes</span>
                            </a>
                            <a class='dropdown-item d-flex align-items-center new-folder' href='javascript:openVideos();'>
                                <i class='material-icons mr-0 pr-1'>play_circle_outline</i> <span>Videos</span>
                            </a>

                            <a class='dropdown-item d-flex align-items-center new-folder' href='javascript:openSolution();'>
                                <i class='material-icons mr-0 pr-1'>link</i> <span>Web Link</span>
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="container mt-5 mt-lg-0" id="folderDtl">

    </div>

    <div class="loading-icon"  style="display: none">
        <div class="loading-icon">
            <div class="loader-wrapper">
                <div class="loader">Loading</div>
            </div>
        </div>
    </div>
</section>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="youtubeclose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <iframe width="100%" height="500px" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
</div>
<g:render template="/resources/videoSection"></g:render>
<g:render template="/resources/weblinksSection"></g:render>
<script>
    var cardLabel="";
    var labelBackgroundColor="";
    var cardColor="";
    var editAction="";
    var privacyLevel=null;
    var shareLink="";
    var actionString="";


    //set the user folders
    var htmlStr='';
    var favouriteResIds = "${resIds}";
    var favArray = favouriteResIds.split(',');

    var previousChapterId = '';
    var loggedInUser = false;
    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>
    <%if(folderDtl==null||"".equals(folderDtl)){%>
    document.getElementById("folderDtl").innerHTML= "No data";
    <%}else{%>
    var userFolderDtlString = "${folderDtl}";
    userFolderDtlString=((userFolderDtlString.replace(/&#92;/g,'\\')).replace(/&quot;/g,'"'))
    var userFolderDtl = JSON.parse(userFolderDtlString);
    if(userFolderDtl ===undefined || userFolderDtl.length==0){
        htmlStr +="<div class='d-flex pt-8'>" +
            "<div class='text-center w-100'>" +
            "<img src=\"${assetPath(src: 'ws/boxempty.svg')}\"  width='125px' height='117px'>"+
            "<p class='flashcard-empty-msg'>Your folder is empty</p>"+
            "<p class='flashcard-empty-msg bold'>Start adding your materials</p>"+
            "</div>" +
            "</div>";
    }
    else {
        htmlStr += "<div class='row justify-content-center'>" +
            "<div class='col-10'>" +
            "<div class='col-12'>";
        htmlStr += " <div class=\"d-flex flex-wrap flashcards\">\n";
        for (var i = 0; i < userFolderDtl.length; i++) {

            cardLabel="";
            labelBackgroundColor="";
            cardColor="";
            editAction="";
            privacyLevel=userFolderDtl[i].privacyLevel;
            shareLink="";
            actionString="";
            var color = "#" + "colors".split("").map(function(){return parseInt(Math.random()*0x10).toString(16);}).join("");

            if(userFolderDtl[i].resType=='Notes') {
                cardLabel = "Notes";
                cardColor = "blue";
                shareLink = "javascript:shareNotes("+JSON.stringify(userFolderDtl[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/resources/notesViewer?resId=" +userFolderDtl[i].resId + "&name=" + encodeURIComponent(userFolderDtl[i].resName).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }
            else if(userFolderDtl[i].resType=='KeyValues'){
                cardLabel = "Flashcard";
                cardColor = "yellow";
                labelBackgroundColor = color;
                shareLink = "javascript:shareFlashCard("+JSON.stringify(userFolderDtl[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/resources/displayFlashCards?resId=" + userFolderDtl[i].resId + "&name=" + encodeURIComponent(userFolderDtl[i].resName).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }
            else if(userFolderDtl[i].resType=='Reference Videos') {
                cardLabel = "Video";
                cardColor = "pink";
                actionString = "<a href='javascript:playVideo(\"" + userFolderDtl[i].resLink + "\"," + userFolderDtl[i].resId + ")' id='resAction_" + userFolderDtl[i].resId + "' class='chapter_txt'>\n";
                shareLink = "javascript:shareNotes("+JSON.stringify(userFolderDtl[i]).replace(/'/g,"&#39;")+")";
            }

            else if(userFolderDtl[i].resType=='Reference Web Links'){
                cardLabel = "Link";
                cardColor = "green";
                shareLink = "javascript:shareWeblink("+JSON.stringify(userFolderDtl[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a  href='javascript:openWebRef(" + userFolderDtl[i].resId + ",\""+userFolderDtl[i].resLink.replace('#', ':')+"\")' id='resAction_"+userFolderDtl[i].resId+"' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }
            else if(userFolderDtl[i].resType=='Multiple Choice Questions'){
                cardLabel = "MCQ";
                cardColor = "violet";
                shareLink = "javascript:shareMCQ("+JSON.stringify(userFolderDtl[i]).replace(/'/g,"&#39;")+")";
                actionString = "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + userFolderDtl[i].resId + "&name=" + encodeURIComponent(userFolderDtl[i].resName).replace(/'/g,"&#39;") + "' class='chapter_txt' onclick='javascript:callAppLoader()'>\n";

            }




            htmlStr += "            <div class=\"col-12 col-md-6 col-lg-6 mt-3 px-0 px-md-3\" id=\"flash"+userFolderDtl[i].resId+"\">\n" +
                "                <div class=\"flashcard-set \">\n" +
                "<div class='mymaterial-img '>"+
                "   <div class='box m-0 "+cardColor+"' style='background: "+labelBackgroundColor+";'>" +
                    actionString+
                "<p class='cardLabel'>"+cardLabel+"</p>"+
                "</div></div>"+
                "                    <div class=\"title-wrapper d-flex \">\n" +
                "                        <h4>" + userFolderDtl[i].resName + "</h4>\n" +
                "                        <div class=\"flashcard-actions\">\n" ;

            <sec:ifLoggedIn>
            if (userFolderDtl[i].resType == "Multiple Choice Questions") {
                htmlStr += " <a href='/wonderpublish/quizcreator?resourceType=Multiple%20Choice%20Questions&useType=quiz&mode=edit&id=" + userFolderDtl[i].resId + "'  class='set'><i class=\"material-icons\">edit</i></a>\n";
            }else if (userFolderDtl[i].resType == "Notes") {
                htmlStr += " <a href='/resourceCreator/addNotes?resourceType=Notes&mode=edit&id=" + userFolderDtl[i].resId + "'  class='set'><i class=\"material-icons\">edit</i></a>\n";
            }
            htmlStr += " <a href='javascript:deleteFlashCard(" + userFolderDtl[i].resId + ")' id='del" + userFolderDtl[i].resId + "' class='set'><i class=\"material-icons\">delete</i></a>\n";

            </sec:ifLoggedIn>
            if(favArray.includes(""+userFolderDtl[i].resId)){
                htmlStr +=  " <a href='javascript:removeFavourite("+userFolderDtl[i].resId+")' id='res"+userFolderDtl[i].resId+"' class='active unset'><i class=\"material-icons\">favorite</i></a>\n" ;
                htmlStr +=  " <a href='javascript:addFavourite("+userFolderDtl[i].resId+")' id='res"+userFolderDtl[i].resId+"' class='set' style='display: none;'><i class=\"material-icons\">favorite</i></a>\n" ;
            }else{
                htmlStr +=  "<a href='javascript:addFavourite("+userFolderDtl[i].resId+")' id='res"+userFolderDtl[i].resId+"' class='set'><i class=\"material-icons\">favorite</i></a>\n" ;
                htmlStr +=  " <a href='javascript:removeFavourite("+userFolderDtl[i].resId+")' id='res"+userFolderDtl[i].resId+"' class='active unset' style='display: none;'><i class=\"material-icons\">favorite</i></a>\n" ;
            }

            // "                            <i class=\"material-icons\">share</i>\n" +

            htmlStr +=    "                        </div>\n" +
                "                    </div>\n";
            if("KeyValues"==userFolderDtl[i].resType) {
                htmlStr += "<a href='/resources/displayFlashCards?resId=" + userFolderDtl[i].resId + "&name=" + encodeURIComponent(userFolderDtl[i].resName).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }else if("Multiple Choice Questions"==userFolderDtl[i].resType) {
                htmlStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + userFolderDtl[i].resId + "&name=" + (encodeURIComponent(userFolderDtl[i].resName)).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }else if("Notes"==userFolderDtl[i].resType) {
                htmlStr += "<a href='/resources/notesViewer?resId=" + userFolderDtl[i].resId + "&name=" + encodeURIComponent(userFolderDtl[i].resName).replace(/'/g,"&#39;") + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }else if("Reference Videos"==userFolderDtl[i].resType) {
                htmlStr += "<a href='javascript:playVideo(\"" + userFolderDtl[i].resLink + "\"," + userFolderDtl[i].resId + ")' id='resAction_" + userFolderDtl[i].resId + "' class='d-block h-100' onclick='javascript:callAppLoader()'>";
            }else if(userFolderDtl[i].resType=="Reference Web Links"){
                htmlStr += "<a  href='javascript:openWebRef(" + userFolderDtl[i].resId + ",\""+userFolderDtl[i].resLink.replace('#', ':')+"\")' id='resAction_"+userFolderDtl[i].resId+"' class='d-block h-100' onclick='javascript:callAppLoader()'>"
            }
            htmlStr +=     "<div>";
            if (userFolderDtl[i].description) {
                htmlStr += "<p>userFolderDtl[i].description</p>";
            } else {
                htmlStr += "<p></p>\n";
            }
            htmlStr += "<div class=\"progress d-none\">\n" +
                "    <div class=\"progress-bar green\" role=\"progressbar\" aria-valuenow=\"70\" aria-valuemin=\"0\" aria-valuemax=\"100\" style=\"width:20%\">\n" +

                "    </div>\n" +
                "  </div>" +
                " </div>\n" +
                "</a>" +
                "  </div>" +
                "</div>\n";
        }


        htmlStr += "</div>"+
            "</div>"+
            "</div>";
        htmlStr += "<div class='col-12 col-lg-2'>" +


            "</div>" +
            "</div>";

    }
    document.getElementById("folderDtl").innerHTML = htmlStr;
    <%}%>


    var tempResId;
    function addFavourite(resId){
        tempResId=resId;
        $('.set#res'+tempResId).hide().removeClass('heart');
        $('.unset#res'+tempResId).show().addClass('heart');
        <g:remoteFunction controller="usermanagement" action="addUserResourceFavourite"  params="'resId='+resId"/>
    }


    function removeFavourite(resId){
        tempResId=resId;
        $('.unset#res'+tempResId).hide().removeClass('heart');
        $('.set#res'+tempResId).show().addClass('heart');
        <g:remoteFunction controller="usermanagement" action="removeUserResourceFavourite" params="'resId='+resId"/>
    }
    function backToFlashcard() {
        window.history.back();
        window.location=document.referrer;
    }
    var deleteSetId;
    function deleteFlashCard(resId) {
        //alert(keyValueId);
        $('#deleteSetModal').modal('show');
        deleteSetId=resId;

    }
    function deleteSetConfirm() {
        var folderId=${params.folderId};
        $("#flash" + deleteSetId).remove();
        <g:remoteFunction controller="resources" action="deleteFlashCards" params="'resId='+deleteSetId+'&folderId='+folderId" onSuccess="deleteSetSuccess()"></g:remoteFunction>
    }

    function cancelSetDelete() {
        $('#deleteSetModal').modal('hide');
    }
    function deleteSetSuccess() {
        $('#deleteSetModal').modal('hide');
    }
    function openSolution() {
        $('#addWeburl').modal('show');
    }
    function closeAddLink(){
        $('#addWeburl').modal('hide');

    }
    if($(window).width() <= 768){
        $('.mobile-footer-resource').removeClass('d-none').addClass('d-flex');
    }

    $(".materialsAddDropdown").on("show.bs.dropdown", function(){
        $("#overlay-bg").attr('style','opacity:1;visibility:visible;');
        $("#dropdownMenuButton span").text('Close');
        $("#dropdownMenuButton").find('i').addClass('rotated');
        $('.resource-label').attr('style','position:relative;z-index:999;color:white;');
    });
    $(".materialsAddDropdown").on("hide.bs.dropdown", function(){
        $("#overlay-bg").attr('style','opacity:0;visibility:hidden;');
        $("#dropdownMenuButton span").text('Add');
        $("#dropdownMenuButton").find('i').removeClass('rotated');
        $('.resource-label').attr('style','');
    });
    function addNotesFromMobile(){
        <%if(("android".equals(session["appType"]))){%>
        JSInterface.addNotes();
        <%} else{%>
        webkit.messageHandlers.addNotes.postMessage('');
        <%}%>
    }
    function callAppLoader() {
        <%if("android".equals(session["appType"])){%>
        JSInterface.flashCardLoaderEvent();
        <%}%>
        <%if("ios".equals(session["appType"])){%>
        webkit.messageHandlers.flashCardLoaderEvent.postMessage('');
        <%}%>
    }
</script>
