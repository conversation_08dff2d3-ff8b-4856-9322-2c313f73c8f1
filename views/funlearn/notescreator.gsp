<g:render template="navheader"></g:render>
<asset:stylesheet href="material-input.css"/>
<div>
    <div class="container">
        <div class="row">
            <div id="left-content" class="col-md-2"></div>
            <div id="static-content" class="col-md-7 main maincontent">
            <div class="row">
                <div class="col-md-12">
                    <h4>${params.resourceType}</h4><br>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addnotes" id="addnotes" action="addNotes" method="post">
                        <input type="hidden" name="resourceType">
                        <input type="hidden" name="topicId">
                        <input type="hidden" name="mode">
                        <input type="hidden" name="resourceDtlId">
                        <input type="hidden" name="notesId">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group resourceName float-label-control">
                                <label for="resourceName">Notes Name</label>
                                <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Notes Name" value="" maxlength="255">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                 <textarea  rows="20" class="form-control" id="notes" name="notes" placeholder="Notes"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="alert alert-warning col-sm-12" id="alertbox" style="display: none">
                            Please complete required fields marked in red.
                        </div>
                        </div>
                        <div class="row">
                        <div class="form-group">
                            <div class=" col-sm-12 text-center">
                                <button type="button" onclick="javascript:formSubmit()" class="btn btn-primary"><%="${params.mode}"=="create"?"Add":"Update"%></button>
                            </div>
                        </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
       </div>
    </div>
    <g:render template="footer"></g:render>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<asset:javascript src="tinymce.min.js"/>
<script>
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var topicId = "${params.topicId}";
    var resourceDtlId,notesId;
    
    tinymce.init({
        selector: '#notes',
        plugins : "table,paste,charmap,textcolor,table",
        menubar: false,
        toolbar1: " bold italic underline strikethrough  | subscript superscript removeformat charmap forecolor backcolor  fontsizeselect table"
    });
<% 
    if("edit".equals(params.mode)){
        notes.each{ note ->
%>
    document.addnotes.resourceName.value  = "${resourceDtl.resourceName}";
    document.addnotes.notes.value = htmlDecode( "${note.textBlob.replaceAll("(\\r\\n)", "<br>")}");
    notesId = "${note.id}";
<%      } %>
        resourceDtlId = "${resourceDtl.id}";
        if(tinyMCE.get('notes')!=null) tinyMCE.get('notes').setContent(document.addnotes.notes.value);   
<%  } %>
        
    function formSubmit() {
        if(validate()) {
            document.addnotes.mode.value=mode;
            document.addnotes.resourceType.value=resourceType;
            document.addnotes.topicId.value=topicId;
            document.addnotes.resourceDtlId.value=resourceDtlId;
            document.addnotes.notesId.value=notesId;
            document.addnotes.submit();
        }
    }
    
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
    
    var flds = new Array (
        'resourceName',
        'notes'
    );    
    
    function validate(){
        var allFilled=true;
        $('.alert').hide();
        tinyMCE.triggerSave();

        for (i=0; i<flds.length; i++) {
            if(!$("#"+flds[i]).val()) {
                $("#"+flds[i]).addClass('has-error');
                $("#"+flds[i]).closest('.form-group').addClass('has-error');
                allFilled = false;
            } else {
                $("#"+flds[i]).removeClass('has-error');
                $("#"+flds[i]).closest('.form-group').removeClass('has-error');
            }
        }
        
        if(!allFilled){
            $('.alert').show();
        }
    
        return allFilled;
    }
</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
</body>
</html>