
<g:render template="navheader"></g:render>
<!--<asset:stylesheet src="timeline.css"/>-->
<div id="fb-root"></div>

<!--<div class="top-container">-->
    <div class="container-fluid profilepage">
        <!--<h3 id="updatedHeader">Profile</h3>-->
        <div class='row'>
            <!--<div class='col-md-12'>-->
                <!--<div class="row">--> 


                            <div class="col-md-10 col-md-offset-1" style="background-color: white">
                                <div class="thumbnail">
                                     <div class="row profile_picture">
                                        <div class="col-md-4 col-md-offset-1">
                                            <%if(user.profilepic!=null){%>
                                            <img src="/funlearn/showProfileImage?id=${user.id}&fileName=${user.profilepic}&type=user&imgType=thumbnail"  class="img-circle">
                                            <%}else{%>
                                            <i class="fa fa-user fa-5x"></i>
                                            <% }%>
                                        </div>

                                        </div>

                                    </div>
                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <h2>${user.name}&emsp;<%if(allowEdit){%><button type="button" class="btn btn-default" onclick="javascript:getProfilePage();"><a href="javascript:getProfilePage();">Edit Profile</a></button><%}%></h2>
                                    </div></div>
        <%if(user.parent!=null || user.student!=null ||user.teacher!=null){%>
                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <p><%=("on".equals(""+user.parent)?"Parent":"")%> <%=("on".equals(""+user.teacher)?"Teacher":"")%> <%=("on".equals(""+user.student)?"Student":"")%></p>
                                    </div></div>
        <%}%> <%if(user.school!=null){%>
                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <p>${user.school}</p>
                                    </div></div>
        <%}%><%if(user.city!=null){%>
                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <p>${user.city}</p>
                                    </div></div>
        <%}%><%if(user.interests!=null && !"".equals(""+user.interests)){%>
                                    <div class="row"><div class="col-md-8 col-md-offset-2 greytext">
                                        <p><b>Interests</b></p>
                                    </div></div>

                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <p>${user.interests}</p>
                                    </div></div>
        <%}%><%if(user.introduction!=null && !"".equals(""+user.introduction)){%>
                                    <div class="row"><div class="col-md-8 col-md-offset-2 greytext">
                                        <p><b>Description</b></p>
                                    </div></div>
                                    <div class="row"><div class="col-md-8 col-md-offset-2">
                                        <p>${user.introduction}</p>
                                    </div></div>
<%}%>
<%if(user.webpage!=null){%>
        <div class="row"><div class="col-md-8 col-md-offset-2">
            <p> <a  href="http://${user.webpage}" target="_blank">${user.webpage}</a></p>
        </div></div><%}%>
                                    <div class="row"><div class="col-md-12">
                                        <div class="profileDetails">
                                            <div class="container infoProfile">
                                                <div class="row">
                                                <div class="col-md-4 pinfo">
                                                    <h4>Class</h4>
                                                    <p>${user.classStudying}</p>
                                                </div>
                                                <div class="col-md-4 pinfo">
                                                    <h4>Friends</h4>
                                                    <p>${noOfFriends}</p>
                                                </div>
                                                <div class="col-md-4 pinfo">
                                                    <h4>Groups</h4>
                                                    <p>${noOfGroups}</p>
                                                </div>
                                                <!--    <a href="${user.facebook}"><i class="fa fa-facebook"></i></a>
                                                    <a href="#"><i class="fa fa-google-plus"></i></a>
                                                    <a href="#"><i class="fa fa-instagram"></i></a>-->
                                                </div>

                                            </div>

                                        </div>
                                    </div></div>
        <br><br>




                                </div>
                            </div>
                 </div>
               <!--</div>-->
            <!--</div>-->





    <g:render template="footer"></g:render>
<!--</div>-->




<asset:javascript src="bootstrap.min.js"/>
<script>
    function getProfilePage(){
        window.open("/creation/editProfile","_self");

}
</script>
<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>





</body>
</html>