<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.css" integrity="sha384-Juol1FqnotbkyZUT5Z7gUPjQ9gzlwCENvUZTpQBAPxtusdwFLRy382PSDx5UUJ4/" crossorigin="anonymous">

<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons%7CMaterial+Icons+Outlined%7CMaterial+Icons+Two+Tone%7CMaterial+Icons+Round%7CMaterial+Icons+Sharp" async>
<% if("books".equals(session["entryController"])){%>
<asset:stylesheet href="wonderslate/ws_webmcq.css"/>
<style>
.back_to_top {
    display: none;
}
</style>
<%}%>

%{--<asset:stylesheet href="landingpage/bootstrap.min.css"/>--}%
<asset:stylesheet href="landingpage/iconfont/icofont.css"/>
<asset:stylesheet href="landingpage/webquiz.css"/>
<script>
    var defaultSiteName="${session['entryController']}";
    var resourceTitle = "${name}";
</script>
<style>
    body,html{
        overflow: hidden;
    }
.start-test .footer-test {
    height: 87px;
    box-shadow: none;
}
.quiz-related{
    display: none;
}
.backButton {
    cursor: pointer;
}
.mt-7{
    padding-top: 5rem;
}

#width_tmp_select{
    display: none;
}
.web-mcq .sub-header.section-header{
    height: auto !important;
}
.arihant .mt-fixed #resourceTitle{
    margin-top: 75px;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no+.que-no a:first-child{
    display:flex !important;
}
.starttest-wrapper .header{
    z-index: 9 !important;
}
.web-mcq .result-menu{
    top:115px !important;
}

.mcqChatBtns {
    gap: 0.5rem;
    margin-left: -20px;
    margin-right: -20px;
    background: #f6f6f6;
    padding: 10px;
    margin-bottom: -5px;
    border-radius: 5px;
}
.mcqChatBtns .exp {
    background: #8854fe;
    color: #fff;
    transition: all 0.4s ease;
}
.mcqChatBtns .exp img {
    width: 20px;
    margin-right: 6px;
}
.mcqChatBtns .exp:hover {
    background: #F79420 !important;
    color: #fff;
}
.mcqChatBtns .squ {
    background: #525152;
    transition: all 0.4s ease;
    color: #fff;
}
.mcqChatBtns .squ img {
    width: 20px;
    margin-right: 6px;
}
.mcqChatBtns .squ:hover {
    background: #F79420 !important;
    color: #fff;
}
@media (max-width: 768px) {
    .mcqChatBtns {
        bottom: -5px;
    }
}
.exp:hover .button-icon {
    filter: grayscale(100%) brightness(100) sepia(100%) hue-rotate(45deg) saturate(500%) contrast(1);
}
.squ:hover .button-icon2 {
    filter: grayscale(100%) brightness(100) sepia(100%) hue-rotate(45deg) saturate(500%) contrast(1);
}
.ibookgpt-section,
.footer-menus{
    display: none;
}
@media (min-width: 769px) {
    #quizQuestionSection .result-menu{
        top: 120px !important;
    }
}
#quizQuestionSection .mt-fixed #answer-block{
    height: 90vh;
    overflow: scroll;
}
.ib_chat{
    top: 190px;
}
</style>
<%if("21".equals(""+session["siteId"])){%>
<style>
.bg-wsTheme{
    background:#F79420 !important;
}
</style>
<%}%>
<%if("1".equals(""+session["siteId"]) || "3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"])){%>
<style type="text/css" media="print">
* { display: none; }
</style>
<%}%>
<% if("libwonder".equals(session["entryController"]) || "oswal".equals(session["entryController"])){%>
<style>
.libwonder footer {
    display: none;
}
.libwonder header.LibWonder  {
    position: fixed;
}
.libwonder #quizInformationSection,
.libwonder #learnInformationSection {
    margin-top: 97px;
}
.libwonder .web-mcq .sub-header {
    top: 100px;
    transition: all 0.2s linear;
}
.libwonder .web-mcq .result-menu {
    top: 5.8rem;
    transition: all 0.2s linear;
}
.libwonder .web-mcq .mt-fixed {
    margin-top: 140px !important;
    margin-bottom: 50px;
}
.libwonder .web-mcq .mt-fixed .que-side-menu {
    border-right: none;
}
.libwonder .web-mcq .mt-fixed form {
    border-left: 1px solid #ededed;
}
.libwonder .tab-wrappers {
    top: 200px;
}
.libwonder .result-menu > div {
    /*height: 86px;*/
}
.libwonder.custom-fix header.LibWonder {
    position: relative;
}
.libwonder.custom-fix #quizInformationSection,
.libwonder.custom-fix #learnInformationSection {
    margin-top: 0px;
}
.libwonder.custom-fix .web-mcq .sub-header {
    top: 0;
    z-index: 999;
}
.libwonder.custom-fix .web-mcq .web-position > div:first-child {
    top: 6rem;
}
.libwonder.custom-fix .web-mcq .mt-fixed {
    margin-top: 70px !important;
}
.libwonder.custom-fix .web-mcq .result-menu {
    top: 0;
    z-index: 999;
}
.libwonder .web-mcq .web-position > div:first-child {
    height: 70vh;
    padding-bottom: 0;
}
.libwonder.custom-fix .web-mcq .web-position > div:first-child {
    height: 80vh;
}
.libwonder .web-mcq .web-position #grid #collapseOne {
    padding-bottom: 0;
}
.libwonder .web-mcq .onclickScrollsList:last-child {
    padding-bottom: 0;
}
@media (max-width: 767px) {
    .libwonder #quizInformationSection,
    .libwonder #learnInformationSection {
        margin-top: 134px;
    }
    .libwonder .web-mcq .sub-header {
        top: 124px;
    }
    .libwonder .result-menu > div {
        height: 36px;
        justify-content: space-between !important;
    }
    .libwonder.custom-fix .result-menu > div {
        height: 50px;
    }
    .libwonder .result-menu > div i {
        display: block !important;
    }
    .libwonder .web-mcq .result-menu {
        top: 134px;
    }
}
@media (max-width: 768px) {
    .libwonder .web-mcq .mt-fixed {
        margin-top: 140px !important;
    }
    .libwonder.custom-fix .web-mcq .mt-fixed {
        margin-top: 50px !important;
    }
}
@media (max-width: 1024px) {
    .libwonder .web-mcq .web-position > div:first-child {
        height: 100%;
    }
}
@media (max-width: 480px) {
    .libwonder .web-mcq .web-position > div:first-child {
        width: 85%;
    }
}
</style>
<% } %>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="start-test" id="learnInformationSection" style="display: none">
    <div class="starttest-wrapper">
        <div class="header">
            <% if("arihant".equals(session["entryController"])){%>
                <p>${name}</p>
            <%}else{%>
                <p>Test Name</p>
            <%}%>
        </div>
        <div class="container str-test" style="padding: 8px!important;">
            <div class="card col-lg-8">
                <div class="language  d-flex justify-content-around align-items-center">
                    <div><p>Default Language</p></div>
                    <div>:</div>
                    <div>
                        <select id="learnLanguageSelect">
                        </select>
                    </div>
                </div>
            </div>
            <div class="card col-lg-8" style="padding: 2px;">
                <div class="language  d-flex justify-content-around align-items-center">
                    <a href="javascript:backPressed();" class="btn btn-starts">Back</a>
                    <%if("1".equals(""+session["siteId"])){%><a href="javascript:practiseWithFlashCards()" class="btn btn-starts">Practice with Flashcards</a><%}%>
                    <a href="javascript:startLearn()" class="btn btn-starts">Study</a>
                </div>
            </div>

        </div>
        <div class="container">

        </div>
        <div class="footer-test">

        </div>
    </div>
</section>
<section class="start-test" id="quizInformationSection" style="display: none">
    <div class="starttest-wrapper">
        <div class="header">
            <p>${name}</p>
        </div>
        <div class="container str-test" style="padding: 8px!important;">
            <div class="card col-lg-8">
                <div class="language  d-flex justify-content-around align-items-center">
                    <div><p>Default Language</p></div>
                    <div>:</div>
                    <div>
                        <select id="languageSelect">
                        </select>
                    </div>
                </div>

            </div>

            <div class="card col-lg-8 mt-4">
                <div class="patterns">
                    <h4>Exam Pattern</h4>
                    <div class="d-flex justify-content-around">

                        <p class="noque">No. of Questions</p>
                        <p>:</p>
                        <p class="values" id="firstPageNoOfQuestions">100</p>
                    </div>
                    <div class="d-flex justify-content-around">
                        <p class="timers">Time </p>
                        <p>:</p>
                        <p class="values" id="firstPageTime">60 min</p>
                    </div>

                </div>

            </div>
            <div class="card col-lg-8 mt-4" style="display: none" id="sectionPattern">
                <div class="section patterns" >

                </div>
            </div>
            <div class="card col-lg-8 mt-4" style="display: none">
                <div class="disclaimer">
                    <h4>Disclaimer</h4>
                    <p>Various institutes and associations across the country conduct English Olympiads & Competitions for Class 7 students. One more line will also come here to enjoy here to history of the w will also come here to enjoy will also come here to enjoy</p>
                </div>
            </div>
            <div class="card col-lg-8 p-0">

                <div class="language  d-flex justify-content-around align-items-center">
                    <a onclick="javascript:backPressed();" class="btn btn-default d-none d-lg-block" id="goBackButton">Back</a>
                    <%if("1".equals(""+session["siteId"])&&(!"bookTest".equals(params.mode))){%><a href="javascript:practiseWithFlashCards()" class="btn btn-starts">Practice with Flashcards</a><%}%>
                    <a href="javascript:startTest()" class="btn btn-starts">Take test</a>
                </div>
            </div>
            <button onclick="backPressed()" class="btn btn-default d-flex align-items-center d-lg-none" style="margin: 10px auto !important;">Back</button>
            <br><br>
            <%if("1".equals(""+session["siteId"])){%>
            <div class="sharethis-inline-share-buttons" id="sharethisid"></div>
            <%}%>

        </div>

    </div>
    <div class="footer-test">
        <div class="container">

        </div>
    </div>

</section>
<section class="web-mcq addwebmcq" style="display: none;" id="quizQuestionSection">
    <div class="overlay-container"></div>
    <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
    <div class="sub-header" style="top:0px;padding-top: 5px;">
        <%}else{%>
        <div class="sub-header">
            <%}%>
            <button class="close-menu" onclick="javascript:closesideNav() ">
                <span></span>
            </button>
            <div class="submit">
                <div class="container">
                    <div class="container d-flex justify-content-between align-items-center svg-timer">
                        <div class="tim-wrapper">
                            <div>
                                <div id="sectionSelectionDiv" style="display: none">
                                    <select id="sectionSelection" onchange="sectionChanged()">
                                        <option value="1">General Aptitude</option>
                                        <option value="2">Section2</option>
                                        <option value="3">Section3</option>
                                    </select>
                                    <select id="width_tmp_select">
                                        <option id="width_tmp_option"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="normal-time">
                                    <svg id="time-progress" width="28" viewBox="0 0 220 220" xmlns="http://www.w3.org/2000/svg">
                                        <g  transform="translate(110,110)">
                                            <circle r="100" class="e-c-progress"/>
                                            <g id="e-pointer">

                                            </g>
                                        </g>
                                        <g  transform="translate(110,110)">
                                            <circle r="100" class="e-c-base"/>
                                        </g>

                                    </svg>
                                    <button class="play" id="pause" data-setter=""></button>
                                </div>
                                <div class="sectiontime-wrapper">
                                    <p class="timeLeft tot-time-text">Total Time Left</p>
                                    <span class="timer display-remain-time">00.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="read-backarrow"><i class="material-icons-round" onclick="goBack();">keyboard_backspace</i></div>

                        <div class="text-right">
                            <div class="submitWrapper">
                                <button type="Submit" class="btn-submit btn bg-wsTheme" onclick="javascript:submitTest()">Submit</button>
                            </div>
                            <div class="total-time-wrapper text-right" style="display:none;">
                                <p class="timeLeft">Total Time Left</p>
                                <p class="total-test-time totalTimeLeft">00.00</p>
                            </div>
                        </div>
                    </div>
                    <div class="options container">

                        <p><span class="answered">Questions</span>:<span class="totalquestion" id="totalQuestions"></span></p>

                        <div class="menu-wrapper" id="firstScreenMenuWrapper">
                            <a class="language" onclick="javascript:changeLanguage();" id="changeLanguage" style="display: none"></a>
                            <button class="menu" onclick="javascript:opensideNav()"></button>
                        </div>


                    </div>
                </div>

            </div>


        </div>
        <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
        <div class="result-menu app-result-menu">
            <%} else{%>
            <div class="result-menu">
                <%}%>


                <div class="container d-flex justify-content-center justify-content-between align-items-center">
                    <i class="material-icons-round backButton" onclick="javascript:backPressed();">
                        keyboard_backspace
                    </i>
                    <h2>Result-Exercise</h2>

                    <p><button class="language" onclick="javascript:changeLanguage();" id="changeLanguage1" style="display: none"></button></p>
                </div>
            </div>
            <div class="mt-fixed">
                <div class="container">
                    <div class="row">

                        <div class="que-side-menu col-lg-3">
                            <button class="close-menu" onclick="javascript:closesideNav() ">
                                <span></span>
                            </button>
                            <div class="web-position">
                                <div class="tab-wrappers">
                                    <div class="container">
                                        <div class="d-flex justify-content-between indicator">
                                            <div class="answered">
                                                <p><span class="circle"></span>Answered</p>
                                            </div>
                                            <div class="unanswered">
                                                <p><span class="circle"></span>Unanswered</p>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between indicator">
                                            <div class="review">
                                                <p><span class="circle"></span>Marked for review</p>
                                            </div>
                                            %{--<div class="notseen">--}%
                                            %{--<p><span class="circle"></span>Not yet seen</p>--}%
                                            %{--</div>--}%
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <ul class="nav nav-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-toggle="tab" href="#grid">GRID</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-toggle="tab" href="#list">LIST</a>
                                            </li>
                                        </ul>
                                        <div class="tab-content">

                                            <div id="grid" class="container tab-pane active grid"><br>
                                            </div>

                                            <div id="list" class="container list tab-pane fade"><br></div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form name="quiz" class="col-md-12 col-lg-9" method="post">
                            <% if("arihant".equals(session["entryController"])){%>
                            <h3 id="resourceTitle">${name}</h3>
                            <%}%>
                            <div id="question-block"></div>

                        </form>
                    </div>
                </div>
                <div class="container">
                    <% if("eutkarsh".equals(session["entryController"])){%>
                    <div id="learn-link" class="alert alert-danger mt-4 text-center" role="alert" style="display: none;">
                        Video explanation is available only in the App.
                    </div>
                    <%}%>
                    <div class="row">
                        <div id="answer-block" class="col-12 col-lg-7" style="display: none"></div>
                    </div>


                    <div class='ans-tab' style='' id="scrolltoTab">
                        <ul class='nav nav-tabs'>
                            <li class='nav-item'><a class='nav-link alls' data-toggle='tab' href='#alls'><span> &#8226;</span>All</a></li>
                            <li class='nav-item'><a class='nav-link correct' data-toggle='tab' href='#correct'><span> &#8226;</span>Correct</a></li>
                            <li class='nav-item'><a class='nav-link incorrect' data-toggle='tab' href='#incorrect'><span> &#8226;</span>Incorrect</a></li>
                            <li class='nav-item'><a class='nav-link skipped' data-toggle='tab' href='#skipped'><span> &#8226;</span>Skipped</a></li>
                        </ul>
                    </div>
                    <div class='tab-content'>
                        <% if("eutkarsh".equals(session["entryController"])){%>
                        <div id="explainLinks" class="alert alert-danger mt-2 text-center" role="alert" style="display: none;">
                            Video explanation is available only in the App.
                        </div>
                        <%}%>
                        <div id='videoContent'>
                            <div class="modal fade" id="videoModal">
                                <div class="modal-dialog  modal-dialog-centered">
                                    <div class="modal-content">

                                        <div class="modal-header">

                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>

                                        <div class="modal-body">
                                            <div class='video-wrapper' id='videoUpdate'>
                                                <iframe src='' frameborder='0' allow='accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture' allowfullscreen>
                                                </iframe>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='tab-pane' id='alls'>

                        </div>
                        <div class='tab-pane' id='correct'>

                        </div>
                        <div class='tab-pane' id='incorrect'>

                        </div>
                        <div class='tab-pane' id='skipped'>

                        </div>

                    </div>
                </div>
                <div class="modal fade" id="continue-test" data-keyboard="false" data-backdrop="static">
                    <div class="modal-dialog modal-dialog-centered modal-sm">
                        <div class="modal-content">

                            <!-- Modal Header -->
                            <div class="modal-header">

                            </div>

                            <!-- Modal body -->
                            <div class="modal-body" id="temp-mod">
                                <div class="content-wrapper">
                                    <p class="timeup">Time's Up!</p>
                                    <h4 id="completed-subject">The time to complete this section has ended.</h4>
                                    <p class="comingup">COMING UP - <span id="comingup-subject"></span></p>
                                </div>
                            </div>

                            <!-- Modal footer -->
                            <div class="modal-footer">
                                <button onclick="javascript: nextSection();" data-dismiss="modal" class="btn btn-continue">Continue</button>
                                %{--<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>--}%
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal fade" id="submit-test" data-keyboard="false" data-backdrop="static">
                    <div class="modal-dialog modal-dialog-centered modal-sm">
                        <div class="modal-content">

                            <!-- Modal Header -->
                            <div class="modal-header">
                                <h1 class="mt-2">Submit test?</h1>
                            </div>

                            <!-- Modal body -->


                            <div class="modal-body text-center" id="submitTest">
                                <p class="summary">Summary</p>
                                <span id="noOfQuestions">(Total Questions:100)</span>
                                <div class="d-flex justify-content-center">
                                    <div>
                                        <div class="d-flex align-items-center mt-4">
                                            <div class="circle answered" id="answered"><span>38</span></div>
                                            <p>Answered</p>
                                        </div>
                                        <div class="d-flex align-items-center mt-3">
                                            <div class="circle unanswered" id="unanswered"><span>38</span></div>
                                            <p>Unanswered</p>
                                        </div>
                                        <div class="d-flex align-items-center mt-3">
                                            <div class="circle review" id="review"><span>38</span></div>
                                            <p>Marked for Review</p>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Modal footer -->
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary Resume" data-dismiss="modal">Resume</button>
                                <button onclick="javascript:submitForm();" data-dismiss="modal" class="btn submit">SUBMIT</button>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal fade" id="force-submit-test" data-keyboard="false" data-backdrop="static">
                    <div class="modal-dialog modal-dialog-centered modal-sm">
                        <div class="modal-content">

                            <!-- Modal Header -->
                            <div class="modal-header">

                            </div>

                            <!-- Modal body -->
                            <div class="modal-body" id="submitTest">
                                <h1 class="submit">Submit ?</h1>
                                <p>Your test will be submitted now. </p>
                            </div>

                            <!-- Modal footer -->
                            <div class="modal-footer">
                                <button onclick="javascript:submitForm();" data-dismiss="modal" class="btn submit">SUBMIT</button>

                            </div>

                        </div>
                    </div>
                </div>
                <div class='modal fade' id='report-que'>
                    <div class='modal-dialog modal-dialog-centered modal-sm'>
                        <div class='modal-content'>
                            <div class='modal-header'>
                                <h4 class='modal-title'>Report the question <i class='material-icons-round'>error</i> </h4>
                                <button type='button' class='close' data-dismiss='modal'>&times;</button>
                            </div>
                            <div class='modal-body'>
                                <label class='containers'>Spelling Mistake
                                    <input type='checkbox' checked='checked' id="spellingMistake">
                                    <span class='checkmark'></span>
                                </label>
                                <label class='containers'>Direction not given
                                    <input type='checkbox' id="directionNotGiven">
                                    <span class='checkmark'></span>
                                </label>
                                <label class='containers'>Graph / Image not visible
                                    <input type='checkbox' id="imageNotVisible">
                                    <span class='checkmark'></span>
                                </label>
                                <label class='containers' >Incomplete question
                                    <input type='checkbox' id="incompleQuestion">
                                    <span class='checkmark'></span>
                                </label>
                                <label class='containers'>Other Issues
                                    <input type='checkbox' id="otherIssues">
                                    <span class='checkmark'></span>
                                </label>
                                <div class='letusknow'>
                                    <textarea placeholder='Let us know' id="moreInformation"></textarea>
                                </div>
                            </div>
                            <div class='modal-footer'>
                                <button type='button' class='btn btn-submit' onclick="openIssueSubmitWeb()">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
</section>
<g:render template="/prompt/chatModule"></g:render>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.js" integrity="sha384-97gW6UIJxnlKemYavrqDHSX3SiygeOwIZhwyOKRfSaf0JWKRVj9hLASHgFTzT+0O" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        renderMathInElement(document.body, {
            // customised options
            // • auto-render specific keys, e.g.:
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            // • rendering keys, e.g.:
            throwOnError : false
        });
    });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>

<script>
    var appInApp="${session["appInApp"]}";
    var loggedInUser = false;

    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>

    var myvalue = location.search.split('currentAffairs=')[1]?location.search.split('currentAffairs=')[1]:"";

    function continueTest() {
        $('#continue-test').modal('show');
    }
    function submitTest() {
        answeredUnanswered();
        $('#submit-test').modal('show');
    }
    function forceSubmitTest() {
        $('#force-submit-test').modal('show');
    }
    let ibookgpt
</script>
<script>
    // $(window).on('load',function () {
    //     $('.viewsolution').on('click',function () {
    //         $('body').scrollTo('body');
    //
    //     });
    //
    // });
    function backPressed(){

        <%if("book".equals(params.fromMode)||("bookTest".equals(params.mode))){%>
            <%if("android".equals(session["appType"])){%>
            JSInterface.showNativeHeader();
            <%}else if("ios".equals(session["appType"])) {%>
            webkit.messageHandlers.showNativeHeader.postMessage('');
            <%}else if("books".equals(session["entryController"]) && "bookTest".equals(params.mode)){%>
                //window.location.href = "/test-generator";
                window.history.back();
            <%}else{%>
            window.history.back();
            <%}%>
        <%}else{%>
            window.history.back();
        <%}%>

        if ('${params.quizMode}'=='learn'){
            window.close();
        }
    }
</script>
<asset:javascript src="webmcq.js"/>
<asset:javascript src="timer.js"/>
<asset:javascript src="totalTimer.js"/>
<asset:javascript src="moment.min.js"/>

<script>

    var data1;
    var localLanguage;
    var resId='${params.resId}';

    function startTest(){
        $('.loading-icon').removeClass('hidden');
        $("#quizInformationSection").hide();
        var useOtherLanguage=false;
        if(document.getElementById("languageSelect").selectedIndex==1) useOtherLanguage=true;
        createMCQ(data1.results,data1.isPassage,data1.passage,data1.chaptersList,"",data1.examMst,data1.examDtl,data1.testSeries,useOtherLanguage,"web","Practice");
        renderMathInElement(document.body);
        $("html, body").animate({ scrollTop: 0 }, "fast");
        $('.loading-icon').addClass('hidden');
        $("#quizQuestionSection").show();
    }
    function getQuestions(){
        $('.loading-icon').removeClass('hidden');
        <%if("bookTest".equals(params.mode)&&(params.bookId!=null||params.chaptersList!=null)){%>
        <g:remoteFunction controller="funlearn" action="quizQuestions" params="'createtest=true&bookTest=true&bookId='+${params.bookId}+'&chaptersList=${chaptersList}&noOfQuestions=${params.noOfQuestions}&resType=Multiple Choice Questions'" onSuccess = "initializeQuizData(data);"/>
        <%}else{%>
        <g:remoteFunction controller="funlearn" action="quizQuestions" params="'quizId='+${params.quizId}+'&resId='+${params.resId}" onSuccess = "initializeQuizData(data);"/>

        <sec:ifNotLoggedIn>
        <g:remoteFunction controller="log" action="updateView" params="'id='+${params.resId}+'&source=web&fromTab=All&viewedFrom=All'" />
        </sec:ifNotLoggedIn>
        <%}%>
    }

    function initializeQuizData(data){
        var systemDate = new Date();
        var endDate=moment(data.testEndDateWeb).utc(false);
         totalTestTime = Math.round((endDate - systemDate.getTime()) / 1000);
        data1=data;
        var select = document.getElementById("languageSelect");
        select.options.length = 0;
        if(data.language1!=null&&!data.language1==""){
            var el = document.createElement("option");
            el.textContent = data.language1;
            el.value =  data.language1;
            select.appendChild(el);
        }else{
            el = document.createElement("option");
            el.textContent = "English";
            el.value =  "English";
            select.appendChild(el);
        }
        if(data.language2!=null&&!data.language2==""){
            var el = document.createElement("option");
            el.textContent = data.language2;
            el.value =  data.language2;
            select.appendChild(el);
            $("#changeLanguage").show();
            $("#changeLanguage1").attr("style", "display:block!important;margin-top:0");

        }
        if(myvalue) {
            localLanguage = localStorage.getItem("language");
            for (var i = 0; i < select.length; i++) {

                if (localLanguage == select[i].value) {
                    select.options[i].selected = true;
                }
            }
        }
        if(data.results.length!=0 || data.results.length!=null) {
            document.getElementById("firstPageNoOfQuestions").innerHTML = data.results.length;
        }
            <sec:ifNotLoggedIn>
        else{
            $('#loginOpen').modal('show');
        }
        </sec:ifNotLoggedIn>
        if(data.examMst!=null&&!data.examMst==""){
            document.getElementById("firstPageTime").innerHTML = data.examMst.totalTime;
        }
        if(data.examDtl!=null&&!data.examDtl==""&&data.examDtl.length>0){

            var sectionStr="<div class=\"section patterns\" >\n" +
                "                <h4>Section Pattern</h4>";
            var eDtl = data.examDtl;
            var totalTime = 0;
            for(s=0;s<eDtl.length;s++){
                var positiveMarks=(data.examMst.rightAnswerMarks!=null&&!data.examMst.rightAnswerMarks==null)?data.examMst.rightAnswerMarks:"-";
                var negativeMarks=(data.examMst.wrongAnswerMarks!=null&&!data.examMst.wrongAnswerMarks==null)?data.examMst.wrongAnswerMarks:"-";
                if(eDtl[s].rightAnswerMarks!=null&&!eDtl[s].rightAnswerMarks=="") positiveMarks=eDtl[s].rightAnswerMarks;
                if(eDtl[s].wrongAnswerMarks!=null&&!eDtl[s].wrongAnswerMarks=="") negativeMarks=eDtl[s].wrongAnswerMarks;
                sectionStr +="<div class=\"d-flex justify-content-around\">\n" +
                    "                    <p class=\"lang\">"+eDtl[s].subject+"</p>\n" +
                    "                    <p>:</p>\n" +
                    "                    <p><span class=\"positive\">"+positiveMarks+"<span>/<span class=\"negative\">-"+negativeMarks+"</span></p>\n" +
                    "                </div>";
                if(eDtl[s].totalTime!=null&&!eDtl[s].totalTime=="") totalTime +=eDtl[s].totalTime;
            }
            sectionStr +="</div>";
            document.getElementById("sectionPattern").innerHTML=sectionStr;
            if(totalTime!=0) document.getElementById("firstPageTime").innerHTML = totalTime;
            $("#sectionPattern").show();
        }
        $("#quizInformationSection").show();
        $('.loading-icon').addClass('hidden');
        if(data.testSeries=="true"||data.testEndDate!=null){
            if(data.testEndDate!=null&&!data.testEndDate==""){
                testEndDate = moment.utc(data.testEndDate).format("D MMM YYYY h:mm a");
            }
        }
        <%if("bookTest".equals(params.mode)&&(params.bookId!=null||params.chaptersList!=null)){%>
        startTest();
        <%}%>
    }


    function getQuestionAnswers(){
        $('.loading-icon').removeClass('hidden');
        $('.que-side-menu .tab').hide();
        if ('${params.dailyTest}' == 'true'){
            //API CALL FOR DAILY TEST (DAILY TEST 4TH API)
            var dailytestID = '${params.dailyTestId}';
            var dateInput = '${params.dateInput}';
            <g:remoteFunction controller="prepjoy" action="getDailyTests" params="'dailyTestId='+dailytestID+'&dateInput='+dateInput+'&siteId='+${session['siteId']}" onSuccess="initializeQuizDataWithAnswers(data)" />
        }else if('${params.mode}' == 'daily') {
            console.log("yes1",'${params.mode}')
            <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'currentAffairsType=Main&resId='+${params.resId}+'&siteId='+${session['siteId']}" onSuccess="initializeQuizDataWithAnswers(data)" />
        }else if('${params.mode}' == 'weekly' || '${params.mode}' == 'monthly'){
            console.log("yes2",'${params.mode}')
            var nos = '${params.noOfQ}';
            var days = '${params.days}';
            var dateInput = '${params.dateInput}';
            <g:remoteFunction controller="prepjoy" action="getMultiDaysQuiz" params="'noOfQuestions='+nos+'&siteId='+${session['siteId']}+'&currentAffairsType=Main&noOfDays='+days+'&dateInput='+dateInput" onSuccess="initializeQuizDataWithAnswers(data)" />
        }else{
            <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+${params.quizId}+'&resId='+${params.resId}" onSuccess = "initializeQuizDataWithAnswers(data);"/>
        }
    }

    function getQuizResults(){
        $('.loading-icon').removeClass('hidden');
        $('.que-side-menu .tab').hide();
        <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+${params.quizId}+'&resId='+${params.resId}" onSuccess = "initializeQuizDataWithAnswers(data);"/>
    }

    function getAnswers(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="quizAnswers" params="'quizRecorderId='+${params.quizRecorderId}+'&resId='+${params.resId}" onSuccess = "initializeQuizAnswers(data);"/>
    }

    function getDetailedResults(){
        $('.loading-icon').removeClass('hidden');
        $('.que-side-menu .tab').hide();
        <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizRecorderId='+${params.quizRecorderId}+'&resId='+${params.resId}" onSuccess = "initializeQuizResults(data);"/>
    }

    function initializeQuizResults(data){
        $('.loading-icon').removeClass('hidden');
        $("#learnInformationSection").hide();
        var useOtherLanguage=false;
        //   if(document.getElementById("learnLanguageSelect").selectedIndex==1) useOtherLanguage=true;
        $("#quizQuestionSection").show();
        var userAnswersInput = JSON.parse(data.quizRecorder.userAnswers);
        var userScore = {};
        userScore.correctAnswers = parseInt(data.quizRecorder.correctAnswers);
        userScore.wrongAnswers=parseInt(data.quizRecorder.wrongAnswers);
        userScore.skipped=parseInt(data.quizRecorder.skipped);
        userScore.totalQuesions=data.results.length;
        userScore.marks = data.quizRecorder.score;
        setResultsMode(userAnswersInput,userScore,data.examMst,data.examDtl,data.testSeries);
        scoreAndShowAnswersMCQ(false,data.results,data.isPassage,data.passage,"web",useOtherLanguage);

        $('#answer-block').on('click', '.show-explanation-btn', function(e) {
            e.preventDefault();
            $(this).parents('.show-explanation').next('.correct-answer-explanation').slideToggle(100);
            $(this).html($(this).html() == 'Show Explanation' ? 'Hide Explanation' : 'Show Explanation');
        });
        $('.loading-icon').addClass('hidden');
    }

    function initializeQuizAnswers(data){
        qa = data.results;
        scoreAndShowAnswersMCQ(false);
        $('.loading-icon').addClass('hidden');
        // $('.que-wrappers').hide();

        //  $('.que-wrappers').hide();

    }

    function startLearn(){
        $('.loading-icon').removeClass('hidden');
        $("#learnInformationSection").hide();
        var useOtherLanguage=false;
        if(document.getElementById("learnLanguageSelect").selectedIndex==1) useOtherLanguage=true;
        $("#quizQuestionSection").show();

        if ('${params.mode}' == 'daily'){
            scoreAndShowAnswersMCQ(true,data1.results,data1.isPassage,data1.passage,"web",useOtherLanguage);
        }else{
            scoreAndShowAnswersMCQ(true,JSON.parse(data1.results),data1.isPassage,data1.passage,"web",useOtherLanguage);
        }

        $('#answer-block').on('click', '.show-explanation-btn', function(e) {
            e.preventDefault();
            $(this).parents('.show-explanation').next('.correct-answer-explanation').slideToggle(100);
            $(this).html($(this).html() == 'Show Explanation' ? 'Hide Explanation' : 'Show Explanation');
        });
        $('.loading-icon').addClass('hidden');
    }

    function practiseWithFlashCards(){
        window.location.href="/resources/displayFlashCards?resId=${params.resId}&name=${name}&fromQuiz=true";
    }
    function initializeQuizDataWithAnswers(data){
        console.log(data)
        $('.loading-icon').addClass('hidden');
        $("#learnInformationSection").show();
        data1=data;
        var select = document.getElementById("learnLanguageSelect");
        select.options.length = 0;
        if(data.language1!=null&&!data.language1==""){
            var el = document.createElement("option");
            el.textContent = data.language1;
            el.value =  data.language1;
            select.appendChild(el);
        }else{
            el = document.createElement("option");
            el.textContent = "English";
            el.value =  "English";
            select.appendChild(el);
        }
        if(data.language2!=null&&!data.language2==""){
            var el = document.createElement("option");
            el.textContent = data.language2;
            el.value =  data.language2;
            select.appendChild(el);
            $("#changeLanguage").show();
            $("#changeLanguage1").attr("style", "display:block!important;margin-top:0");

        }else{
            startLearn();
        }

    }


    <%if("learn".equals(params.quizMode)){%>
    getQuestionAnswers();
    <%}else if("results".equals(params.quizMode)){%>
    getDetailedResults();
    <%}else{%>
    getQuestions();
    <%}%>


    function goBack(){
        history.back();
    }
    function  updateWithQuizAnswers(userAnswers,score,quizgenerated){
        <%if(!"bookTest".equals(params.mode)){%>
        <sec:ifLoggedIn>
        var params;
        if("true"==quizgenerated) params = "noOfQuestions="+userAnswers.length+"&timetaken="+(endTime-startTime)+"&testgenid="+testGenId;
        else params = "noOfQuestions="+userAnswers.length+"&quizid=${params.resId}&timetaken="+(endTime-startTime);
        for (i = 0; i < userAnswers.length; i++) {
            params += "&id"+i+"="+userAnswers[i]['id'];
            params += "&option1"+i+"="+userAnswers[i].ans1;
            params += "&option2"+i+"="+userAnswers[i].ans2;
            params += "&option3"+i+"="+userAnswers[i].ans3;
            params += "&option4"+i+"="+userAnswers[i].ans4;
            params += "&option5"+i+"="+userAnswers[i].ans5;

            if(userAnswers[i].skipped=="true")  params += "&correctAnswer"+i+"=skipped";
            else {
                params += "&correctAnswer"+i+"="+userAnswers[i].correctAnswer;
            }
        }

        params += "&correctAnswers="+score.correctAnswers;
        params += "&wrongAnswers="+score.wrongAnswers;
        params += "&skipped="+score.skipped;
        params += "&score="+score.marks;
        totalQuestions = userAnswers.length;
        noOfRightAnswers = score.correctAnswers;
        params +="&userAnswers="+JSON.stringify(userAnswers);

        <g:remoteFunction controller="funlearn" action="updateWithQuizAnswers" params="params"></g:remoteFunction>

        </sec:ifLoggedIn>
        <%}%>
    }
    var shareThis = document.getElementById("sharethisid");
    shareThis.setAttribute("data-url","https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href ));


    function openIssueSubmitWeb(){
        $('#report-que').modal('hide');
        var issuesSelected="";
        if(document.getElementById("spellingMistake").checked){
            issuesSelected="Spelling mistake";
        }
        if(document.getElementById("directionNotGiven").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Directions not given";
        }
        if(document.getElementById("imageNotVisible").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Image not visible";
        }
        if(document.getElementById("incompleQuestion").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Incomplete questions";
        }
        if(document.getElementById("otherIssues").checked){
            issuesSelected+=(issuesSelected==""?"":",")+"Other issues";
        }
        var moreInformation = document.getElementById("moreInformation").value;
        <g:remoteFunction controller="log" action="addQuizIssue" params="'id='+qa[issueIndex].id+'&issuesList='+issuesSelected+'&issue='+moreInformation" />
    }

    <%if(!"bookTest".equals(params.mode)){%>
    <sec:ifLoggedIn>
    updateUserView("${params.resId}","${params.quizMode}_${params.fromTab}","${params.viewedFrom}");
    </sec:ifLoggedIn>
    <sec:ifNotLoggedIn>
    updateView("${params.resId}","${params.quizMode}_${params.fromTab}","${params.viewedFrom}");
    </sec:ifNotLoggedIn>
    <%}%>
    function updateView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }

    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    $(document).ready(function() {
        $('#answer-block').bind('copy', function(e) {
            e.preventDefault();
            console.log("funny people -1");
        });
    });
    document.getElementById('answer-block').ondragstart = function () {
        return false; };
    $(document).ready(function() {
        $('#question-block').bind('copy', function(e) {
            e.preventDefault();
            console.log("funny people 0");
        });
    });
    document.getElementById('question-block').ondragstart = function () {
        return false; };


    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }

</script>
<script>
    $(document).ready(function() {
       <% if("arihant".equals(session["entryController"]) && session["userdetails"]==null && !"".equals(params.navMode) && params.navMode!=null){%>
        <% def newCookie = new javax.servlet.http.Cookie( "siteName", "${session["entryController"]}");
            newCookie.path = "/"
            response.addCookie newCookie;
            %>
        localStorage.setItem('quizlink','true');
       $('#signup').modal('show');
        operation="signup";
        $('#signup button.close, #loginOpen button.close, #forgotPasswordmodal button.close').hide();
        $('#goBackButton').attr('style','display:none !important');
        $('#signupMessage, #loginMessage').text("Please sign-up/login to attempt mock tests.").css('color','black');
        <%}else{ %>
        $('#sectionSelection').change(function(){
            $("#width_tmp_option").html($('#sectionSelection option:selected').text());
            $(this).width($("#width_tmp_select").width() + 20);
        });
        <%if("android".equals(session["appType"])){%>
        JSInterface.hideNativeHeader();
        <%}else if("ios".equals(session["appType"])) {%>
        webkit.messageHandlers.hideNativeHeader.postMessage('');
        <%}%>
       <%}%>
    });

    <%if("1".equals(""+session["siteId"]) || "3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"]) || "71".equals(""+session["siteId"])){%>
    if ('matchMedia' in window) {
        // Chrome, Firefox, and IE 10 support mediaMatch listeners
        window.matchMedia('print').addListener(function(media) {
            console.log("pringgg",media);
            if (media.matches) {
                beforeBookPrint();
            } else {
                // Fires immediately, so wait for the first mouse movement
                $(document).one('mouseover', afterBookPrint);
            }
        });
    } else {
        // IE and Firefox fire before/after events
        $(window).on('beforeprint', beforeBookPrint);
        $(window).on('afterprint', afterBookPrint);
    }
    function beforeBookPrint() {
        $("html,body").hide();
    }
    function afterBookPrint() {
        $("html,body").show();
    }
    <%}%>

    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 'c') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
    document.addEventListener('contextmenu', function(event) {
        event.preventDefault();
        return false;
    }, false);
    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
</script>

</body>
</html>
