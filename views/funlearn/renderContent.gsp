<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script type="text/javascript" async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML">
    </script>
    <title></title>

    <style>
        .questionWrapper,
        .question,
        .option{
            display: flex;
        }
        .question p,
        .question h4,
        .questionNo p,
        .option p,
         strong p,
        .answer_explanation p{
            margin: 0;
        }
        .question,.optionsList{
            margin-bottom: 15px;
        }
        .correctAnswer{
            margin-bottom: 5px;
        }
        .question,
        .questionNo{
            font-size: 17px;
            line-height: 1.6;
        }
        .option{
            margin-bottom: 10px;
            font-size: 18px;
        }
        .optionKey{
            margin-right: 5px !important;
        }
        .questionWrapper{
            margin-bottom: 16px;
        }
        .questionWrapper img{
            width: auto;
        }
        #content{width: calc(100% - 20%);margin: 0 auto;}
        @media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
            #content{width: calc(100% - 2%)}
            .questionWrapper img{
                width: 100%;
            }
        }
        #exportButton{
            display: flex;
            margin:0 auto;
        }
    </style>
</head>

<body>

<div id="exportContent">
    <div id="content">

    </div>
</div>

<script>
    const getQuiz = async ()=>{
        const params = '${params.quizId}&resId:${params.resId}';
        const response = await fetch('/funlearn/newQuizQA?siteId=1&quizId='+params);
        if (!response.ok){
            console.log('SOMETHING WENT WRONG');
            return;
        }
        const data = await response.json();
        const result = JSON.parse(data.results);
        let html = "";
        result.forEach((item,index)=>{

            html += '<div class="question_'+(index+1)+' questionWrapper" id="question-'+(index+1)+'">' +
                        '<div class="questionNo" style="margin-right: 5px;"><p>'+(index+1) + '.</p></div>'+
                        '<div class="questionSection">'+
                            '<div class="question math-content">' +
                                '<div><h4>' +item.ps+'</h4></div>' +
                            '</div>'+
                            '<div class="optionsList">'+
                                '<div class="option math-content"><p class="optionKey">a. </p><div >'+item.op1+'</div></div>'+
                                '<div class="option math-content"><p class="optionKey">b. </p><div >'+item.op2+'</div></div>'+
                                '<div class="option math-content"><p class="optionKey">c. </p><div >'+item.op3+'</div></div>'+
                                '<div class="option math-content"><p class="optionKey">d. </p><div >'+item.op4+'</div></div>'+
                            '</div>';

            if (item.ans1=="Yes"){
                html += '<div class="correctAnswer"><strong style="display:flex;align-items: center"><p style="margin-right: 5px;">Answer : a - </p>'+item.op1+'</strong></div>';
            }else if (item.ans2=="Yes"){
                html += '<div class="correctAnswer"><strong style="display:flex;align-items: center"><p style="margin-right: 5px;">Answer : b - </p>'+item.op2+'</strong></div>';
            }else if (item.ans3=="Yes"){
                html += '<div class="correctAnswer"><strong style="display:flex;align-items: center"><p style="margin-right: 5px;">Answer : c - </p>'+item.op3+'</strong></div>';
            }else if (item.ans4=="Yes"){
                html += '<div class="correctAnswer"><strong style="display:flex;align-items: center"><p style="margin-right: 5px;">Answer : b - </p>'+item.op4+'</strong></div>';
            }else if(item.ans5!=undefined && item.ans5!=null && item.ans5!=""){
                html += '<div class="correctAnswer"><strong style="display:flex;align-items: center"><p style="margin-right: 5px;">Answer : e - </p>'+item.op5+'</strong></div>';
            }

            if (item.answerDescription!="" && item.answerDescription!=null && item.answerDescription!=undefined){
                html +='<div class="answer_explanation">' +
                            '<p><strong>Answer Explanation :</strong></p>'+
                            '<div>'+item.answerDescription+'</div>'+
                        '</div>';
            }
            html +='</div>'+
        '</div>';
        });
        document.getElementById('content').innerHTML = html;
        var imgElement = document.querySelectorAll('p img');
        var baseURL = window.location.origin;
        imgElement.forEach(img=>{
            var currentSrc = img.getAttribute('src');
            if (currentSrc.includes("funlearn/downloadEpubImage?")){
                var newSrc = baseURL + currentSrc;
            }else{
                var newSrc = currentSrc;
            }

            img.setAttribute('src', newSrc);
        });

        // MathJax.Hub.Queue(['Typeset', MathJax.Hub, document.querySelectorAll('.math-content')]);
        MathJax.Hub.Queue(['Typeset', MathJax.Hub, document.getElementById('content')]);
    }

    getQuiz()

    document.getElementById("exportButton").addEventListener("click", function () {
        var divContent = document.getElementById("exportContent").innerHTML;
        const cssStyle =
            "<style>\n"+
            "span[aria-hidden='true']{display: none;}\n"+
            " .questionWrapper,\n" +
            " .question,\n" +
            " .option{\n" +
            "     display: flex;\n" +
            " }\n" +
            " .question p,\n" +
            " .question h4,\n" +
            " .questionNo p,\n" +
            " .option p,\n" +
            " strong p,\n" +
            " .answer_explanation p{\n" +
            "     margin: 0;\n" +
            " }\n" +
            " .question, .optionsList{\n" +
            "     margin-bottom: 15px;\n" +
            " }\n" +
            " .correctAnswer{\n" +
            "     margin-bottom: 5px;\n" +
            " }\n" +
            " .question, .questionNo{\n" +
            "     font-size: 17px;\n" +
            "     line-height: 1.6;\n" +
            " }\n" +
            " .option{\n" +
            "     margin-bottom: 10px;\n" +
            "     font-size: 18px;\n" +
            " }\n" +
            " .optionKey{\n" +
            "     margin-right: 5px !important;\n" +
            " }\n" +
            " .questionWrapper{\n" +
            "     margin-bottom: 16px;\n" +
            " }\n" +
            " .questionWrapper img{\n" +
            "     width: auto;\n" +
            " }\n" +
            " #content{\n" +
            "     width: calc(100% - 20%);\n" +
            "     margin: 0 auto;\n" +
            " }\n" +
            " @media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){\n" +
            "     #content{\n" +
            "         width: calc(100% - 2%)\n" +
            "     }\n" +
            "     .questionWrapper img{\n" +
            "         width: 100%;\n" +
            "     }\n" +
            " }\n" +
            " #exportButton{\n" +
            "     display: flex;\n" +
            "     margin:0 auto;\n" +
            " }\n"+
        "</style>";
        var metaTags = '<meta charset="UTF-8">'+
                       '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">'+
                       '<meta http-equiv="X-UA-Compatible" content="ie=edge">';
        var htmlContent = "<html>" +
            "<head>" +
            metaTags +
            cssStyle+
            "</head>" +
            "<body>" + divContent + "</body>" +
            "</html>";
        var blob = new Blob([htmlContent], { type: "document/pdf" });

        var url = URL.createObjectURL(blob);
        var a = document.createElement("a");
        a.href = url;
        a.download = "mcq_export.html";
        a.click();
        URL.revokeObjectURL(url);
    });
</script>

</body>
</html>