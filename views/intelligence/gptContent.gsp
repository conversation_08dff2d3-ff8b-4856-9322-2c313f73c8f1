<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/assets/bookgpt/externalgpt.css">
    <title>${pageTitle}</title>
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y0QVWCD9K" defer></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-6CJSRG577K');
    </script>
</head>
<body>
<div id="loader" class="loader-container">
    <div class="app-loader"></div>
</div>

<div class="container">
    <div class="menuOptions">
        <button class="edit-title-btn">
            <i class="fas fa-edit"></i> Edit Title
        </button>
        <button class="print-btn">
            <i class="fas fa-print"></i> Print
        </button>
    </div>
    <div class="gpt-contents" id="gpt-contents">
        <h2 class="text-center" id="page-title" style="text-align: center">${pageTitle}</h2>
        <div id="content"></div>
        <div id="questionPaperContainer" class="question-paper-container">
            <!-- Questions will be dynamically added here -->
        </div>
    </div>
    <button id="showAnswersBtn" class="show-answers-btn">Show Answers</button>
</div>

<div id="modal" class="modal">
    <div class="modal-content">
        <span id="closeModal" class="close-icon" style="display:none;">&times;</span>
        <div id="modalBody">
            <h5>Modal Title</h5>
            <p>This is a reusable modal. You can customize the content here.</p>
        </div>
    </div>
</div>
<script>
    const res_id = "${params.readingMaterialResId}"
    const res_type = "${params.promptType}";
    let quizId = null;
    let quiz_res_id = null

    const modal = document.getElementById('modal');
    const closeModal = document.getElementById('closeModal');
    async function getGptContent(){
        const loader = document.getElementById("loader");
        const content = document.getElementById("content");

        try {
            loader.style.display = "flex";
            content.innerHTML = "";
            let api_endpoint = "/prompt/getGPTsForResource?resId="+res_id+"&promptType="+res_type
            if(res_type=="giveTest"){
                api_endpoint = "/prompt/getTestInfo?readingMaterialResId="+res_id
            }
            var response = await fetch(api_endpoint);
            if(!response.ok){
                alert("Something  went wrong")
                return;
            }
            var result = await response.json();
            if(result.hasResource && res_type.toLowerCase() =="qna" || res_type.toLowerCase() == "qanda" || res_type.toLowerCase() == "qnas"){
                quizId = result.quizId
                quiz_res_id = result.resId
                quizInputHandler(result)
            }else if(res_type == "giveTest"){
                quizInputHandler(result)
            }else{
                if (result.hasResource && result.answer) {
                    renderMarkdown(result);
                } else {
                    document.getElementById("summary-content").innerHTML = "<p>No resource available.</p>";
                }
            }

        } catch (error) {
            console.error("Error fetching chapter details:", error);

        } finally {
            loader.style.display = "none";
        }
    }

    function renderMarkdown(content) {
        var container = document.getElementById("content");
        container.innerHTML = ""
        var parsedContent = content.answer;

        var cardHTML =
            "<div class='card'>" +
            "<div class='card-body'>" +
            "</div>" +
            "</div>";

        container.innerHTML = cardHTML;

        const ansDiv = document.querySelector('.card')
        ansDiv.innerHTML = parsedContent
        renderMathInElement(ansDiv, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        ansDiv.innerHTML = marked.parse(ansDiv.innerHTML)
    }

    function addContentToModalBody() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = ""
        const inputGroupDiv = document.createElement('div');
        inputGroupDiv.className = 'inputGroup';

        const label = document.createElement('label');
        label.setAttribute('for', 'noOfQuiz');
        label.style.width = '50%';
        label.textContent = 'Number of Questions';
        inputGroupDiv.appendChild(label);

        const input = document.createElement('input');
        input.type = 'number';
        input.id = 'noOfQuiz';
        input.placeholder = 'Enter number of questions';
        inputGroupDiv.appendChild(input);

        const errorMsg = document.createElement('p');
        errorMsg.id = "errorMsg"
        errorMsg.style.display = "none"
        errorMsg.style.color = "#FF6F61"

        modalBody.appendChild(inputGroupDiv);
        modalBody.appendChild(errorMsg);

        const buttonDiv = document.createElement('div');
        buttonDiv.className = 'createBtn';

        const button = document.createElement('button');
        button.id = 'createMcqsBtn';
        button.textContent = 'Create Questions';
        buttonDiv.appendChild(button);

        modalBody.appendChild(buttonDiv);
        button.onclick = function () {
            getDefaultQuestionAndAns();
        };
    }

    async function getDefaultQuestionAndAns(){
        const loader = document.getElementById("loader");
        const content = document.getElementById("content");
        const noOfQuiz = document.getElementById("noOfQuiz");
        const errorMsg = document.getElementById("errorMsg");
        try {
            loader.style.display = "flex";
            content.innerHTML = "";
            if(!noOfQuiz.value.trim() || noOfQuiz.value.trim()=="" || noOfQuiz.value.trim()<=0){
                errorMsg.innerHTML = "Please enter valid number."
                errorMsg.style.display = "block"
                setTimeout(()=>{
                    errorMsg.innerHTML = ""
                    errorMsg.style.display = "none"
                },5000)
                return;
            }
            var response = await fetch("/prompt/getQuestionAndAnswers?resId="+quiz_res_id+"&noOfQuestions="+noOfQuiz.value.trim());
            if(!response.ok){
                alert("Something  went wrong")
                return;
            }
            var result = await response.json();
            showQuestionAndAnswers(result.qaList)
        } catch (error) {
            console.error("Error fetching question and answers:", error);
        } finally {
            loader.style.display = "none";
            modal.style.display = 'none';
        }
    }

    function showQuestionAndAnswers(data){
        var questionList = document.getElementById('content');
        var wrapHTML = "<div class='cwrap'>";

        data.forEach(function(item, index) {
            wrapHTML +=
                "<div class='card'>" +
                    "<div class='card-header'>" +
                    "Q" + (index + 1) + ". " + item.question +
                    "</div>" +
                    "<div class='card-body'>" +
                        "<div class='answer'>" +
                            "Answer: " + item.answer +
                        "</div>" +
                        "<div class='difficulty'>" +
                            "Difficulty: " + item.difficultyLevel +
                        "</div>" +
                        "<button class='copy-btn'><i class='fa-solid fa-copy'></i></button>" +
                    "</div>" +
                "</div>";
        });

        wrapHTML += "</div>";
        questionList.innerHTML += wrapHTML;

        const tempEl = document.createElement("div")
        tempEl.innerHTML = wrapHTML

        renderMathInElement(tempEl, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        questionList.innerHTML = marked.parse(tempEl.innerHTML)
        document.querySelectorAll('.copy-btn').forEach(function(button) {
            button.addEventListener('click', function (event) {
                var card = event.target.closest('.card');
                var question = card.querySelector('.card-header').textContent.trim();
                var answer = card.querySelector('.answer').textContent.trim();
                var difficulty = card.querySelector('.difficulty').textContent.trim();

                var formattedText = question+"\n"+
                    answer+"\n"+
                    difficulty;

                navigator.clipboard.writeText(formattedText)
                    .then(function () {
                        alert('Content copied!');
                    })
                    .catch(function (err) {
                        console.error('Failed to copy text: ', err);
                    });
            });
        });
    }

    function addQPModalBody(result){
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = "";
        for (const category in result) {
            const categoryDiv = document.createElement('div');

            const subText = document.createElement("p")
            subText.style.textTransform = 'capitalize';
            subText.innerHTML = "<strong>"+category+"</strong>";
            categoryDiv.appendChild(subText)

            const levels = result[category].difficultylevels;
            for (const level in levels) {
                const inputGroupDiv = document.createElement('div');
                inputGroupDiv.className = 'inputGroup';
                inputGroupDiv.style.flexDirection = "row"
                inputGroupDiv.style.alignItems = "center"

                const label = document.createElement('label');
                label.setAttribute('for', level);
                label.style.width = '50%';
                label.style.textTransform = 'capitalize';
                label.textContent = level+" ("+levels[level]+")"// "Easy Questions", etc.
                inputGroupDiv.appendChild(label);

                const input = document.createElement('input');
                input.type = 'number';
                input.id = level;
                input.classList.add("questionPaperInput")
                if(category.toLowerCase().includes("mcq")){
                    input.classList.add("_mcq")
                }else if(category.toLowerCase().includes("qna")){
                    input.classList.add("_qna")
                }
                input.placeholder = 'Enter number of ' + level + ' questions'; // "Enter number of easy questions"
                input.value = 0;
                input.max = Number(levels[level])
                inputGroupDiv.appendChild(input);

                categoryDiv.appendChild(inputGroupDiv)
            }
            modalBody.appendChild(categoryDiv);
        }

        const errorMsg = document.createElement('p');
        errorMsg.id = "errorMsg";
        errorMsg.style.display = "none";
        errorMsg.style.color = "#FF6F61";
        modalBody.appendChild(errorMsg);

        const buttonDiv = document.createElement('div');
        buttonDiv.className = 'createBtn';

        const button = document.createElement('button');
        button.id = 'createMcqsBtn';
        button.textContent = 'Create Question Paper';
        buttonDiv.appendChild(button);

        modalBody.appendChild(buttonDiv);

        button.onclick = function () {
            createQuestionPaperHandler();
        };
    }

    async function createQuestionPaperHandler(){
        const questionPaperInputs = document.querySelectorAll('.questionPaperInput');
        const errorMsg = document.getElementById("errorMsg");
        const errObj = validateQPInput()
        if(errObj.error){
            errorMsg.innerHTML = errObj.message
            errorMsg.style.display = "block"
            setTimeout(()=>{
                errorMsg.innerHTML = ""
                errorMsg.style.display = "none"
            },5000)
            return;
        }
        const queryParams = new URLSearchParams({
            readingMaterialResId: res_id,
        });
        const qnaEl = document.querySelectorAll('._qna')
        const mcqEl = document.querySelectorAll('._mcq')

        qnaEl.forEach(el => {
            const value = parseInt(el.value, 10) || 0;
            queryParams.set(el.id+"_qna", value);
        })

        mcqEl.forEach(el=>{
            const value = parseInt(el.value, 10) || 0;
            queryParams.set(el.id+"_mcq", value);
        })
        loader.style.display = "flex";
        const quizRes = await fetch("/prompt/createTest?"+queryParams.toString());
        const quizData = await quizRes.json();
        renderQuestions(quizData)
    }

    function renderQuestions(data) {
        modal.style.display = 'none';
        loader.style.display = "none";
        showTestQuestions(data, false)
        document.getElementById('showAnswersBtn').addEventListener('click', function() {
            showTestQuestions(data, true)
            document.getElementById('showAnswersBtn').style.display = 'none';
        });
    }

    function showTestQuestions(data, showAnswer){
        var container = document.getElementById('questionPaperContainer');
        container.innerHTML = "";

        if (data.mcqs.length > 0) {
            var mcqHTML = "";
            data.mcqs.forEach(function(question, index) {
                var questionHTML =
                    "<div class='question'>" +
                    "<p>Q" + (index + 1) + ". " + question.question + "</p>" +
                    "<div class='options'>";

                var optionLabels = ['A', 'B', 'C', 'D'];
                for (var i = 0; i < 4; i++) {
                    questionHTML +=
                        "<p class='option'>" +
                        optionLabels[i] + ". " + question["option" + (i + 1)] +
                        "</p>";
                }

                questionHTML += "</div>";

                if (showAnswer) {
                    var answerOpt = "";
                    if (question.answer1 === "Yes") {
                        answerOpt = "<strong>Answer: </strong>A. " + question.option1;
                    } else if (question.answer2 === "Yes") {
                        answerOpt = "<strong>Answer: </strong>B. " + question.option2;
                    } else if (question.answer3 === "Yes") {
                        answerOpt = "<strong>Answer: </strong>C. " + question.option3;
                    } else if (question.answer4 === "Yes") {
                        answerOpt = "<strong>Answer: </strong>D. " + question.option4;
                    }

                    questionHTML +=
                        "<div class='answer'>" +
                        "<p>" + answerOpt + "</p>" +
                        "<p><strong>Explanation: </strong>" + question.answerDescription + "</p>" +
                        "</div>" +
                        "<p class='difficulty'>Difficulty: " + question.difficultyLevel + "</p>";
                }
                questionHTML +=
                    "<button class='copy-btn'><i class='fa-solid fa-copy'></i></button>" + // Add copy button
                    "</div>";
                questionHTML += "</div>";
                mcqHTML += questionHTML;
            });
            container.innerHTML += mcqHTML;
        }

        if (data.qna.length > 0) {
            var folCount = data.mcqs.length > 0 ? data.mcqs.length : 0;
            var qnaHTML = "";
            data.qna.forEach(function(question, index) {
                var questionHTML =
                    "<div class='question'>" +
                    "<p>Q" + ((index + folCount) + 1) + ". " + question.question + "</p>";

                if (showAnswer) {
                    questionHTML +=
                        "<div class='answer'>" +
                        "<p><strong>Answer: </strong>" + question.answer + "</p>" +
                        "</div>" +
                        "<p class='difficulty'>Difficulty: " + question.difficultyLevel + "</p>";
                }
                questionHTML +=
                    "<button class='copy-btn'><i class='fa-solid fa-copy'></i></button>" + // Add copy button
                    "</div>";
                questionHTML += "</div>";
                qnaHTML += questionHTML;
            });
            container.innerHTML += qnaHTML;
        }
        const tempEl = document.createElement("div")
        tempEl.innerHTML = container.innerHTML
        renderMathInElement(tempEl, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        container.innerHTML = tempEl.innerHTML
        container.style.display = "block"
        document.getElementById('showAnswersBtn').style.display = 'block';
        document.querySelectorAll('.copy-btn').forEach(function (button) {
            button.addEventListener('click', function (event) {
                var questionElement = event.target.closest('.question');
                var questionText = questionElement.querySelector('p').textContent.trim(); // Get the main question
                var options = Array.from(questionElement.querySelectorAll('.option')).map(function (option) {
                    return option.textContent.trim(); // Get each option
                });
                var answerElement = questionElement.querySelector('.answer');
                var answerText = answerElement ? answerElement.textContent.trim() : '';
                var difficultyElement = questionElement.querySelector('.difficulty');
                var difficultyText = difficultyElement ? difficultyElement.textContent.trim() : '';


                var formattedText = questionText+"\n" +
                    options.join('\n') +"\n"+
                answerText +"\n"+
                difficultyText;

                navigator.clipboard.writeText(formattedText)
                    .then(function () {
                        alert('Content copied!');
                    })
                    .catch(function (err) {
                        console.error('Failed to copy text: ', err);
                    });
            });
        });
    }
    function validateQPInput(){
        const questionPaperInputs = document.querySelectorAll('.questionPaperInput');

        const errObj = {
            error:false,
            message:""
        }
        for (let i = 0; i < questionPaperInputs.length; i++) {
            if (parseInt(questionPaperInputs[i].value) > 0 && Number(questionPaperInputs[i].max) >= Number(questionPaperInputs[i].value)) {
                errObj.error = false
                return errObj;
            }else if(Number(questionPaperInputs[i].max) <= Number(questionPaperInputs[i].value)){
                errObj.error = true
                errObj.message = "Please enter a valid number"
                return errObj;
            }else{
                errObj.error = true
                errObj.message = "Please fill at least one field"
            }
        }
        return errObj;
    }
    function quizInputHandler(result){
        modal.style.display = 'flex';
        if(res_type=="giveTest"){
            addQPModalBody(result)
        }else{
            addContentToModalBody()
        }
    }
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    document.addEventListener("DOMContentLoaded", function () {
        var editTitleBtn = document.querySelector(".menuOptions button:first-child");
        var closeModal = document.getElementById("closeModal");
        var modalBody = document.getElementById("modalBody");

        editTitleBtn.addEventListener("click", function () {
            modal.style.display = "flex";
            closeModal.style.display = "block";

            modalBody.innerHTML = "";
            var heading = document.createElement("h5");
            heading.textContent = "Edit Page Title";
            modalBody.appendChild(heading);

            var input = document.createElement("input");
            input.type = "text";
            input.id = "newTitle";
            input.placeholder = "Enter new title";
            input.style.width = "100%";
            input.style.padding = "8px";
            modalBody.appendChild(input);

            var button = document.createElement("button");
            button.id = "updateTitle";
            button.classList.add('updateTitle')
            button.textContent = "Update";
            modalBody.appendChild(button);

            button.addEventListener("click", function () {
                var newTitle = input.value.trim();
                if (newTitle) {
                    document.getElementById("page-title").textContent = newTitle;
                    modal.style.display = "none"; // Close the modal
                }
            });
        });


        var printBtn = document.querySelector(".menuOptions button:nth-child(2)");
        printBtn.addEventListener("click", function () {
            var gptContents = document.getElementById("gpt-contents");
            var printWindow = window.open("", "_blank", "width=800,height=600");

            const tt = document.getElementById('page-title').textContent
            var printContent = "<html><head><title>"+tt+"</title><style>";
            printContent += Array.from(document.styleSheets)
                .map(function (styleSheet) {
                    try {
                        return Array.from(styleSheet.cssRules || [])
                            .map(function (rule) {
                                return rule.cssText;
                            })
                            .join("\n");
                    } catch (e) {
                        return "";
                    }
                })
                .join("\n");
            printContent += "</style></head><body>";
            printContent += gptContents.outerHTML;
            printContent += "</body></html>";

            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        });
    });
    getGptContent()
</script>
</body>
</html>