<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script src="https://code.jquery.com/jquery-1.12.4.min.js" crossorigin="anonymous"></script>
<script src="/assets/wonderslate/vendors.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<style>
.pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.back-btn {
    background-color: #f0f0f0;
    color: #333;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background-color: #e0e0e0;
}

.form-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-heading {
    margin-top: 0;
    color: #333;
    font-size: 1.25rem;
}

.section-description {
    color: #666;
    margin-bottom: 1.5rem;
}

.input-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.image-upload-container {
    border: 2px dashed #ccc;
    border-radius: 4px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 1rem;
    min-height: 50px;
}

.image-preview-wrapper {
    position: relative;
    width: 100px;
    height: 100px;
    cursor: grab;
    transition: transform 0.2s;
    border: 2px solid transparent;
}

.image-preview-wrapper.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    z-index: 1000;
}

.image-preview-wrapper.drag-over {
    border: 2px dashed #1890ff;
}

.image-order-number {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translate(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.remove-image-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #ff4d4f;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    padding: 10px !important;
}

.text-input-container textarea {
    width: 100%;
    min-height: 150px;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
}

.divider {
    margin: 2rem 0;
    border-top: 1px solid #eee;
}

.action-group {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.button-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#createSolutionButton {
    background-color: #1890ff;
    color: white;
}

#createSolutionButton:hover:not(:disabled) {
    background-color: #096dd9;
}

.clear-btn, .clear-all-btn {
    background-color: #f0f0f0;
    color: #333;
}

.clear-btn:hover, .clear-all-btn:hover {
    background-color: #e0e0e0;
}

.error-message {
    color: #ff4d4f;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.loading {
    display: none;
    text-align: center;
    margin: 2rem 0;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #1890ff;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.solution-result {
    display: none;
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.result-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.result-title-container h3 {
    margin: 0;
}

.copy-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: transparent;
    color: #1890ff;
    padding: 0.5rem 0.75rem;
    border: 1px solid #1890ff;
}

.copy-btn:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

.solution-content {
    background-color: white;
    padding: 1.5rem;
    border-radius: 4px;
    border: 1px solid #eee;
    min-height: 150px;
    white-space: pre-wrap;
}


.katex-display>.katex{
    text-align: unset !important;
}
.katex-display{
    text-align: unset !important;
}


</style>
<%
    String backNav
    if ('books'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/home"
    }else if('privatelabel'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/admin"
    }
%>
<body>
<div class="container">
    <div class="pyqs-header mt-5">
        <h1>Create Solution</h1>
        <a href="${backNav}" class="back-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
            </svg>
            Back to Admin
        </a>
    </div>
    <p class="mb-4">Create solutions for problems using sample questions and solutions.</p>

    <g:form id="createSolutionForm" url="[controller: 'pdfExtractor', action: 'createMcqSolution']" method="post" enctype="multipart/form-data">
    <%-- Form content identical to solution_creation.html --%>
    <%-- Sample Question Section --%>
        <div class="form-sections">
            <div class="form-section">
                <h3 class="section-heading">Sample Question with Solution</h3>
                <p class="section-description">Provide a sample question with solution to help generate better results.</p>

                <div class="input-section">
                    <div class="form-group">
                        <label for="sampleImageUpload"><strong>Upload Image</strong></label>
                        <div class="image-upload-container">
                            <input type="file" id="sampleImageUpload" name="sample_image_0" accept="image/*" onchange="previewImages(event, 'sampleImagePreviewContainer', 'sampleImages')">
                            <div class="image-preview-container" id="sampleImagePreviewContainer"></div>
                        </div>
                        <div class="error-message" id="sampleImageError"></div>
                    </div>

                    <div class="form-group">
                        <label for="sampleTextInput"><strong>OR Enter Text</strong></label>
                        <div class="text-input-container">
                            <g:textArea id="sampleTextInput" name="sample_text" placeholder="Enter sample question with solution..." oninput="validateInputs()"/>
                        </div>
                        <div class="error-message" id="sampleTextError"></div>
                    </div>
                </div>
            </div>

            <%-- Question to Solve Section --%>
            <div class="form-section">
                <h3 class="section-heading">Question to Create Solution For</h3>
                <p class="section-description">Provide the question you want to generate a solution for.</p>

                <%-- Question input fields (similar to sample section) --%>
                <div class="input-section">
                    <div class="form-group">
                        <label for="questionImageUpload"><strong>Upload Image</strong></label>
                        <div class="image-upload-container">
                            <input type="file" id="questionImageUpload" name="question_image_0" accept="image/*" onchange="previewImages(event, 'questionImagePreviewContainer', 'questionImages')">
                            <div class="image-preview-container" id="questionImagePreviewContainer"></div>
                        </div>
                        <div class="error-message" id="questionImageError"></div>
                    </div>

                    <div class="form-group">
                        <label for="questionTextInput"><strong>OR Enter Text</strong></label>
                        <div class="text-input-container">
                            <g:textArea id="questionTextInput" name="question_text" placeholder="Enter question to solve..." oninput="validateInputs()"/>
                        </div>
                        <div class="error-message" id="questionTextError"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <div class="form-group action-group">
            <div class="button-group">
                <button type="button" id="createSolutionButton" onclick="createSolution()" disabled>Create Solution</button>
                <button type="button" id="clearSolutionButton" onclick="clearInputFields()" class="clear-btn">Clear Question</button>
                <button type="button" id="clearAllButton" onclick="clearAllInputFields()" class="clear-all-btn">Clear All</button>
            </div>
            <div class="error-message" id="solutionFormError"></div>
        </div>
    </g:form>

    <div class="loading" id="solutionLoadingIndicator">
        <div class="spinner"></div>
        <p>Creating solution. This may take a moment...</p>
    </div>

    <div class="solution-result" id="solutionResultSection">
        <div class="result-title-container">
            <h3>Solution</h3>
            <button onclick="copyText('solutionResult', event)" class="copy-btn solution-copy-btn" title="Copy solution to clipboard">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                    <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                </svg>
                Copy Solution
            </button>
        </div>
        <div class="solution-container">
            <pre class="solution-content" id="solutionResult"></pre>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    // Global variables to store uploaded images
    let sampleImages = [];
    let questionImages = [];
    let rawSolutionText = '';
    let dragSrcElement = null;

    // Validate inputs to enable/disable the create button
    function validateInputs() {
        const sampleTextInput = document.getElementById('sampleTextInput').value.trim();
        const questionTextInput = document.getElementById('questionTextInput').value.trim();
        const createButton = document.getElementById('createSolutionButton');

        // Clear error messages
        document.getElementById('sampleTextError').textContent = '';
        document.getElementById('questionTextError').textContent = '';
        document.getElementById('sampleImageError').textContent = '';
        document.getElementById('questionImageError').textContent = '';

        // Check if we have valid inputs for both sample and question
        const hasSampleInput = sampleTextInput !== '' || sampleImages.length > 0;
        const hasQuestionInput = questionTextInput !== '' || questionImages.length > 0;

        // Enable button only if we have both inputs
        createButton.disabled = !(hasSampleInput && hasQuestionInput);
    }

    // Preview image before upload
    function previewImages(event, previewContainerId, imageArrayName) {
        const files = event.target.files;
        const previewContainer = document.getElementById(previewContainerId);

        // Clear previous previews
        previewContainer.innerHTML = '';

        // Reset the image array
        if (imageArrayName === 'sampleImages') {
            sampleImages = [];
        } else {
            questionImages = [];
        }

        // Only allow one file
        if (files.length > 1) {
            const errorElement = document.getElementById(
                imageArrayName === 'sampleImages' ? 'sampleImageError' : 'questionImageError'
            );
            errorElement.textContent = "Only one image allowed";
            return;
        }

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Store the file in the appropriate array
            if (imageArrayName === 'sampleImages') {
                sampleImages.push(file);
            } else {
                questionImages.push(file);
            }

            // Create preview
            const previewWrapper = document.createElement('div');
            previewWrapper.className = 'image-preview-wrapper';
            previewWrapper.dataset.index = i;

            const preview = document.createElement('img');
            preview.className = 'image-preview';
            preview.file = file;

            // No need for order number with single image

            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-image-btn';
            removeBtn.innerHTML = '&times;';
            removeBtn.onclick = function() {
                previewContainer.removeChild(previewWrapper);

                // Remove from array
                if (imageArrayName === 'sampleImages') {
                    sampleImages = sampleImages.filter(img => img !== file);
                } else {
                    questionImages = questionImages.filter(img => img !== file);
                }

                // Update order numbers
                updateOrderNumbers(previewContainer);
                validateInputs();
            };

            previewWrapper.appendChild(preview);
            previewWrapper.appendChild(removeBtn);
            previewContainer.appendChild(previewWrapper);

            // Read and display the image
            const reader = new FileReader();
            reader.onload = (function(aImg) {
                return function(e) {
                    aImg.src = e.target.result;
                };
            })(preview);
            reader.readAsDataURL(file);
        }

        validateInputs();
    }

    // These drag and drop functions are no longer needed with single image
    // but we'll keep empty implementations to avoid errors
    function handleDragStart(e) {}
    function handleDragOver(e) {}
    function handleDragLeave(e) {}
    function handleDrop(e) {}
    function handleDragEnd(e) {}
    function updatePreviewOrder(container, arrayType) {}

    // Update indices after removing an image
    function updateOrderNumbers(container) {
        const wrappers = container.querySelectorAll('.image-preview-wrapper');
        wrappers.forEach((wrapper, index) => {
            wrapper.dataset.index = index;
        });
    }

    // Create solution
    async function createSolution() {
        const sampleTextInput = document.getElementById('sampleTextInput').value.trim();
        const questionTextInput = document.getElementById('questionTextInput').value.trim();
        const formError = document.getElementById('solutionFormError');
        const loadingIndicator = document.getElementById('solutionLoadingIndicator');
        const resultSection = document.getElementById('solutionResultSection');

        // Validate inputs again
        validateInputs();

        if (document.getElementById('createSolutionButton').disabled) {
            return; // Validation failed
        }

        // Show loading indicator
        loadingIndicator.style.display = 'block';
        resultSection.style.display = 'none';
        formError.textContent = '';

        try {
            const form = document.getElementById('createSolutionForm');
            const formData = new FormData(form);

            // Add sample data
            if (sampleImages.length > 0) {
                // Remove any existing sample_text field
                formData.delete('sample_text');

                // Add sample images in the order they appear in the array
                sampleImages.forEach((file, index) => {
                    formData.append('sample_image_' + index, file);
                });

                // Add a hidden field to indicate the order
                formData.append('sample_image_order', JSON.stringify(Array.from({length: sampleImages.length}, (_, i) => i)));
            } else {
                formData.append('sample_text', sampleTextInput);
            }

            // Add question data
            if (questionImages.length > 0) {
                // Remove any existing question_text field
                formData.delete('question_text');

                // Add question images in the order they appear in the array
                questionImages.forEach((file, index) => {
                    formData.append('question_image_' + index, file);
                });

                // Add a hidden field to indicate the order
                formData.append('question_image_order', JSON.stringify(Array.from({length: questionImages.length}, (_, i) => i)));
            } else {
                formData.append('question_text', questionTextInput);
            }

            // Send request to API
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to create solution');
            }

            const data = await response.json();

            // Store raw solution text for copying
            rawSolutionText = data.solution;

            // Display the solution
            const solutionResult = document.getElementById('solutionResult');

            // Use MathJax to render LaTeX if available
            solutionResult.innerHTML = data.solution;
            renderMathInElement(solutionResult, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html',
                ignoredTags: []
            });

            // Show the result section
            resultSection.style.display = 'block';

            // Clear only the question input fields, preserve sample
            clearInputFields();
        } catch (error) {
            formError.textContent = error.message;
        } finally {
            loadingIndicator.style.display = 'none';
        }
    }

    // Clear question input fields
    function clearInputFields() {
        document.getElementById('questionTextInput').value = '';
        document.getElementById('questionImageUpload').value = '';
        document.getElementById('questionImagePreviewContainer').innerHTML = '';
        questionImages = [];
        validateInputs();
    }

    // Clear all input fields
    function clearAllInputFields() {
        // Clear question fields
        clearInputFields();

        // Clear sample fields
        document.getElementById('sampleTextInput').value = '';
        document.getElementById('sampleImageUpload').value = '';
        document.getElementById('sampleImagePreviewContainer').innerHTML = '';
        sampleImages = [];

        // Hide result section
        document.getElementById('solutionResultSection').style.display = 'none';

        validateInputs();
    }

    // Copy text to clipboard
    function copyText(elementId, event) {
        const button = event.currentTarget;
        const originalText = button.innerHTML;

        // Get the text to copy
        const textToCopy = rawSolutionText || document.getElementById(elementId).innerText;

        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = textToCopy;
        textarea.setAttribute('readonly', '');
        textarea.style.position = 'absolute';
        textarea.style.left = '-9999px';
        document.body.appendChild(textarea);

        // Select and copy the text
        textarea.select();
        document.execCommand('copy');

        // Remove the textarea
        document.body.removeChild(textarea);

        // Update button text to show success
        button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/></svg> Copied!';

        // Reset button text after 2 seconds
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }

    // No need for Sortable with single image
    function initSortable() {
        // Sortable functionality removed as we only allow one image
    }

    // Initialize when document is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Set up initial validation
        validateInputs();

        // Hide result section initially
        document.getElementById('solutionResultSection').style.display = 'none';
        document.getElementById('solutionLoadingIndicator').style.display = 'none';

        // Initialize Sortable
        initSortable();
    });
</script>
</body>