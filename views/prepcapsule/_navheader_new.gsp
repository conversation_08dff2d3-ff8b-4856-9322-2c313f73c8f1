<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"PrepCapsule"%></title>
    <meta name="description" content="Next-gen learning platform which offers Smart eBooks for competitive exams such as JEE, NEET, UPSC, Banking and much more. Learn, practice and analyze your preparation now!">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'prepcapsule/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'prepcapsule/PrepCapsule-icon.svg')}"/>
    <link rel="windows-touch-icon" href="${assetPath(src: 'prepcapsule/PrepCapsule-icon.svg')}" />
    <meta name="facebook-domain-verification" content="b1ndgtx0vj7jgzgmkhl612wuekczgd" />
    <meta name="theme-color" content="#6F58D8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
    <asset:stylesheet href="wonderslate/material.css" async="true"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
    <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
    <asset:stylesheet href="landingpage/fonts/flaticon.css" async="true"/>
    <asset:stylesheet href="landingpage/smartbanner.css" async="true"/>
    <%if("true".equals(commonTemplate)){%>
    <asset:stylesheet href="wonderslate/wsTemplate.css" async="true"/>
    <% }%>
    <link rel="stylesheet" href="/assets/katex.min.css">

    <!-- General styles for admin & user pages -->
    <asset:stylesheet href="wonderslate/ws_general.css" async="true"/>

    <!-- Animation CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css"/>


    <!-- Facebook Pixel Code -->
    <script>!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js'); fbq('init', '943725009655123'); fbq('track', 'PageView');</script>
    <noscript> <img height="1" width="1" src="https://www.facebook.com/tr?id=943725009655123&ev=PageView&noscript=1"/></noscript>
    <!-- End Facebook Pixel Code -->

    <script>
        function encodeString(String){
            return(btoa(String))
        }
        function decodeString(String){
            return(atob(String))
        }
        function unicodeToChar(text) {
            return text.replace(/\\u[\dA-F]{4}/gi,
                function (match) {
                    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                });
        }
        function htmlDecode( html ) {
            var a = document.createElement( 'a' ); a.innerHTML = html;
            return a.textContent;
        };
    </script>
    <style>
    .mobile-back-button{
        display: none;
    }
    #allAddButton {
        display: none !important;
    }
    html body {
        background-color: #FFF;
    }
    </style>
    <!-- Global site tag (gtag.js) - Google Ads -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-10879966217"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'AW-10879966217');
    </script>
    <!--end of tag maneger-->
</head>

<body class="wonderslate_main">



<g:render template="/books/signIn"></g:render>
<% boolean isInstitutePublisher = false %>
<%if(session.getAttribute("userdetails")!=null&&session.getAttribute("userdetails").publisherId!=null){

    isInstitutePublisher = session["isInstitutePublisher"]

}%>
<!-- Header -->
<%if(showHeader==null||"true".equals(showHeader)){%>

<%if(params.tokenId==null&&session["appType"]==null){%>

<div class="back_to_top mdl-js">
    <button type="button" onclick="javascript:backToTop();" id="goTopBtn" class="btn btn-shadow border-0 d-flex justify-content-center align-items-center mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect">
        <i class="material-icons-round">north</i>
    </button>
</div>

<div class="mega_menu__wrapper shadow">
    <button type="button" class="btn btn-sm btn-warning btn-warning-modifier btn-shadow border-0 d-flex justify-content-center align-items-center mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect mega_menu__close_mob d-md-none">Close</button>
    <div id="mega_menu__mainNavbar" class="d-md-none explored"></div>
    <div class="mega_menu__mange_rows position-relative">
        <div class="row m-0">
            <div class="card-columns w-100" id="topMenuItems"></div>
        </div>
    </div>
</div>
<div class="mega_menu__overlay_bg"></div>

<div class="mdl-js-layout">
    <header class="navbar navbar-expand navbar-header container-fluid bg-white px-3 px-xl-5 <%if(session.getAttribute("publisherLogo")!=null && session.getAttribute("publisherLogo")!=""){%>active-publisher<%}%>">
        <div class="navbar-hamburger">
            <a href="javascript:void(0)" class="mega_menu__hamburger_btn">
                <i class="material-icons-round mega_menu__icon">menu</i>
                <i class="material-icons-round mega_menu__close d-none d-md-block">highlight_off</i>
            </a>
        </div>
        <button class="mobile-back-button d-lg-none" onclick="javascript:history.back();">
            <img src="${assetPath(src: 'ws/icon-back-arrow-white.svg')}" class="d-lg-none"> Back
        </button>
        <%if(!"true".equals(session["appInApp"])){%>

        <%if(session.getAttribute("publisherLogo")!=null && session.getAttribute("publisherLogo")!=""){%>
        <a href="/publisherManagement/publisher?id=${session.getAttribute("publisherLogo")}" class="mobile-logo border-right mr-2">
            <img class="publisher-logo" style="width:35px;height:35px;margin-right:5px;" onerror="this.style.display='none'" src="/publisherManagement/showPublisherImage?id=${session.getAttribute("publisherLogo")}" alt="">
        </a>
        <%}%>

        <sec:ifNotLoggedIn>
        <a class="navbar-brand" href="/prepcapsule/index">
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
        <a class="navbar-brand d-none d-md-block" href="/prepcapsule/index">
        </sec:ifLoggedIn>
            <img src="${assetPath(src: 'prepcapsule/PrepCapsule-logo-dark.svg')}" width="180" class="d-none d-md-block">
            <img src="${assetPath(src: 'prepcapsule/PrepCapsule-icon.svg')}" width="35" class="d-md-none">
        </a>

        <div class="pomodoro_section d-none">
            <a href="javascript:openCurrentPomodoro();">
                <canvas id="canvasProgressBar" width=90 height=40></canvas>
            </a>
            <a href="javascript:openCurrentPomodoro();" id="pomodoroTimer"></a>
        </div>

        <div class="notify_icon mt-3 ml-3 d-none">
            <a href="#">
                <i class="material-icons-round">notifications_active</i>
                <small>3</small>
            </a>
        </div>
        <ul id="mainNavbar" class="navbar-nav">
            <sec:ifNotLoggedIn>
                <li class="nav-item">
                    <a href="javascript:loginOpen();" class="nav-link">My Library</a>
                </li>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
                <li class="nav-item">
                    <a href="/wsLibrary/myLibrary" class="nav-link">My Library</a>
                </li>
                <sec:ifAllGranted roles="ROLE_INSTITUTION_DEMO">
                    <li class="nav-item">
                        <a href="/intelligence/demo" class="nav-link">Demo</a>
                    </li>
                </sec:ifAllGranted>
                <%if(session["userInstitutes"]!=null&&session["userInstitutes"].size()>0){%>
                <li class="nav-item">
                    <a href="/institute/home?instituteId=${session["userInstitutes"][0].id}" class="nav-link institute-menu">
                        <span class="institute-menu-text">${session["userInstitutes"][0].name}</span>
                    </a>
                </li>
                <%}%>

                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                    <li class="nav-item">
                        <a href="/publishing-desk" class="nav-link">Publishing Desk</a>
                    </li>
                </sec:ifAllGranted>


            </sec:ifLoggedIn>
            <li class="nav-item">
                <a href="/prepcapsule/store" class="nav-link">eBooks Store</a>
            </li>
%{--            <sec:ifLoggedIn>--}%
%{--                <li class="nav-item">--}%
%{--                    <a href="/test-generator" class="nav-link">Create Test</a>--}%
%{--                </li>--}%
%{--                <li class="nav-item">--}%
%{--                    <a href="/usermanagement/orders" class="nav-link">Orders History</a>--}%
%{--                </li>--}%
%{--            </sec:ifLoggedIn>--}%
%{--            <li class="nav-item">--}%
%{--                <a href="/doubts" class="nav-link">Doubts</a>--}%
%{--            </li>--}%
        </ul>
        <ul class="navbar-nav ml-auto">
            <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")==0){%>
            <li class="nav-item pr-0">
                <a href="javascript:loginOpen();" class="nav-link login-menu-link">
                    <span class="d-none d-md-block">Login</span> <i class="material-icons-round d-md-none">person_outline</i>
                </a>
            </li>
            <li class="nav-item d-none d-md-block">
                <a href="javascript:signupModal();" class="btn nav-link register-menu-btn shadow-sm">
                    Register for Free
                </a>
            </li>
            <%}%>
        </ul>

        <div class="navbar-search ebooks d-none">
            <div class="d-flex justify-content-center align-items-center global-search">
                <form class="form-inline rounded rounded-modifier col-12 p-0">
                    <i class="material-icons-round">search</i>
                    <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
                </form>
            </div>
        </div>

        <div class="navbar_cart px-2 px-md-1 px-lg-2">
            <sec:ifLoggedIn>
                <a href="/books/shoppingCart" class="mobile_cart_icon"><i class="material-icons-outlined">shopping_cart</i><span class="cart_count" id="navbarCartCount">0</span></a>
            </sec:ifLoggedIn>
            <sec:ifNotLoggedIn>
                <a href="javascript:loginOpenWithFunction('goCartPage','Please login to see your cart.');" class="mobile_cart_icon"><i class="material-icons-outlined">shopping_cart</i><span class="cart_count" id="navbarCartCount">0</span></a>
            </sec:ifNotLoggedIn>
        </div>

        <div class="navbar_search_trigger px-1 px-lg-2">
            <a href="javascript:showSearchForm();" class="d-block"><i class="material-icons-round search-open-icon">search</i><i class="material-icons-round search-close-icon d-none">close</i></a>
        </div>

        <div class="navbar-whatsapp-link">
            <a href="https://wa.me/916363371085" target="_blank" data-toggle="tooltip" title="Chat with us" class="btn d-flex whatsapp-btn pl-0">
                <i class="material-icons-round">whatsapp</i>
            </a>
        </div>

        <div class="navbar-user d-flex align-items-center">
            <sec:ifLoggedIn>
                <div class="dropdown dropdown-user">
                    <a class="dropdown-toggle dropdown-user-link" href="#" data-toggle="dropdown" aria-expanded="false">
                        <span class="avatar rounded-circle">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                            <%}%>
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right p-3">
                        <span class="username mb-2 d-block">Hello, <strong><%= session["userdetails"]!=null?session["userdetails"].name:"" %>!</strong></span>
                        <a class="dropdown-item border-top pl-0" href="/usermanagement/editprofile"><i class="material-icons-round mr-2">person_outline</i> Edit Profile</a>
                        <a class="dropdown-item pl-0" href="javascript:logout();"><i class="material-icons-round mr-2">power_settings_new</i> Logout</a>
                    </div>
                </div>
            </sec:ifLoggedIn>
        </div>

        <div class="mobile_menu d-none">
            <a href="javascript:void(0)" class="d-flex align-items-center mobile_navigation__btn pl-2">
                Menu
                <span class="menu_line">
                    <span class="first_line"></span>
                    <span class="second_line"></span>
                </span>
            </a>
        </div>

        <%}%>
    </header>

</div>


<%}%>
<%}%>
<script>
    var activeCategories = [];
    if("${session["activeCategories_"+session["siteId"]]}" != "") {
        activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
    }

    function showSearchForm() {
        $("body").toggleClass('showing-search-form');
        $(".search-open-icon,.search-close-icon,header .navbar-search").toggleClass("d-none");
        if($("body").hasClass("showing-search-form")) {
            $("#search-book-header").focus();
        }
    }

    $(window).scroll(function () {
        if ( $(this).scrollTop() > 300 && !$('body').hasClass('fixed-navbar') ) {
            $('body').addClass('fixed-navbar');
        } else if ( $(this).scrollTop() <= 300 ) {
            $('body').removeClass('fixed-navbar');
        }
    });

    $(".mega_menu__hamburger_btn").click(function(){
        $(".navbar-hamburger").toggleClass("menu-actives");
        $(".mega_menu__overlay_bg").toggleClass("active");
        $(".mega_menu__wrapper").toggleClass("menu-showing");
        if ($(window).width() < 767) {
            $("body").css({
                'overflow-y':'hidden'
            });
        }
    });

    $(".mega_menu__overlay_bg,.mega_menu__close_mob").click(function(){
        $(".navbar-hamburger").removeClass("menu-actives");
        $(".mega_menu__overlay_bg").removeClass("active");
        $(".mega_menu__wrapper").removeClass("menu-showing");
        if ($(window).width() < 767) {
            $("body").css({
                'overflow-y':'unset'
            });
        }
    });

    $(".mobile_navigation__btn").click(function() {
        $("#mainNavbar").toggleClass("mobile-menu-showing").removeClass("d-none");
        $(this).toggleClass("active");
    });

    function backToTop() {
        $('html, body').animate({scrollTop:0}, '300');
    }

    function logout(){
        window.location.href = '/logoff';
    }

    function exploreCategories() {
        $("#topMenuItems").toggleClass("hidden_categories");
        $("#mega_menu__mainNavbar").toggleClass("explored");
    }

    $(document).ready(function () {
        var divContent = $("#mainNavbar").html();
        if ($(window).width() < 767) {
            document.getElementById("mega_menu__mainNavbar").innerHTML = "<div class='card border-0 mega_menu__links'><ul class='list-inline p-0'>"+divContent+"</ul><a href='javascript:exploreCategories();' class='explore_cat_btn'>Explore Categories <i class='material-icons-round'>expand_more</i></a></div>";
        }
    });
</script>
