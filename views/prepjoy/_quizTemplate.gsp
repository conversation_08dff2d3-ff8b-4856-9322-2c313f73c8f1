<div class="quizes d-none">
    <!--Success Progressbar-->
    <div class="progress progress-bar-vertical player1">
        <div id='player1' class="progress-bar progress-bar-success active" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="height: 0%;">
            <span class="sr-only">60% Complete</span>
        </div>
    </div>
    <!--Question and Answers-->
    <div class="container">
        <p class="audioChange" onclick="audioChange()"><i class="material-icons">volume_up</i> </p>
        <div class="question-wrapper">

            <div id='question-no'></div>
            <p class="que_text">What is the manchester of South India?</p>
            %{--            <img src="${assetPath(src: 'prepjoy/html.jpeg')}" width="100%" height="100px">--}%
        </div>
        <div class="que-options-wrapper mt-4">
            <div class="que-options">

            </div>
        </div>
    </div>

    <!--Success Progressbar-->
    <div class="progress progress-bar-vertical player2">
        <div id='player2' class="progress-bar progress-bar-success active" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="height: 0%;">
            <span class="sr-only">60% Complete</span>
        </div>
    </div>
</div>

<!--User win status Dialog box-->
<div class="modal fade" id="winnerModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header d-none">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="winner">
                </div>
                <h1 class="user-status">You Won</h1>
                <div id="submitQuiz"></div>
            </div>
            <div class="modal-footer">

            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="medalModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm" role="document">
        <div class="modal-content">

            <div class="modal-body medal-wrappers">
                <div>
                    <div id="medal-upload"></div>
                    <h1>Congratulations</h1>
                    <p id="win-message">You’ve won 8 Consecutive times</p>
                    <div id="medal-user" class="medal-user">
                    </div>
                    <p id="medal-name">Gold Medal</p>
                    <button class="btn btn-answer mt-3" data-dismiss="modal">Back to Scoreboard</button>
                </div>

            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="rankModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="winnerModal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom modal-sm" role="document">
        <div class="modal-content">

            <div class="modal-body medal-wrappers">
                <div>
                    <div id="rank-upload"></div>
                    <h1>Congratulations</h1>
                    <p id="rank-message">You have got 1st Rank</p>
                    <div id="rank-cup" class=''>
                    </div>
                    <button class="btn btn-answer mt-3" data-dismiss="modal">Back to Scoreboard</button>
                </div>

            </div>

        </div>
    </div>
</div>

<!--Score Dashboard-->
<div class="resultWrapper d-none">
    <div class="container-fluid">
        <div class="d-flex justify-content-between result-header">
            <h2><i class="material-icons">
                arrow_left
            </i><img class="prep-logo" src="/assets/prepjoy/prepjoy.svg"></h2>
            <p><span class="result">Results</span></p>
            <p class="invisible"><i class="material-icons">share</i> Share</p>
        </div>
        <div class="mt-3">
            <h1 id="user-status" class="pb-2">
                You Won!
            </h1>

            <div class="badge-screen">
                <div class="text-center">
                    <h1 id="congrats" class="">
                        Congratulations!
                    </h1>
                    <p>You’ve been promoted to</p>
                    <div class="badge">

                    </div>
                    <p id="badgetype"></p>
                </div>
            </div>

            %{--<img src="/assets/prepjoy/celebration.gif"/>--}%
            <div class="score-media d-flex justify-content-center align-items-center">
                <div>
                    <p class="score-text pb-0">You scored</p>
                    <p id="score">123</p>
                </div>
            </div>
            <div id="slider">
                <div id="custom-handle" class="ui-slider-handle"></div>
            </div>
            <div class="container">
                <div class="row justify-content-between badges">
                    <div>
                        <p id="currentBadge">pCommander</p>
                        <p id="currentBadgePoints">2000 pts</p>
                    </div>
                    <div class="">
                        <p id="nextBadge"> pSupreme</p>
                        <p id="nextBadgePoints">4000 pts</p>
                    </div>
                </div>

            </div>
            <div class="mt-3">
                <div class="balance-points">You need 500 pts more to promote to pSupreme</div>
            </div>

            <div class="button-results">
                <button class="btn btn-playagain" onclick="playAgain()">Rematch</button>
                <button class="btn btn-prev btn-playagain" onclick="previousQuiz()">Play Previous</button>
                <button class="btn btn-next btn-playagain" onclick="nextQuiz()">Play Next</button>
            </div>

            <div class="next-match text-center mt-4">
                <button class="btn btn-answer" id="show-answer">Show Answers</button>
            </div>

            <div class="answer-wrapper">
                <div class="answer-indicator">
                    <div>
                        <div class="box green"></div>
                        <p>Correct <br/> Answers</p>
                    </div>
                    <div>
                        <div class="box red"></div>
                        <p>Incorrect <br/> Answers</p>
                    </div>

                </div>
                <div class="qa" id="qa-answer">

                </div>
            </div>
            <div id="lottie"></div>
        </div>
    </div>
</div>

</div>
<script>

    function previousQuiz(){
        if(source!='web'){
            if(source =='android'){
                JSInterface.prevQuiz();
            }
            else{
                webkit.messageHandlers.prevQuiz.postMessage('');
            }
        }
    }
    function nextQuiz(){
        if(source!='web'){
            if(source =='android'){
                JSInterface.nextQuiz();
            }
            else{
                webkit.messageHandlers.nextQuiz.postMessage('');
            }
        }
    }
</script>