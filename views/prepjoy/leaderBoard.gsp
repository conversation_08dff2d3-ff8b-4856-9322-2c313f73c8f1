<!DOCTYPE html>
<html lang="en" >
<head>
    <meta charset="UTF-8">
    <title><%= title!=null?title:"LeaderBoard"%></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/prepjoyWebsites/leaderBoard.css">
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <g:render template="/wonderpublish/loginChecker"></g:render>
    <style>

    </style>
</head>
<body>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<!-------- LOADER --------->
<div class="loading-icon ">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container">
    <div class="daily__test-header mt-5">
        <div class="daily__test-header__title d-flex align-items-center">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <h3 class="text-primary-modifier">
                <strong>LeaderBoard</strong>
            </h3>
        </div>
    </div>

</div>

<div class="container topc" style="margin-top: 75px">
    <div class="top3">

    </div>
</div>

<div class="container mx-auto tabs-content">
    <div class="tabs-wrapper d-flex  align-items-center">
        <button type="button" class="btn tab-btn btn-secondary activeDate mr-3" id="dailyRank">Daily</button>
        <button type="button" class="btn tab-btn btn-secondary mr-3" id="weeklyRank">Weekly</button>
        <button type="button" class="btn tab-btn btn-secondary" id="monthlyRank">Monthly</button>
    </div>
    <div class="custom__date-wrapper d-flex justify-content-center">
        <input type="text" class="datepicker"  maxlength="10" id="datepicker" placeholder="DD-MM-YYYY">
    </div>
    <p class="text-danger text-center"  id="dateError" style="display: none">Please Enter a valid date (DD-MM-YYYY)</p>
</div>

<div class="container mb-5" id="nextList">

</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>

    var currentDate = new Date().toISOString().split("T")[0]

    //EVENT LISTENERS
    $('#dailyRank').on('click',function (){
        $('#dailyRank').addClass('activeDate');
        $("#weeklyRank").removeClass('activeDate');
        $("#monthlyRank").removeClass('activeDate');
        $('.custom__date-wrapper').addClass('d-flex').removeClass('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        document.querySelector('.datepicker').value = currentDate.split('-').reverse().join('-');
        getDailRank();
    });

    $("#weeklyRank").on('click',function (){
        $('#weeklyRank').addClass('activeDate');
        $("#dailyRank").removeClass('activeDate');
        $("#monthlyRank").removeClass('activeDate');
        $('.custom__date-wrapper').removeClass('d-flex').addClass('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        getWeeklyRank();
    });

    $('#monthlyRank').on('click',function (){
        $('#monthlyRank').addClass('activeDate');
        $("#dailyRank").removeClass('activeDate');
        $("#weeklyRank").removeClass('activeDate');
        $('.custom__date-wrapper').removeClass('d-flex').addClass('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        getMonthlyRank();
    });

   document.querySelector('.datepicker').value = currentDate.split('-').reverse().join('-');
    $('.datepicker').datepicker({
        dateFormat: 'dd-mm-yy',
        autoclose:true,
        endDate: "today",
        maxDate:'0'
    }).on('changeDate', function (ev) {
        $(this).datepicker('hide');
    });
    $('.datepicker').on('change',function (e){
        currentDate = e.target.value;
        currentDate = currentDate.split('-').reverse().join('-');
        var valid = moment(e.target.value, 'DD-MM-YYYY',true).isValid();
        if(valid){
            currentDate = e.target.value.split('-').reverse().join('-')
            getDailRank();
        }else {
            $('#dateError').show();
            currentDate = new Date().toISOString().split("T")[0];
            wage.value =currentDate.split('-').reverse().join('-');
            getDailRank();
            setTimeout(()=>{
                $('#dateError').hide();
            },1600)
        }
    })

    var wage = document.getElementById("datepicker");

    wage.addEventListener("keydown", function (e) {
        if (e.code === "Enter") {
            var valid = moment(e.target.value, 'DD-MM-YYYY',true).isValid();
            if(valid){
                currentDate = e.target.value.split('-').reverse().join('-');
                getDailRank();
            }else {
                $('#dateError').show();
                currentDate = new Date().toISOString().split("T")[0];
                wage.value =currentDate.split('-').reverse().join('-');
                getDailRank();
                setTimeout(()=>{
                    $('#dateError').hide();
                },1600)
            }
        }
    });
    function getDailRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyDailyRanks" params="'rankDate='+currentDate+'&siteId=1'" onSuccess="dailyRankUI(data)" />
    }

    function getWeeklyRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyWeeklyRanks" params="'rankDate='+currentDate+'&siteId=1'" onSuccess="dailyRankUI(data)" />
    }
    function getMonthlyRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="prepjoy" action="getPrepjoyMonthlyRanks" params="'rankDate='+currentDate+'&siteId=1'" onSuccess="dailyRankUI(data)" />
    }

    getDailRank();
    function dailyRankUI(data){
        $('.loading-icon').addClass('hidden');

        if (data !=='No Ranks'){
            var leaderBoardData = JSON.parse(data);
            var topLeaders =[];
            var nextLeaders = [];
            var lbHtml = "";
            var tHtml = "";

            for(var n=0;n<leaderBoardData.length;n++){
                if (leaderBoardData[n].rank != 1 && leaderBoardData[n].rank != 2 && leaderBoardData[n].rank != 3){
                    nextLeaders.push(leaderBoardData[n]);
                }else{
                    topLeaders.push(leaderBoardData[n]);
                }
            }

            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+nextLeaders[i].rank+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].profilePic !=null && nextLeaders[i].profilePic !="" && nextLeaders[i].profilePic!=undefined){
                    lbHtml +="<img src='/funlearn/showProfileImage?id="+nextLeaders[i].userId+"&fileName="+nextLeaders[i].profilePic+"&type=user&imgType=passport'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].name+"</h3>";
                if (nextLeaders[i].state !=undefined && nextLeaders[i].state !=null && nextLeaders[i].state !=""){
                    lbHtml +=  "<p>"+nextLeaders[i].state+"</p>";
                }
                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].userPoints+"</p>\n"+
                    "<p>Points</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }

            for(var t=0;t<topLeaders.length;t++){
                tHtml +="<div class='item' id='item-"+topLeaders[t].rank+"'>\n"+
                    "<div class='pos'>"+topLeaders[t].rank+"</div>";

                if(topLeaders[t].profilePic !=null && topLeaders[t].profilePic !="" && topLeaders[t].profilePic!=undefined){
                    tHtml +="<div class='pic' style='background-image: url(/funlearn/showProfileImage?id="+topLeaders[t].userId+"&fileName="+topLeaders[t].profilePic+"&type=user&imgType=passport)'></div>";
                }else{
                    tHtml +="<div class='pic' style='background-image: url(/assets/landingpageImages/img_avatar3.png)'></div>";
                }

                tHtml +="<div class='name'>"+topLeaders[t].name+"</div>\n";
                if (topLeaders[t].state !=undefined && topLeaders[t].state !=null && topLeaders[t].state !=""){
                    tHtml +=  "<p>"+topLeaders[t].state+"</p>";
                }
                tHtml +=     "<div class='score'>"+topLeaders[t].userPoints+"</div>";
                tHtml += "<div class=''>\n"+
                    "<img src='/assets/prepJoy/"+topLeaders[t].rank+".svg' alt='' class='badgeImg'>\n"+
                    "</div>\n"+
                    "</div>";
            }

            document.querySelector('#nextList').innerHTML = lbHtml;
            document.querySelector('.top3').innerHTML = tHtml;
            $('.top3').css('margin-bottom','20px');
        }else{
            var tHtml = "";
            tHtml +="<div class='d-flex flex-column justify-content-center align-items-center'><h2>Not many leaders?</h2>\n"+
                    "<p>Play and top the chart.</p></div>";
            document.querySelector('#nextList').innerHTML = '';
            document.querySelector('.top3').innerHTML = tHtml; // can be changed
            $('.top3').css('margin-bottom','75px');
        }

        $("#item-1").addClass('one');
        $("#item-2").addClass('two');
        $("#item-3").addClass('three');
    }

    $( window ).on( "load", function (){
        $('.loading-icon').removeClass('hidden');
    });
    $( document ).ready(function() {
        $('.loading-icon').addClass('hidden');
    });
</script>
</body>
</html>
