<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">

<style>
.bk_grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.bk_wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px;
    padding:5px 5px 10px 5px;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease-in-out;
    border: 1px solid #f0f0f0;
    max-width: 200px;
    max-height: 300px;
}

.bk_wrap:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.bk_cover-wrap {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: relative;
    overflow: hidden;
    border-radius: 5px;
}

.bk_cover {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.3s ease-in-out;
}

.bk_wrap:hover .bk_cover {
    transform: scale(1.05);
}

.bk_title-wrap {
    text-align: center;
    margin-top: 12px;
}

.bk_title {
    font-size: 18px;
    font-weight: 500;
    color: #212529;
    margin: 8px 0;
    max-width: 190px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bk_addBtn {
    padding: 10px 18px;
    border: 2px solid #ff6f61;
    border-radius: 50px;
    background-color: #ff6f61;
    color: white;
    font-size: 14px;
    cursor: pointer;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.bk_addBtn:hover {
    background-color: white;
    color: #ff6f61;
}
.md_cv{
    width: 200px;
    margin: 0 auto;
    padding: 10px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}
.md_cv img{
    width: 100%;
}
/* Responsive for mobile devices */
@media (max-width: 1024px) {
    .bk_grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .bk_grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .bk_grid {
        grid-template-columns: 1fr;
    }
}
</style>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="bk_grid"></section>
<div class="modal fade confirmation__modal modal-modifier" id="confirmation-modal" style="z-index: 999999!important;">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close mr-2" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div class="md_cv">
                    <img src="" alt="" id="md_coverImg">
                </div>
                <h5>Get this book</h5>
                <div class="d-flex justify-content-center py-3 col-12">
                    <button type="button" class="okBtn btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">No</button>
                    <button type="button" class="okBtn btn btn-lg btn-danger btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" onclick="addBooks()">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script>
    $('.loading-icon').removeClass('hidden');
    const bk_list = document.querySelector('.bk_grid');
    var bookId;
    function getBooks(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="privatelabel" action="getBooksForCampaign"  onSuccess='showBooks(data);' params="'campaignCode=${params.campaignCode}&accessCode=${params.accessCode}'"/>
    }

    function showBooks(data){
        let books = data.books;
        let htmlContent = ""
        books = books.slice(0,10)
        books.forEach(book=>{
            const title = book.title
            const coverImg = "/funlearn/showProfileImage?id="+book.id+"&fileName="+book.coverImage+"&type=books&imgType=passport"
            htmlContent +='<div class="bk_wrap">' +
                '<div class="bk_cover-wrap">' +
                '<img src="'+coverImg+'" class="bk_cover">' +
                '</div>' +
                '<div class="bk_title-wrap">' +
                '<p class="bk_title">'+title+'</p>' +
                '<button onclick="select(' + book.id + ', \'' + book.coverImage + '\')" class="bk_addBtn">Select Book</button>'+
                '</div>' +
                '</div>';
        })
        bk_list.innerHTML = htmlContent;
        $('.loading-icon').addClass('hidden');
    }
    getBooks()
    function addBooks(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="privatelabel" action="addBookToUser"  onSuccess='showConfirm(data);' params="'campaignCode=${params.campaignCode}&accessCode=${params.accessCode}&bookId='+bookId"/>

    }
    function showConfirm(data){
        var btnText, btnColor, redirectUrl;
        redirectUrl = "/wsLibrary/myLibrary";
        btnText = "Go to My books";
        btnColor = "#367AD8";
        if(data.status=='allocated') {
            swal({
                title: "Book Access Granted!",
                text: "Book successfully added to your library.",
                type: "success",
                allowOutsideClick: false,
                showConfirmButton: true,
                showCancelButton: false,
                confirmButtonColor: btnColor,
                confirmButtonText: btnText,
                cancelButtonText: "Cancel",
                closeOnConfirm: true,
                closeOnCancel: false,
                allowEscapeKey: false,
                customClass: '',
            }, function () {
                $('.loading-icon').removeClass('hidden');
                window.location.href = redirectUrl;
            });
        }else{
            alert("Error: "+data.status);
        }
    }

    function select(id,cover){
        bookId = id;
        const md_coverImg = document.getElementById('md_coverImg')
        md_coverImg.src = "/funlearn/showProfileImage?id="+id+"&fileName="+cover+"&type=books&imgType=passport"
        $("#confirmation-modal").modal("show")
    }
</script>
<g:render template="/${session['entryController']}/footer_new"></g:render>
