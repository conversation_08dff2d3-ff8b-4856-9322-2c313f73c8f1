<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
    .custom_container_new{
        width: calc(100% - 10%);
        margin: 0 auto;
    }
    td{
        vertical-align: top;
        min-width: 80px;
    }
    h4 {
        color: #6D5BD0;
        font-size: 1.5em;
        margin-bottom: 20px;
        border-bottom: 2px solid #D9D5EC;
        padding-bottom: 10px;
    }

    form {
        max-width: 600px;
        margin: 0 auto;
        background: #FFFFFF;
        padding: 20px;
        border: 1px solid #D9D5EC;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    form div {
        margin-bottom: 15px;
    }

    label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #6E6893;
    }

    input[type="text"], textarea, select,
    input[type="file"]{
        width: 100%;
        padding: 10px;
        border: 1px solid #D9D5EC;
        border-radius: 4px;
        font-size: 14px;
        color: #212529;
        background-color: #FDFDFE;
    }

    textarea {
        resize: vertical;
    }

    select {
        background-color: #FFFFFF;
    }

    input[type="text"]:focus, textarea:focus, select:focus {
        border-color: #6D5BD0;
        outline: none;
        box-shadow: 0 0 3px #6D5BD0;
    }

    button, input[type="submit"] {
        background-color: #6D5BD0;
        color: #FFFFFF;
        border: none;
        border-radius: 4px;
        padding: 10px 15px;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    button:hover, input[type="submit"]:hover {
        background-color: #23313E;
    }

    .no-selection {
        color: #999999;
    }

    .file-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>
<div style="min-height: 74vh">

    <div class="custom_container_new" style="margin-bottom: 2rem; margin-top: 1rem;">
<h2>Prompts List &nbsp;&nbsp;<a href="/promptLanguages/index" class="btn btn-primary">Manage Prompt Languages</a></h2>
<br>
<h4>Add New Prompt</h4>
<g:form action="save" enctype="multipart/form-data">
    <div>
        <label for="promptType">Prompt Type:</label>
        <g:textField name="promptType" size="40"/>
    </div>
    <div>
        <label for="promptLabel">Prompt Label:</label>
        <g:textField name="promptLabel" size="40" />
    </div>
    <div>
        <label for="basePrompt">Base Prompt:</label>
        <g:textArea name="basePrompt" rows="5" cols="80" />
    </div>
    <div>
        <label for="sortOrder">Sort:</label>
        <g:textField name="sortOrder" />
    </div>
    <!--Copilot: Create a drop down for parent prompt-->
    <div>
        <label for="parentPromptType">Parent Prompt:</label>
        <g:select name="parentPromptType" from="${parentPrompts}" optionKey="promptType" optionValue="promptLabel" noSelection="['':'-Select-']"/>
    </div>
    <div>
        <label for="icon">Upload Icon:</label>
        <input type="file" name="icon" id="icon"/>
    </div>
    <div>
        <label>Is Default Prompt:</label>
        <label>
            <input type="radio" name="isDefault" value="Yes" /> Yes
        </label>
        <label>
            <input type="radio" name="isDefault" value="No"  checked/> No
        </label>
    </div>
    <div>
        <g:submitButton name="create" value="Add Prompt" />
    </div>
</g:form>

<h4>Existing Prompts</h4>
<table>
    <thead>
    <tr>
        <th>Prompt Type</th>
        <th>Prompt Label</th>
        <th>Base Prompt</th>
        <th>Parent Prompt</th>
        <th>Sort</th>
        <th>Icon</th>
        <th>Actions</th>
        <th></th>
        <th>Add to Default Prompts</th>
    </tr>
    </thead>
    <tbody>
    <g:each in="${prompts}" var="prompt">

        <tr>
        <g:form action="edit" method="post"  name="editForm_${prompt.id}" id="editForm_${prompt.id}">
            <input type="hidden" name="id" value="${prompt.id}"/>
            <td>
                <g:textField name="promptType" value="${prompt.promptType}" />
            </td>
            <td>
                <g:textField name="promptLabel" value="${prompt.promptLabel}" />
            </td>
            <td>
                <g:textArea rows="5" cols="50" name="basePrompt" value="${prompt.basePrompt}" />
            </td>
            <td>
                <g:select style="width: 160px" name="parentPromptType" from="${parentPrompts}" optionKey="promptType" optionValue="promptLabel" value="${prompt.parentPromptType}" noSelection="['':'-Select-']"/>
            </td>
            <td style="width: 100px;">
                <g:textField name="sortOrder" value="${prompt.sortOrder}" />
            </td>
            <td>
                <img src="/prompt/showPromptIcon?promptId=${prompt.id}&fileName=${prompt.iconPath}" width="20px"/>
            </td>
            <td>
                <g:submitButton name="update" value="Update" onClick="editPrompt(${prompt.id}); return false;"/>
            </td>
        </g:form>
            <td>
                <g:form action="delete" method="post" style="display:inline;background: transparent !important;border: none!important;box-shadow: none!important;">
                    <input type="hidden" name="id" value="${prompt.id}"/>
                    <g:submitButton name="delete" value="Delete"/>
                </g:form>
            </td>
            <td>
                <%
                    boolean isDefault  = false
                    if("Yes".equals(prompt.isDefault)) isDefault = true
                %>
                <label style="display:inline-block !important;">
                    <input type="radio" name="isDefaultPrompt_${prompt.id}" value="Yes" <%= isDefault?"checked":"" %>/> Yes
                </label>
                <label style="display:inline-block !important;">
                    <input type="radio" name="isDefaultPrompt_${prompt.id}" value="No" <%= !isDefault?"checked":"" %> /> No
                </label>
            </td>
        </tr>

    </g:each>
    </tbody>
</table>
</div>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    function editPrompt(id) {

      var form = $('#editForm_' + id);
        var data = form.serialize();
        console.log("data="+data);
        console.log("&*****");
        $.ajax({
            type: 'POST',
            url:'/prompt/update',
            data: data,
            success: function(response) {
                alert(response);
            }
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        const radioButtons = document.querySelectorAll('input[type="radio"][name^="isDefaultPrompt_"]');

        radioButtons.forEach(radio => {
            radio.addEventListener("change", async function () {
                const promptId = this.name.split("_")[1];
                const isDefault = this.value;

                console.log("Prompt ID: "+promptId+" Is Default: "+isDefault);

                try {
                    const result = await updateDefaultPrompt(promptId, isDefault);
                    console.log(result);
                    alert(result)
                } catch (error) {
                    console.error(error);
                }
            });
        });

        async function updateDefaultPrompt(promptId, isDefault) {
            $('.loading-icon').removeClass('hidden');
            const url = "/prompt/updateDefaultPrompt?promptId="+promptId+"&isDefault="+isDefault;
            try {
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error("Failed to update prompt "+promptId+": "+response.statusText);
                }
                const result = await response.json()
                $('.loading-icon').addClass('hidden');
                return result.message
            } catch (error) {
                throw new Error("Error updating prompt "+promptId+": "+error.message);
            }
        }
    });

</script>

