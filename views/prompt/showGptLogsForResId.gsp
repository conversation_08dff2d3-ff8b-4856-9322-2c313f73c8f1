<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>

<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- DataTables CSS - Simplified -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<!-- DateRangePicker CSS - Simplified -->
<link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
<!-- FontAwesome -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<style>
/* General styling */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 16px 20px;
}

.card-header h5 {
    font-weight: 600;
    color: #333;
}

/* Table styling */
.dataTables_wrapper {
    padding: 15px;
}

table.dataTable {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    margin-bottom: 1rem;
    border-radius: 6px;
    overflow: hidden;
    font-size: 14px;
}

table.dataTable thead th {
    background-color: #f1f5f9;
    color: #333;
    font-weight: 600;
    padding: 12px 15px;
    border-bottom: 2px solid #dee2e6;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

table.dataTable tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
    color: #444;
}

table.dataTable tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Pagination styling */
.dataTables_paginate .pagination {
    margin-bottom: 0;
    padding: 10px 0;
}

.page-item.active .page-link {
    background-color: #4a6cf7;
    border-color: #4a6cf7;
}

.page-link {
    color: #4a6cf7;
    padding: 8px 12px;
    margin: 0 2px;
    border-radius: 4px;
}

/* Filter and search styling */
.dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    margin-left: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.dataTables_length select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    margin: 0 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Loading spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25em;
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
    }

    .filter-group {
        margin-top: 10px;
        width: 100%;
    }

    .dataTables_wrapper {
        padding: 10px;
    }

    table.dataTable {
        font-size: 13px;
    }

    table.dataTable thead th {
        padding: 10px;
    }

    table.dataTable tbody td {
        padding: 10px;
    }
}

/* Improved truncated text with show more/less */
.truncated-text {
    position: relative;
    max-height: 80px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.truncated-text.expanded {
    max-height: none;
}

.show-more-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 30%);
    padding: 2px 8px;
    cursor: pointer;
    color: #4a6cf7;
    font-weight: 500;
    font-size: 12px;
    border-radius: 4px;
    width: 100px;
    text-align: right;
}

.show-more-btn:hover {
    text-decoration: underline;
}

/* Date range picker styling */
.daterangepicker {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: none;
}

.daterangepicker .calendar-table {
    border-radius: 6px;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: #4a6cf7;
}

.daterangepicker .drp-buttons .btn {
    border-radius: 4px;
    font-weight: 500;
}

/* Export button styling */
#exportBtn {
    background-color: #10b981;
    border-color: #10b981;
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

#exportBtn:hover {
    background-color: #0ea271;
    border-color: #0ea271;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Date filter styling */
.date-filter .input-group {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.date-filter .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.date-filter .form-control {
    border-left: none;
}
</style>

<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">GPT Logs</h5>
                        <p class="text-muted mb-0 mt-1"><i class="fas fa-link me-1"></i>Resource ID: <strong>${params.resId}</strong></p>
                    </div>
                    <div class="filter-group d-flex align-items-center">
                        <div class="date-filter me-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="text" id="dateRangeFilter" class="form-control" placeholder="Select date range">
                            </div>
                        </div>
                        <button id="exportBtn" class="btn btn-success">
                            <i class="fas fa-file-export me-1"></i> Export CSV
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="gptLogsTable" class="table table-striped table-hover" style="width:100%">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th width="20%">User Prompt</th>
                                    <th width="25%">Response</th>
                                    <th>Feedback</th>
                                    <th>Resource</th>
                                    <th>Chapter</th>
                                    <th>Book</th>
                                    <th>Date & Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="spinner-overlay d-none">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<!-- Toast Container for Notifications -->
<div class="toast-container"></div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<!-- Bootstrap 5 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery (required for DataTables) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS - Simplified -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<!-- DateRangePicker JS -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- FileSaver JS for Export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>

</body>
</html>

<script>
$(document).ready(function() {
    // Initialize variables
    var startDate = null;
    var endDate = null;
    var toastCounter = 0;

    // Initialize DateRangePicker with Indian date format
    $('#dateRangeFilter').daterangepicker({
        opens: 'left',
        autoUpdateInput: false,
        locale: {
            cancelLabel: 'Clear',
            format: 'DD/MM/YYYY',
            applyLabel: 'Apply',
            daysOfWeek: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
            monthNames: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
        },
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });

    // Handle date range selection
    $('#dateRangeFilter').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
        startDate = picker.startDate.format('YYYY-MM-DD'); // Backend format
        endDate = picker.endDate.format('YYYY-MM-DD');     // Backend format
        table.ajax.reload();
    });

    $('#dateRangeFilter').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('');
        startDate = null;
        endDate = null;
        table.ajax.reload();
    });

    // Initialize DataTable with simplified configuration
    var table = $('#gptLogsTable').DataTable({
        processing: true,
        serverSide: true,
        searching: true,      // Enable search
        ordering: true,       // Enable ordering
        paging: true,         // Enable pagination
        info: true,           // Show info
        lengthChange: true,   // Show length change
        autoWidth: false,     // Disable auto width
        ajax: {
            url: '${createLink(controller: 'prompt', action: 'findGptLogsForResId')}',
            type: 'GET',
            data: function(d) {
                d.resId = '${params.resId}';
                d.startDate = startDate;
                d.endDate = endDate;
                return d;
            },
            dataSrc: function(json) {
                return json.gptLogs.map(function(log) {
                    if (!log) return Array(9).fill(''); // Handle null logs

                    return [
                        // Name with link
                        '<a href="/prompt/showGptLogsForUser?username=' + (log.username || '') + '&name=' + (log.name || '') + '" class="text-primary fw-medium">' + (log.name || '-') + '</a>',

                        // Type with badge
                        log.promptType ? '<span class="badge bg-info text-white">' + log.promptType + '</span>' : '-',

                        // User Prompt with improved formatting
                        formatLongText(log.userPrompt ? log.userPrompt.replace(/\\text\{|\}/g, '').replace(/<\/?p>/g, '') : '-'),

                        // Response with improved formatting
                        formatLongText(log.response || '-'),

                        // Feedback Type with colored badge
                        log.feedbackType ? '<span class="badge bg-' + getFeedbackBadgeColor(log.feedbackType) + ' text-white">' + log.feedbackType + '</span>' : '-',

                        // Resource Link with icon
                        log.readingMaterialResId ? '<a href="/prompt/showGptLogsForResId?resId=' + log.readingMaterialResId + '" class="btn btn-sm btn-outline-secondary"><i class="fas fa-link me-1"></i>' + (String(log.readingMaterialResId).length > 8 ? String(log.readingMaterialResId).substring(0, 8) + '...' : log.readingMaterialResId) + '</a>' : '-',

                        // Chapter with ellipsis for long names
                        log.chapterName ? '<span title="' + log.chapterName + '">' + (String(log.chapterName).length > 20 ? String(log.chapterName).substring(0, 20) + '...' : log.chapterName) + '</span>' : '-',

                        // Book with ellipsis for long titles
                        log.bookTitle ? '<span title="' + log.bookTitle + '">' + (String(log.bookTitle).length > 20 ? String(log.bookTitle).substring(0, 20) + '...' : log.bookTitle) + '</span>' : '-',

                        // Date formatted in Indian format with IST timezone
                        log.dateCreated
                    ];
                }).filter(function(row) { return row[0] != ''; }); // Filter out null logs
            },
            error: function(xhr, error, thrown) {
                showToast('Error loading data. Please try again.', 'error');
                $('#loadingSpinner').addClass('d-none');
            }
        },
        columns: [
            { title: 'Name' },
            { title: 'Type' },
            { title: 'User Prompt' },
            { title: 'Response' },
            { title: 'Feedback Type' },
            { title: 'Resource Link' },
            { title: 'Chapter' },
            { title: 'Book' },
            { title: 'Date' }
        ],
        order: [[8, 'desc']], // Sort by date descending
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
            emptyTable: '<div class="text-center py-4"><i class="fas fa-info-circle text-info me-2"></i>No logs found</div>',
            info: 'Showing _START_ to _END_ of _TOTAL_ logs',
            infoEmpty: 'No logs available',
            infoFiltered: '(filtered from _MAX_ total logs)',
            lengthMenu: 'Show _MENU_ logs',
            search: '<i class="fas fa-search me-1"></i>',
            searchPlaceholder: 'Search logs...',
            zeroRecords: '<div class="text-center py-4"><i class="fas fa-search text-warning me-2"></i>No matching logs found</div>',
            paginate: {
                first: '<i class="fas fa-angle-double-left"></i>',
                last: '<i class="fas fa-angle-double-right"></i>',
                next: '<i class="fas fa-angle-right"></i>',
                previous: '<i class="fas fa-angle-left"></i>'
            }
        },
        dom: '<"row mb-3"<"col-md-6"l><"col-md-6"f>>rt<"row mt-3"<"col-md-6"i><"col-md-6"p>>',
        drawCallback: function() {
            // Initialize show more/less buttons
            $('.show-more-btn').off('click').on('click', function() {
                var container = $(this).closest('.truncated-text');
                container.toggleClass('expanded');
                $(this).text(container.hasClass('expanded') ? 'Show Less' : 'Show More');
            });

            // Add custom styling to search input
            $('.dataTables_filter input').addClass('search-input');

            // Add tooltip to truncated text
            $('[title]').tooltip({
                placement: 'top',
                trigger: 'hover'
            });
        },
        initComplete: function() {
            // Add custom class to search input
            $('.dataTables_filter input')
                .attr('placeholder', 'Search logs...')
                .css('width', '250px');
        }
    });

    // Export functionality
    $('#exportBtn').on('click', function() {
        showLoading();

        // Get all data for export
        $.ajax({
            url: '${createLink(controller: 'prompt', action: 'findGptLogsForResId')}',
            type: 'GET',
            data: {
                resId: '${params.resId}',
                max: 1000, // Get a large number of records
                offset: 0,
                startDate: startDate,
                endDate: endDate
            },
            success: function(data) {
                if (data.gptLogs && data.gptLogs.length > 0) {
                    exportToCSV(data.gptLogs);
                } else {
                    showToast('No data to export', 'warning');
                }
                hideLoading();
            },
            error: function() {
                showToast('Error exporting data. Please try again.', 'error');
                hideLoading();
            }
        });
    });

    // Helper Functions

    // Get appropriate badge color based on feedback type
    function getFeedbackBadgeColor(feedbackType) {
        if (!feedbackType) return 'secondary';

        feedbackType = feedbackType.toLowerCase();

        if (feedbackType.includes('positive') || feedbackType.includes('good')) {
            return 'success';
        } else if (feedbackType.includes('negative') || feedbackType.includes('bad')) {
            return 'danger';
        } else if (feedbackType.includes('neutral')) {
            return 'info';
        } else {
            return 'secondary';
        }
    }

    function formatLongText(text) {
        if (!text || text === '-') return '-';

        // Clean up the text
        text = text.replace(/\n/g, ' ').trim();

        if (text.length <= 100) return text;

        // Create truncated text with gradient fade and show more button
        return '<div class="truncated-text">' +
               text +
               '<span class="show-more-btn">Show More</span>' +
               '</div>';
    }

    // Convert GMT to IST (GMT+5:30) and format as dd/mm/yyyy hh:mm
    function formatDate(dateString) {
        if (!dateString) return '-';

        // Parse the date string and add 5 hours and 30 minutes for IST
        var date = moment(dateString).add(5, 'hours').add(30, 'minutes');

        // Format in Indian style: DD/MM/YYYY HH:MM
        return date.format('DD/MM/YYYY HH:mm');
    }

    function exportToCSV(data) {
        // Create CSV content
        var csvContent = 'Name,Type,User Prompt,Response,Feedback Type,Resource Link,Chapter,Book,Date\n';

        data.forEach(function(log) {
            if (!log) return; // Skip null logs

            // Clean and escape fields for CSV
            var row = [
                escapeCsvField(log.name || ''),
                escapeCsvField(log.promptType || ''),
                escapeCsvField(log.userPrompt ? log.userPrompt.replace(/\\text\{|\}/g, '').replace(/<\/?p>/g, '') : ''),
                escapeCsvField(log.response || ''),
                escapeCsvField(log.feedbackType || ''),
                escapeCsvField(log.readingMaterialResId || ''),
                escapeCsvField(log.chapterName || ''),
                escapeCsvField(log.bookTitle || ''),
                escapeCsvField(log.dateCreated)
            ];

            csvContent += row.join(',') + '\n';
        });

        // Create and download the file
        var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        var filename = 'gpt_logs_' + moment().format('YYYYMMDD_HHmmss') + '.csv';
        saveAs(blob, filename);

        showToast('Export completed successfully', 'success');
    }

    function escapeCsvField(field) {
        if (field == null || field == undefined) return '';

        // Convert to string and escape quotes
        var stringField = String(field);
        var escapedField = stringField.replace(/"/g, '""');

        // Wrap in quotes if contains comma, newline, or quotes
        if (escapedField.includes(',') || escapedField.includes('\n') || escapedField.includes('"')) {
            return '"' + escapedField + '"';
        }

        return escapedField;
    }

    function showLoading() {
        $('#loadingSpinner').removeClass('d-none');
    }

    function hideLoading() {
        $('#loadingSpinner').addClass('d-none');
    }

    function showToast(message, type) {
        // Generate unique ID for the toast
        var toastId = 'toast-' + Date.now();

        // Determine the background color based on the type
        var bgClass = 'bg-success';
        if (type == 'error') {
            bgClass = 'bg-danger';
        } else if (type == 'warning') {
            bgClass = 'bg-warning text-dark';
        }

        // Create toast HTML with icon
        var toastHtml =
            '<div id="' + toastId + '" class="toast align-items-center text-white ' + bgClass + '" role="alert" aria-live="assertive" aria-atomic="true">' +
                '<div class="d-flex">' +
                    '<div class="toast-body">' +
                        '<i class="fas fa-' + (type == 'success' ? 'check-circle' : (type == 'warning' ? 'exclamation-triangle' : 'times-circle')) + ' me-2"></i>' +
                        message +
                    '</div>' +
                    '<button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>' +
                '</div>' +
            '</div>';

        $('.toast-container').append(toastHtml);

        var toastElement = new bootstrap.Toast(document.getElementById(toastId), {
            autohide: true,
            delay: 5000
        });

        toastElement.show();

        // Remove toast from DOM after it's hidden
        $('#' + toastId).on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
});
</script>
