<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h1 class="text-center">Add Question Type</h1>
                    <form action="${createLink(controller: 'QuestionPaper', action: 'saveQuestionType')}" method="POST">
                        <input type="hidden" name="sectionId" value="${sectionId}">
                        <div class="mb-3">
                            <label for="type" class="form-label">Question Type</label>
                            <select class="form-control" id="type" name="type" required>
                                <option value="" disabled selected>Select question type</option>
                                <g:each in="${questionTypes}" var="questionType">
                                    <option value="${questionType.questionType}">${questionType.questionType}</option>
                                </g:each>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="numberOfQuestions" class="form-label">Number of Questions</label>
                            <input type="number" class="form-control" id="numberOfQuestions" name="numberOfQuestions" required>
                        </div>
                        <div class="mb-3">
                            <label for="marksPerQuestion" class="form-label">Marks per Question</label>
                            <input type="number" class="form-control" id="marksPerQuestion" name="marksPerQuestion">
                        </div>
                        <button type="submit" class="btn btn-primary">Add Question Type</button>
                    </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
