<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h2 class="text-center">Question Paper Creator</h2>
                    <form action="${createLink(controller: 'QuestionPaper', action: 'createPattern')}" method="POST">
                        <div class="mb-3">
                            <label for="questionPaperName" class="form-label">Pattern Name</label>
                            <input type="text" class="form-control" id="questionPaperName" name="questionPaperName" placeholder="Enter pattern name" value="${bookTitle}" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Enter description (optional)"></textarea>
                        </div>
                        <g:if test="${params.bookId}">
                            <div class="mb-3">
                                <label class="form-label">Book</label>
                                <div class="form-control bg-light">${bookTitle ?: 'Book ID: ' + params.bookId}</div>
                                <input type="hidden" name="bookId" value="${params.bookId}">
                            </div>
                        </g:if>

                        <button type="submit" class="btn btn-primary">Create Pattern</button>
                    </form>

                    <hr>

                    <h2>Existing Patterns</h2>
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>#</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <g:each in="${patterns}" var="pattern" status="i">
                            <tr>
                                <td>${i + 1}</td>
                                <td>${pattern.name}</td>
                                <td>${pattern.description ?: 'N/A'}</td>
                                <td>${pattern.name}</td>
                                <td>
                                    <a href="${createLink(controller: 'QuestionPaper', action: 'viewPattern', params: [patternId: pattern.id])}" class="btn btn-secondary btn-sm">View</a>
                                    <a href="${createLink(controller: 'QuestionPaper', action: 'deletePattern', params: [patternId: pattern.id])}" class="btn btn-danger btn-sm">Delete</a>
                                </td>
                            </tr>
                        </g:each>
                        </tbody>
                    </table>
                </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
