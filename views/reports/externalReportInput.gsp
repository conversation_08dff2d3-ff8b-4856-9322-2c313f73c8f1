<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">

<div class="container-fluid  my-5 px-5 publishing_sales">

    <h2>Integration Sales Report</h2>
   <div class="row">
    <div class="form-group" class="form-group col-md-3">
        <label for="fromDate">Date from:</label>
        <input type="date" id="fromDate" name="fromDate" class="form-control">
    </div>
    <div class="form-group" class="form-group col-md-3">
        <label for="toDate">Date to:</label>
        <input type="date" id="toDate" name="toDate" class="form-control">
    </div>

    <div class="form-group" class="form-group col-md-3">
        <label for="status">Status:</label>
        <select id="status" name="status" class="form-control">
            <option value="All">All</option>
            <option value="Purchased">Purchased</option>
            <option value="Cancelled">Cancelled</option>
        </select>
    </div>
    <% if("1".equals(""+session["siteId"])) {%>
    <div class="form-group col-md-3">
        <label for="siteId"><strong>Sites</strong></label>
        <g:select id="siteId" class="form-control w-100" optionKey="id" optionValue="clientName"
                  value="" name="siteId" from="${sites}" noSelection="['':'All']"/>
    </div>
    <%  } else{%>
    <input type="hidden" name="siteId" id="siteId" value="${session["siteId"]}">
    <%}%>
       <div class="form-group" class="form-group col-md-3">
           <label for="paymentReference">Order id / Payment reference:</label>
           <input type="text" id="paymentReference" name="paymentReference" class="form-control">
       </div>
   </div>
    <button id="search" class="btn btn-primary">Search</button>
    <button id="download" class="btn btn-secondary">Download</button>

</div>
<div class="container-fluid  my-5 px-5 publishing_sales">
<div id="outsidePurchaseTable" class="mt-3" style="display: none">
    <table class="table table-striped">
        <thead>
        <tr>
            <th>Site Id</th>
            <th>Name</th>
            <th>Mobile</th>
            <th>Email</th>
            <th>Price</th>
            <th>Payment Reference</th>
            <th>Date Created</th>
            <th>Isbns</th>
            <th>Status</th>
            <th>Missed Isbns</th>
            <th></th>
        </tr>
        </thead>
    </table>
</div>
    <div id="totalCount" class="mt-3"></div>
<div id="pagination" class="mt-3">
    <nav aria-label="Page navigation example">
        <ul class="pagination">
        </ul>
    </nav>
</div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script>
    var perPage = 10;

    $(document).ready(function() {
        $('#search').click(function() {
            var fromDate = $('#fromDate').val();
            var toDate = $('#toDate').val();
            var siteId = $('#siteId').val();
            var status = $('#status').val();
            var paymentReference = $('#paymentReference').val();
             getOutsidePurchases(1, fromDate, toDate, siteId, status,paymentReference);
        });

        $('#download').click(function(){
            var fromDate = $('#fromDate').val();
            var toDate = $('#toDate').val();
            var siteId = $('#siteId').val();
            var status = $('#status').val();
            var paymentReference = $('#paymentReference').val();
            window.location.href = "${createLink(controller: 'reports', action: 'downloadPurchases')}?fromDate=" + fromDate + "&toDate=" + toDate + "&siteId=" + siteId + "&status=" + status+"&paymentReference="+paymentReference;
        });
    });

    function getOutsidePurchases(page, fromDate, toDate, siteId, status,paymentReference) {
        $.get("${createLink(controller: 'reports', action: 'getPurchases')}", { max: 10, page: page, fromDate: fromDate, toDate: toDate, siteId: siteId, status: status,paymentReference:paymentReference }, function(response) {
            $('#outsidePurchaseTable table tr:not(:first)').remove();
            $.each(response.purchases, function(index, purchase) {
                var row = '<tr><td>'+purchase.siteId+'</td><td>'+purchase.name+'</td><td>'+(purchase.mobile==null?"":purchase.mobile)+'</td><td>'+(purchase.email==null?"":purchase.email)+'</td><td>'+purchase.price+'</td><td>'
                    +purchase.paymentReference+'</td><td>'+formatDate(purchase.dateCreated)+'</td><td>'+(purchase.isbns==null?"":purchase.isbns)+'</td><td>'+(purchase.status==null?"":purchase.status)+'</td><td>'
                    +(purchase.missedIsbns==null?"":purchase.missedIsbns)+'</td><td> <a href=\"javascript:getDirectLink('+purchase.id+');\")>Get direct link</a></td></tr>';
                $('#outsidePurchaseTable table').append(row);
            });
            paginate(response.total, 10, page);
            document.getElementById("totalCount").innerHTML="<b>Total orders : "+response.total+"</b>"
            $("#outsidePurchaseTable").show();
        });
    }
  function getDirectLink(purchaseId){
      <g:remoteFunction controller="reports" action="getDirectLink"  onSuccess='showDirectLink(data);'
        params="'purchaseId='+purchaseId"/>
  }

  function showDirectLink(data){
        alert(data.directUrl);
  }
    function paginate(totalCount, perPage, currentPage) {
        var totalPages = Math.ceil(totalCount / perPage);
        var paginationHtml = '';
        var visiblePages = 5; // Number of pages to show at a time

        var startPage = Math.max(1, currentPage - Math.floor(visiblePages / 2));
        var endPage = Math.min(totalPages, startPage + visiblePages - 1);

        if (endPage - startPage + 1 < visiblePages) {
            startPage = Math.max(1, endPage - visiblePages + 1);
        }

        // Previous button
        if (currentPage > 1) {
            paginationHtml += '<li class="page-item"><a class="page-link" href="#" data-page="' + (currentPage - visiblePages) + '">Previous</a></li>';
        }

        for (var i = startPage; i <= endPage; i++) {
            paginationHtml += '<li class="page-item' + (i === currentPage ? ' active' : '') + '"><a class="page-link" href="#" data-page="' + i + '">' + i + '</a></li>';
        }

        // Next button
        if (currentPage < totalPages) {
            paginationHtml += '<li class="page-item"><a class="page-link" href="#" data-page="' + (currentPage + visiblePages) + '">Next</a></li>';
        }

        $('#pagination ul').html(paginationHtml);

        $('#pagination a').click(function() {
            var page = parseInt($(this).data('page'));
            getOutsidePurchases(page, $('#fromDate').val(), $('#toDate').val(), $('#siteId').val(), $('#status').val(),$('#paymentReference').val());
        });
    }



    function formatDate(dateStr) {
        var d = new Date(dateStr),
            day = '' + d.getDate(),
            month = '' + (d.getMonth() + 1),
            year = d.getFullYear(),
            hour = '' + d.getHours(),
            minute = '' + d.getMinutes();

        if (month.length < 2) month = '0' + month;
        if (day.length < 2) day = '0' + day;
        if (hour.length < 2) hour = '0' + hour;
        if (minute.length < 2) minute = '0' + minute;

        return [day, month, year].join('/') + ' ' + [hour, minute].join(':');
    }

</script>

</body>
</html>
