<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.18/css/bootstrap-select.min.css"/>
<asset:stylesheet href="wonderslate/addNotes.css"/>
<asset:stylesheet href="ckeditor5/styles.css"/>
<%if(("ios".equals(session["appType"]))){%>
<style>
    input,textarea{
        font-size: 16px !important;
    }
</style>
<%}%>
<form class="form-horizontal" enctype="multipart/form-data" role="form" name="addhtml" id="addhtml" action="/resourceCreator/addHTML" method="post">
                        <input type="hidden" name="resourceType">
                        <input type="hidden" name="mode">
                        <input type="hidden" name="resourceDtlId">
                        <input type="hidden" name="htmlId">
                        <input type="hidden" name="quizMode" value="${params.quizMode}">
                        <input type="hidden" name="subject">
                       <%if(chapterId!=null){%>
                        <input type="hidden" name="chapterId" value="${chapterId}">
                        <input type="hidden" name="page" value="notes">
                        <input type="hidden" name="bookId" value="${params.bookId}">

                        <%}%>
                        <input type="hidden" name="folderId" value="${params.folderId}">
                        <input type="hidden" name="resourceName">
                        <input type="hidden" name="notes">

</form>
    <div class="container create_notes_section">
        <div class="row align-items-center pt-3 pt-lg-5 pb-3 mx-0">
            <h4 class="text-radial-gradient pr-2 mb-0" id="resourceNameDiv"></h4>
            <a id="editNotesBtn" href="javascript:editName();" class="align-items-start edit_notes hidden">
                <i class="material-icons">edit</i>
            </a>
        </div>
        <div class="row row-editor mx-0 mb-3">
            <div class="editor card w-100 border">

            </div>
        </div>
        <p id="invalidEditor" class="form-text text-danger hidden mb-3">You can not save empty notes.</p>
        <div class="text-center"><button id="submit" class="btn btn-lg btn-success col-6 col-md-3 col-lg-2" onclick="formSubmit();">SAVE</button></div>
    </div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<%if(("ios".equals(session["appType"]))){%>
<asset:javascript src="landingpage/jquery-3.2.1.min.js" />
<%}%>
<div class="modal fade" id="namesSection" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header px-4">
                <button type="button" class="close" onclick="javascript:closeNamesModal()"><span>X</span></button>
                <h5 class="modal-title">Add Notes</h5>
            </div>

            <!-- Modal body -->
            <div class="modal-body px-4 py-2">
                <div class="form-group">
                    <label class="control-label">Title</label>
                    <input class="form-control" type='textbox' name='modalResourceName' id='modalResourceName' placeholder="Enter title" autocomplete="off">
                    <small id="invalidName" class="form-text text-danger hidden">Please enter a title</small>
                </div>
                <div class="form-group">
                    <label class="control-label">Select subject</label>
                    <g:select data-show-subtext="true" data-live-search="true" name="subject" id="subject" from="${subjects}" optionKey="name" optionValue="name" value="${selectedSubject}"
                              class='selectpicker form-control selected'  noSelection="['':'Select']" data-none-results-text="Did not find any match"/>
                    <small id="invalidSubject" class="form-text text-danger hidden">Please select subject</small>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer px-0 mt-4">
                <button type="button" class="btn btn-light"  onclick="javascript:closeNamesModal()">Cancel</button>
                    <button type="button" class="btn btn-success"  onclick="javascript:addName()">SAVE</button>
                </div>
            </div>
        </div>
    </div>

</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.18/js/bootstrap-select.min.js"></script>

<script>
    var editor;
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";

    var resourceDtlId,htmlId;


    <%  if("edit".equals(params.mode)){ %>
    getHtmlsData(${params.id});
    resourceDtlId = ${params.id};
    document.getElementById("resourceNameDiv").innerText = htmlDecode("${resourceName}");
    $("#editNotesBtn").removeClass("hidden").addClass("d-flex");
    <%  } %>

    function getHtmlsData(resId){
        $(".loading-icon").removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="getHtmlsFile"  onSuccess="displayHtmls(data);" params="'resId='+resId+'&mode=edit'" />
    }

    // function openNamesModal(){
    //     $('#namesSection').modal('show');
    // }
    function closeNamesModal(){

      if(document.addhtml.resourceName.value=="")  history.back();
      else $('#namesSection').modal('hide');
    }

    function editName(){
        $('#namesSection').modal('show');
        document.getElementById("modalResourceName").value = document.getElementById("resourceNameDiv").innerText;
    }
    function addName(){
       if(document.getElementById("modalResourceName").value&&document.getElementById("subject").selectedIndex>0) {
           document.addhtml.resourceName.value = document.getElementById("modalResourceName").value;
           document.addhtml.subject.value = document.getElementById("subject")[document.getElementById("subject").selectedIndex].value;
           document.getElementById("resourceNameDiv").innerText = document.addhtml.resourceName.value;
           $('#namesSection').modal('hide');

       } else if(document.getElementById("modalResourceName").value == ""){
           $("#invalidName").removeClass('hidden');
       } else {
           $("#invalidSubject").removeClass('hidden');
       }

       if(document.getElementById("modalResourceName").value != "") {
           $("#editNotesBtn").removeClass("hidden").addClass("d-flex");
       }
    }
    function displayHtmls(data){
        $(".loading-icon").addClass('hidden');
        var resLink="${resLink}";

        var replaceStr = "/funlearn/downloadEpubImage"+
            "?source="+resLink.substring(0,resLink.lastIndexOf('/')+ 1)+"extract";


        var htmls = data;
        htmls =  replaceAll(htmls,"src=\"", "src=\"" +replaceStr);
        htmls = replaceAll(htmls,"src: url(\"", "src: url(\"" +replaceStr);
        htmls = replaceAll(htmls,"background-image: url('", "background-image: url('" +replaceStr);
        htmls = htmls.replace(/\\\\/g , '\\');
        htmls = htmls.replace(/\\mathring\ *\{\ *([A-Za-z])\ *\}\ */g , '\\overset\{\\ \\ \\circ\}\{$1\}');
        document.addhtml.resourceName.value="${resourceName}";
        document.addhtml.resourceDtlId.value="${params.id}";

        editor.setData(htmls);

    }
    function formSubmit(){editor.getData();
        if(editor.getData()=="")
        {
            $("#invalidEditor").removeClass('hidden');
        }else{
            $(".loading-icon").removeClass('hidden');
            document.addhtml.notes.value = editor.getData();
            document.addhtml.mode.value=mode;
            document.addhtml.resourceType.value=resourceType;
            document.addhtml.submit();
        }

    }

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }


</script>
<script src="https://cdn.ckeditor.com/ckeditor5/28.0.0/balloon/ckeditor.js"></script>
<script>BalloonEditor
    .create( document.querySelector( '.editor' ), {

        toolbar: {
            items: [
                'bold',
                'italic',
                'link',
                'bulletedList',
                'numberedList',
                '|',
                'outdent',
                'indent',
                '|',
                'undo',
                'redo',
                'heading',
                'fontColor',
                'fontSize'
            ]
        },
        language: 'en',
        licenseKey: '',


    } )

    .then( editor => {
    window.editor = editor;
    $('#namesSection').on('hide.bs.modal', function () {
        editor.editing.view.focus();
    });
} )
.catch( error => {

console.error( error );
} );


</script>

<script>
    $(document).ready(function(){
        <%  if("create".equals(params.mode)){ %>
        //openNamesModal();
        $('#namesSection').modal('show');
        document.getElementById("modalResourceName").value = "${notesName}";
        <%}%>

        $('#namesSection').on('shown.bs.modal', function () {
            $('#modalResourceName').focus();
        });

        $("#subject").on("change", function() {
            $("#invalidSubject").addClass('hidden');
        });

        $("#modalResourceName,.editor").keyup(function () {
            $("#invalidName,#invalidEditor").addClass('hidden');
        });
    });

</script>
