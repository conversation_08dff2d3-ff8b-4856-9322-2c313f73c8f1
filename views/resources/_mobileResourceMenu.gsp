<asset:stylesheet href="wonderslate/mobileFooter.css" async="true"/>
<style>
.footer-menu-popover .modal-dialog {
    bottom: 90px;
}
</style>
<div class="mobile-footer-resource d-none justify-content-around d-lg-none">
    <button type="button" onclick="javascript:viewChapterNotes();">
        <i class="material-icons">notes</i>
        <span>Notes</span> </button>
    <button type="button" onclick="javascript:handleMenuClick('/test-generator')">
        <i class="material-icons">help</i>
        <span>Create Test</span>
    </button>
    <button type="button" onclick="javascript:handleMenuClick('/doubts')">
        <img src="${assetPath(src: 'ws/icon-chats.svg')}" style="width: 24px;">
        <span>Doubts</span>
    </button>
    <button  onclick="javascript:openModal()" data-toggle="modal" data-target="#footer-menu-popover">
        <i class="material-icons menu-icon-footer">add</i>
        <i class="material-icons menu-close-icon-footer hidden">close</i>
        <span>Add More</span>
    </button>
</div>
<div class="footer-menu-popover modal fade" id="footer-menu-popover" tabindex="-1" role="dialog" aria-labelledby="footer-menu-popover" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="top-drop-menu">
                    <div onclick="javascript:searchRelatedVideos();" id="clickingButton">
                        <i class="material-icons">add</i><p>Related Videos</p>
                    </div>
                    <div onclick="javascript:openVideos();">
                        <i class="material-icons">add</i><p>Add Videos</p>
                    </div>
                    <div onclick="javascript:openSolution();">
                        <i class="material-icons">add</i> <p>Add Weblink</p>
                    </div>
                    <div onclick="javascript:createNewSet();">
                        <i class="material-icons">add</i>
                        <p>Flashcard</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $('#footer-menu-popover').on('show.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    $('#footer-menu-popover').on('hide.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    function handleMenuClick(url){
        <%if("android".equals(session["appType"])){%>
        if(url=='/books/index'){
            JSInterface.backToHome();
        }
        else if(url=='/dashboard'){
            JSInterface.backToDashboard();
        }
        else if(url=='/doubts'){
            JSInterface.backToDoubts();
        }
        else if(url=='/flashcards'){
            JSInterface.backToFlashcard();
        }

        <%}else if("ios".equals(session["appType"])) {%>

        if(url=='/books/index'){
            webkit.messageHandlers.backToHome.postMessage('');
        }
        else if(url=='/dashboard'){
            webkit.messageHandlers.backToDashboard.postMessage('');
        }
        else if(url=='/doubts'){
            webkit.messageHandlers.backToDoubts.postMessage('');
        }
        else if(url=='/flashcards'){
            webkit.messageHandlers.backToFlashcard.postMessage('');
        }
        <%} else{%>

        // If a mobile browser is being used
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/iPad|iPhone|iPod|Android/.test(userAgent)) {
            window.location.href = url; // this url is passed as an argument to function from menu items
            return;
        }
        <%}%>
    }
    $("#clickingButton").on('click', function () {
        $('#footer-menu-popover').modal('hide');
    });
</script>
