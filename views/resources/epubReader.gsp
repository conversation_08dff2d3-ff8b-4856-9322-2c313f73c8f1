<!DOCTYPE html>
<html dir="ltr" mozdisallowselectionprint>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="google" content="notranslate">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>EPUB</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/assets/wonderslate/vendors.min.css">
    <link rel="stylesheet" href="/assets/wonderslate/readSection.css">

    <script src="/assets/jquery-1.11.2.min.js"></script>
    <script src="/assets/annotator-full.js"></script>
    <script  src="/assets/annotator.touch.js"></script>
    <link rel="stylesheet" href="/assets/annotator-template.css">
    <script  src="/assets/popper.min.js"></script>
    <script src="/assets/landingpage/bootstrap.min.js"></script>

    <style>
    .lottieLoader{
    width: 78px;
    height: 78px;
    border-radius: 50%;
    border: 7px solid;
    border-bottom-color: #FF3D00;
    margin: 0 auto;
    animation:spinRotation 1s linear infinite;
    transition: all 1s ease;
    }

    .pdf-annotatoe-hll{
        background-color: red;
    }
    .card-hl{
        padding: 1rem;
        background: #fff;
    }
    .popover{
        background: #fff;
        border: 0.5px solid rgba(68, 68, 68, 0.24);
        box-sizing: border-box;
        box-shadow: 0 2px 16px rgba(0,0,0, 0.5);
    }
    .popover .btn{
        background-color:#ffffff;
        color:#444444;
        background: none;
        border:none;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.8%);
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        font-size: 12px;
        border-radius: 4px;
    }
    .popover .btn.btn-primary {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    .popover .btn.btn-danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }
    .popover .form-control{
        display: block;
        min-width: 200px;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        margin: 1rem;
    }
    .popover .btn.btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }
    .popover .btn.btn-success {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }
    .popover div.d-flex{
        display: flex;
        justify-content: space-around;
        background: #ededed;
        padding: 4px;
    }
    .popover-body p{
        margin-bottom: 10px;
        padding: 1rem;
    }

    .popover{
        background: #fff;
        border-radius: 4px;

        min-width: 200px;
    }
    .lazyLoader{
        background: #fff;
        height: 100vh;
        position: absolute;
        opacity: 0.9;
        z-index: 9999;
        top: 0;
        left: 0;
        right: 0;
    }
    .lazyLoader h2{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-size: 5rem;
    }

    .loaderHidden{
        display: none;
    }
    @media print {
        body {
            display:none;
        }
    }
    html,body{
        scroll-behavior: smooth !important;
    }

    .anim{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
    .pdfReaderFooter{
        position: relative;
        top: -60px;
        background: #f4f4f4;
        height: 50px;
        padding: 5px;
        display: flex;
        width: 100%;
        justify-content: space-evenly;
        align-items: center;
    }

    .footerPageNumbers,.range{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 50%;
        font-size: 14px;
    }
    #bookContent{
        padding-bottom: 7rem;
        padding-left:10px;
        padding-right: 10px;
    }
    @media (max-width: 768px) {
        .lazyLoader{
            top: 70px;
        }
        #bookContent{
            padding: 0 10px;
        }
    }

    .annotateColorGreen{
        background-color: green;
        border-radius: 3px;
        color: #fff;
    }
    .annotateColorBlue{
        background-color: blue;
        border-radius: 3px;
        color: #fff;
    }
    .annotateColorYellow{
        background-color: yellow;
        border-radius: 3px;
    }
    .annotateColorPicker{
        position: absolute;
        top: -55px;
        background: #fff;
        width: auto;
        padding: 10px;
        border-radius: 5px;
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
        border: 1px solid rgba(0,0,0,0.2)
    }
    .annotateColorPicker:after{
        content: '';
        width: 20px;
        height: 20px;
        position: absolute;
        background: #fff;
        top: 35px;
        transform: rotate(45deg);
        left: 35px;
    }
    .annotateColorPicker button,
    .notesColorBtn,
    .annotator-editor .notesColorBtn{
        width: 30px;
        height: 30px;
        border-radius: 50%;
        opacity: 0.6;
        cursor: pointer;
    }
    .annotator-editor .notesColorBtn:after{
        position: revert;
        width: 0;
        height: 0;
        background-image: none;
    }
    .annotator-editor .notesColorBtn{
        padding: 0;
    }
    .annotator-editor .notesColorBtn.green:hover{
        padding: 0;
        background: green;
    }
    .annotator-editor .notesColorBtn.blue:hover{
        padding: 0;
        background: blue;
    }
    .annotateColorPicker button.blue,
    .notesColorBtn.blue{
        background: blue;
        border: 2px solid transparent;
    }
    .annotateColorPicker button.green,
    .notesColorBtn.green{
        background: green;
        border: 2px solid transparent;
    }
    .annotateColorPicker button.yellow,
    .notesColorBtn.yellow{
        background: yellow;
        border: 2px solid transparent;
    }
    .annotator-adder .annotate-btn{
        border-right: 1px solid #ededed;
    }
    .annotateColorPicker button.selected,
    .notesColorBtn.selected{
        border-color: black;
    }
    .notesColorBtn.yellow:hover,
    .notesColorBtn.yellow:focus,
    .notesColorBtn.yellow:active{
        background: yellow !important;
    }
    .notesColorBtn.green:hover,
    .notesColorBtn.green:focus,
    .notesColorBtn.green:active{
        background: green !important;
    }
    .notesColorBtn.blue:hover,
    .notesColorBtn.blue:focus,
    .notesColorBtn.blue:active{
        background: blue !important;
    }
    </style>
</head>
<body>
<div id="bookContent">

</div>
<script>
    var annotation;
    var resIdVal = '${params.resId}';
    var bookId = '${params.bookId}';
    var bookGPT = '${params.bookGPT}';
    var annotationColorVal = 'red';
    var genericReader = ${genericReader};
    Annotator.Plugin.StoreLogger = function (element) {
        return {
            pluginInit: function () {
                this.annotator
                    .subscribe("annotationCreated", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    })
                    .subscribe("annotationUpdated", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    })
                    .subscribe("annotationDeleted", function (annotation) {
                        setTimeout(function(){
                            %{--<g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess='renderCreatedNotes(data);' params="'uri='+resIdVal+'&bookId=${params.bookId}'" />--}%
                        }, 100);
                    });
            }
        }
    };

    var serverPath = "${serverURL}";
    var bookLang="${params.bookLang}";
    var loggedInUser = "${params.loggedInUser}";
    var chapterId = "${params.chapterId}"

    let chaptersList =JSON.parse('${chapters}'.replace(/&#92;/g,'\\').replace(/&quot;/g,'"'))
    chaptersList.sort(function(a, b) {
        return a.chapterId - b.chapterId;
    });
    chaptersList.sort()
    let chapterIdsArr = []
    let hrefsArr = []
    chaptersList.forEach(ch=>{
        chapterIdsArr.push(ch.chapterId)
        let hrefs = ch.link.split("/")
        hrefs = hrefs[hrefs.length-1]
        hrefsArr.push({
            link:hrefs,
            chapterId:ch.chapterId,
        })
    })

    const nextBtnEpub = window.parent.nextBtnEpub
    const prevBtnEpub = window.parent.prevBtnEpub
    const footerPageno = window.parent.footerPageno
    const epubHighlights = window.parent.epubHighlights
    const navLeftItem = window.parent.navLeftItem
    const epubFooterTotalPages = window.parent.epubFooterTotalPages
    const previewMode = window.parent.previewMode
    let footPreviousChapter = ""
    let footNextChapter =""

    async function loadAnnotator(resId,data) {
        await openBooks(resId)
        if(annotation !== undefined) {
            annotation.annotator('destroy');
        }

        annotation = $('#bookContent').annotator();
        annotation.annotator('addPlugin', 'Store', {
            // The endpoint of the store on your server.
            prefix: serverPath+'/wonderpublish',

            // Attach the uri of the current page to all annotations to allow search.
            annotationData: {
                'uri': resId,
                'bookId' : bookId,
                annotateColor:annotationColorVal
            },

            urls: {
                // These are the default URLs.
                create:  '/annotateSave',
                update:  '/annotateUpdate/:id',
                destroy: '/annotateDestroy/:id',
                search:  '/annotateSearch'
            },

            // This will perform a "search" action when the plugin loads. Will
            // request the last 20 annotations for the current url.
            // eg. /store/endpoint/search?limit=20&uri=http://this/document/only
            loadFromSearch: {
                'limit': 100,
                'all_fields': 1,
                'uri': resId,
                'bookId':bookId
            },

            showViewPermissionsCheckbox: true,
            showEditPermissionsCheckbox: true
        });

        annotation.annotator('addPlugin', 'Tags');
        annotation.annotator('addPlugin', 'StoreLogger');
        annotation.annotator().annotator("addPlugin", "Touch");
        if(!bookGPT && bookGPT!="true"){
            window.parent.hideLazyLoader()
        }else{
            window.parent.hideAppLoader()
        }
    }

   async function getAnnotatedData(resId){
        if(parent.frames.length > 0) {
            if(window.parent.loginUser){
                <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess="loadAnnotator(resId,data)" params="'limit=100&all_fields=1&uri='+resId+'&bookId='+bookId" />
            }else{
                if(window.parent.userId && window.parent.userId!=""){
                    <g:remoteFunction controller="wonderpublish" action="annotateSearch"  onSuccess="loadAnnotator(resId,data)" params="'limit=100&all_fields=1&uri='+resId+'&bookId='+bookId" />
                }else{
                    await openBooks(resId)
                }
                if(!bookGPT && bookGPT!="true"){
                    window.parent.hideLazyLoader()
                }else{
                    window.parent.hideAppLoader()
                }
            }
        }
    }

    getAnnotatedData(resIdVal)

    function dummyAnnotatorData(){
        if(parent.frames.length > 0) {
            var resId=${params.resId};
            var bookId=${params.bookId};
            <g:remoteFunction controller="wonderpublish" action="dummyAnnotatorData"  onSuccess="loadAnnotator(resId,data)" params="'limit=100&all_fields=1&uri='+resId+'&bookId='+bookId" />
        }
    }

    async function openBooks(resId) {
        var resLink="supload/epub/"+bookId+"/"
        const currentIndex = chapterIdsArr.indexOf(Number(chapterId))
        const lastIndex = chapterIdsArr.length - 1
        if(!bookGPT && bookGPT !="true"){

            if(currentIndex==0){
                if(!previewMode){
                    prevBtnEpub.setAttribute('disabled','disabled')
                }
                if(!previewMode && genericReader){
                    footNextChapter = window.parent.document.getElementById('nextChapter')
                    footNextChapter.removeAttribute("style")
                    footNextChapter.innerHTML = "Next Chapter"
                    footNextChapter.addEventListener('click',nextChapter)
                }
            }else if(currentIndex == lastIndex){
                if(!previewMode){
                    nextBtnEpub.setAttribute('disabled','disabled')
                }
                if(!previewMode && genericReader){
                    footPreviousChapter = window.parent.document.getElementById('previousChapter')
                    footPreviousChapter.removeAttribute("style")
                    footPreviousChapter.innerHTML = "Previous Chapter"
                    footPreviousChapter.addEventListener('click',previousChapter)
                }
            }else{
                if(!previewMode){
                    nextBtnEpub.removeAttribute('disabled')
                    prevBtnEpub.removeAttribute('disabled')
                }
                if(!previewMode && genericReader){
                    footNextChapter = window.parent.document.getElementById('nextChapter')
                    footPreviousChapter = window.parent.document.getElementById('previousChapter')
                    footPreviousChapter.removeAttribute("style")
                    footPreviousChapter.innerHTML = "Previous Chapter"
                    footNextChapter.removeAttribute("style")
                    footNextChapter.innerHTML = "Next Chapter"
                    footNextChapter.addEventListener('click',nextChapter)
                    footPreviousChapter.addEventListener('click',previousChapter)
                }
            }
        }

        const bookDataRes = await fetch('/funlearn/getHtmlsFile?resId='+resId)
        const bookData = await bookDataRes.text();
        const bookContent = document.getElementById('bookContent')
        bookContent.innerHTML = bookData
        const cssFileLink = bookContent.querySelectorAll('link[rel="stylesheet"][type="text/css"]');
        const imgLinks = bookContent.querySelectorAll('img');
        const anchorTags = bookContent.querySelectorAll('a')

        cssFileLink.forEach(function(link) {
            const styleLink = "/funlearn/downloadEpubImage?source="
            let cssPath =  link.href.split("/")
            cssPath = cssPath[cssPath.length-1]
            link.href = styleLink+resLink+cssPath
        });
        imgLinks.forEach(function(link) {
            const imgLink = "/funlearn/downloadEpubImage?source="
            let imgPath =  link.src.split("/")
            imgPath = imgPath[imgPath.length-1]
            link.src = imgLink+resLink+imgPath
        });

        anchorTags.forEach(anchor=>{
            let pageLink = anchor.href.split("/")
            pageLink = pageLink[pageLink.length-1]
            if(pageLink.includes("#")){
                pageLink= pageLink.split("#")[0]
            }
            for(var i=0;i<hrefsArr.length;i++){
                if(hrefsArr[i].link==pageLink){
                    const pageIndex = chapterIdsArr.indexOf(Number(hrefsArr[i].chapterId))
                    anchor.href="javascript:changeChapter("+pageIndex+")"
                }
            }
        })
        let navElement=""
        if(!bookGPT && bookGPT !="true"){
            if(!previewMode && genericReader){
                navElement = "<div id='chaptersListDisplayEpub'>" +
                    "<select name='chaptersList' id='chaptersList'>";
                for(var c=0;c<chaptersList.length;c++){
                    if(chaptersList[c].resId == resId){
                        navElement+=  "<option value="+chaptersList[c].chapterId +" selected>"+chaptersList[c].chapterName.replaceAll("\\r?\\n", " ")+"</option>";
                    }else{
                        navElement+=  "<option value="+chaptersList[c].chapterId +">"+chaptersList[c].chapterName.replaceAll("\\r?\\n", " ")+"</option>";
                    }
                }

                navElement+="</select>"+
                    "</div>";
                navLeftItem.innerHTML+=navElement
                var chaptersListDropDown = window.parent.document.getElementById('chaptersList');
                chaptersListDropDown.addEventListener('change',(e)=>{
                    if(!bookGPT && bookGPT!="true"){
                        window.parent.showLazyLoader()
                    }
                    const chIndex = chapterIdsArr.indexOf(Number(e.target.value))
                    changeChapter(chIndex)
                })
            }
            epubFooterTotalPages.innerHTML = chaptersList.length
            footerPageno.innerHTML = currentIndex+1
        }
    }



    async function nextChapter(){
        if(!bookGPT && bookGPT!="true"){
            window.parent.showLazyLoader()
        }
        const nextIndex = chapterIdsArr.indexOf(Number(chapterId))+1
        changeChapter(nextIndex)
    }
    async function previousChapter(){
        if(!bookGPT && bookGPT!="true"){
            window.parent.showLazyLoader()
        }
        const previousIndex = chapterIdsArr.indexOf(Number(chapterId))-1
        changeChapter(previousIndex)
    }

    function showHideHighlights(){
        window.parent.viewChapterNotesEpub(resIdVal,bookId,chapterId)
    }

    function changeChapterDropDownValue(id){
        console.log(id)
        const nextIndex = chapterIdsArr.indexOf(Number(id))
        changeChapter(nextIndex)
    }
    function changeChapter(index){
        const nextChapterId = chaptersList[index]
        resIdVal = nextChapterId.resId
        chapterId = nextChapterId.chapterId
        footerPageno.innerHTML = index
        if(!previewMode && genericReader){
            navLeftItem.innerHTML=''
        }
        getAnnotatedData(nextChapterId.resId)
    }

    if(!bookGPT && bookGPT !="true"){
        if(!previewMode){
            nextBtnEpub.addEventListener('click',nextChapter)
            prevBtnEpub.addEventListener('click',previousChapter)
        }

        epubHighlights.addEventListener('click',showHideHighlights)
    }


    $(document).ready(function(){
        $('[data-toggle="popover"]').popover();
    });

    function renderCreatedNotes(data){
        const annotationData = data
        let annotationColorClass = "red"
        annotationData.forEach(item=>{
            if(item.annotateColor){
                if(item.annotateColor=="annotateColorBlue"){
                    annotationColorClass = "annotateColorBlue"
                }else if(item.annotateColor=="annotateColorGreen"){
                    annotationColorClass = "annotateColorGreen"
                }else if(item.annotateColor=="annotateColorYellow"){
                    annotationColorClass="annotateColorYellow"
                }
                document.querySelector('[data-annotation-id="'+item.id+'"]').classList.add(annotationColorClass)
            }
        })
    }
    function renderedNewHighlightedText(data) {

    }
    document.addEventListener('DOMContentLoaded', (event) => {
        document.addEventListener('copy', (e) => {
            e.preventDefault();
        });

        document.addEventListener('cut', (e) => {
            e.preventDefault();
        });

        document.addEventListener('paste', (e) => {
            e.preventDefault();
        });
    });
</script>
<script>

    document.addEventListener('keydown', event => {
        if (event.ctrlKey || event.metaKey && event.key === 's') {
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    },false);
</script>
</body>
</html>

