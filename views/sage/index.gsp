<%@ page import="javax.servlet.http.Cookie" %>
<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

session.setAttribute("servername", appURL);
def newCookie = new javax.servlet.http.Cookie("siteName", "sage");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/sage/navheader"></g:render>

<div class="main-section row">
  <div class="col-md-6 registr-in-seconds-wrapper">
    <p class="registr-in-seconds">Register in just 60 seconds</p>
    <p class="get-access">Get access to new-age online resources for today’s ever-evolving digital learning era</p>
    <%if(session["userdetails"]!=null){%>
      <% if("instructor".equals(session["userdetails"].userType)) { %>
          <a href="/sage/instructorResources?isbn=${session["isbn"]}&siteName=sage" class="btn btn-get-started waves-effect waves-ripple">Get Started</a>
      <% } %>
      <% if("student".equals(session["userdetails"].userType)) { %>
          <a href="/sage/studentResources?isbn=${session["isbn"]}&siteName=sage" class="btn btn-get-started waves-effect waves-ripple">Get Started</a>
      <% } %>
    <%}else{%>
      <button class="btn btn-get-started waves-effect waves-ripple" onclick="javascript:showRegistration();">Get Started</button>
    <%}%>
    
  </div>
  <div class="col-md-6 main-page-wrapper hidden-xs hidden-sm">
    <img src="${assetPath(src: 'sage/assets/img_banner.png')}" class="img-responsive" alt="">
  </div>
</div>

<div class="simplify-learning row">
  <div class="simplify-learning-student col-md-12 col-xs-12">
    <div class="col-md-5 simplify-explained">
      <p class="changed-teaching-experience">Simplify</p> 
      <p class="changed-teaching-experience">Teaching & Learning</p>
      <p class="simplified-details hidden-xs">Based on UGC curriculum guidelines and developed by subject experts, the
      content is designed to provide holistic overview of topics and fosters
      engagement to help students and instructors. The resources offer a robust
      online learning experience on devices across various platforms including
      Apple iOS, Android and Windows and can be accessed from anywhere.</p>

      <p class="simplified-details hidden-sm hidden-md hidden-lg">Get access to online resources for the ever-evolving digital learning era.</p>
    </div>

    <div class="col-md-7 col-xs-12 benefits">
      <div class="col-md-12 col-xs-12 student-benefit">
        <p class="students-benefits-label">Students</p>
        <div class="col-md-6 col-xs-6 benefits-content">
          <div class="img-benefit">
            <a href="/sage/studentResources?isbn=${session["isbn"]}&siteName=sage" class="studyQue">
              <img src="${assetPath(src: 'sage/assets/01.png')}" class="img-responsive" alt="">    
            </a>
          </div>
          <p class="benefits-label hidden-xs">Study Questions</p>
          <p class="benefits-label hidden-sm hidden-md hidden-lg">QAs</p>
          <p class="benefits-text">Short and long answer questions to test your understanding.</p>
        </div>
        <div class="col-md-6 col-xs-6 benefits-content" style="padding-left: 16px;">
          <div class="img-benefit">
            <a href="/sage/studentResources?isbn=${session["isbn"]}&siteName=sage" class="mcqLink">
              <img src="${assetPath(src: 'sage/assets/02.png')}" class="img-responsive" alt="">  
            </a>
          </div>
          <p class="benefits-label">MCQs</p>
          <p class="benefits-text">Multiple choice questions and answers for self-testing.</p>
        </div>
      </div>

      <div class="col-md-12 col-xs-12 student-benefit instructor-content">
        <p class="students-benefits-label">Instructors</p>
        <div class="col-md-6 col-xs-6 benefits-content">
          <div class="img-benefit">
            <a href="/sage/instructorResources?isbn=${session["isbn"]}&siteName=sage" class="teachingManual">
              <img src="${assetPath(src: 'sage/assets/03.png')}" class="img-responsive" alt="">  
            </a>
          </div>
          <p class="benefits-label">Teaching Manual</p>
          <p class="benefits-text">Offers teaching guidelines and ideas to support instructors.</p>
        </div>
        <div class="col-md-6 col-xs-6 benefits-content" style="padding-left: 16px;">
          <div class="img-benefit">
            <a href="/sage/instructorResources?isbn=${session["isbn"]}&siteName=sage" class="teachingSlides">
              <img src="${assetPath(src: 'sage/assets/04.png')}" class="img-responsive" alt="">  
            </a>
          </div>
          <p class="benefits-label">Teaching Slides</p>
          <p class="benefits-text">PowerPoint presentations to assist in classroom lectures.</p>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-12 col-xs-12 author-of-books">
    <p class="hidden-xs">Interested in writing a <span>textbook?</span> <a href="javascript:openSageModal();" data-toggle="modal" class="open-sage-modal"> CLICK HERE</a></p>
    <p class="hidden-sm hidden-md hidden-lg">Are you an <span>Author of textbooks?</span> If you would like to submit your manuscript,</p>
    <a href="#sageModal" data-toggle="modal" class="hidden-sm hidden-md hidden-lg mobile-click-here open-sage-modal">Click Here</a>
  </div>
</div>

<div class="modal fade sage-modal" id="sageModal" tabindex="-1" role="dialog" aria-labelledby="sageModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header sage-modal-header">
        <a href="javascript:hideUserLogin();" class="close-sidebar pull-right" data-dismiss="modal">
          <i class="material-icons">close</i>
        </a>
      </div>
      <div class="modal-body">
        <form class="sage-login-form" style="margin-top: 0;">
          <div class="form-group">
            <input type="text" class="form-control sage-input" id="userFormName" placeholder="Name">
          </div>
          <div class="form-group">
            <input type="email" class="form-control sage-input email-input" id="userFormEmail" placeholder="Email">
          </div>
          <div class="form-group">
            <textarea class="form-control sage-input" id="userFormQuery" rows="8" placeholder="Write your query"></textarea>
          </div>
          <div class="form-group">
            <img id="userFormCaptchaImg" src='#'/>
            <label class="benefits-text" for="captcha">Type the letters above in the box below</label>
            <g:textField class="form-control sage-input" id="userFormCaptcha" name="captcha"/>
          </div>          
          <div class="form-group">
            <input type="button" class="form-control sage-input sage-login-btn waves-effect waves-ripple full-width-input" onclick="javascript:userFormSubmit();" value="Submit">
          </div>
          <div class="form-group">
            <input type="hidden" id="userFormSubject" value="I’m an author">
            <div id="userform-email-error" style="color: red; display: none;">Please enter email address</div>
            <div id="userform-name-error" style="color: red; display: none;">Please enter name</div>
            <div id="userform-query-error" style="color: red; display: none;">Please enter query</div>
          </div>          
        </form>
      </div>
    </div>
  </div>
</div>
<g:render template="/sage/footer"></g:render>

<script>
    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }
    
    function openSageModal() {
        $('#userFormCaptchaImg').attr("src", "${createLink(controller: 'simpleCaptcha', action: 'captcha')}/rand="+Math.random())
        $('#sageModal').modal('show');            
    }
  
    function userFormSubmit() {  
        $('#userform-email-error').hide();
        $('#userform-name-error').hide();
        $('#userform-query-error').hide()
        
        if($("#userFormName").val() == '') {
            $('#userform-name-error').show()
        } else if($("#userFormEmail").val() == '') {
            $('#userform-email-error').show()
        } else if($("#userFormQuery").val() == '') {
            $('#userform-query-error').show()
        } else{
            var email = $("#userFormEmail").val();
            var name = $("#userFormName").val();
            var query = $("#userFormQuery").val();
            var subject = $("#userFormSubject").val();
            var captcha = $("#userFormCaptcha").val();
            
            <g:remoteFunction controller="creation" action="submitUserForm"  onSuccess='hideUserLogin1(data);'
                    params="'email='+email+'&name='+name+'&query='+query+'&subject='+subject+'&captcha='+captcha" />
        }
    }
    
    function hideUserLogin1(data) {
        if(data!=null && data.status == "Fail") {
            $('#userform-query-error').show().html("Invalid CAPTCHA! Please try again.");          
        } else {
            $('#userform-query-error').show().html("Query Submitted Successfully.");
            
            setTimeout(function() {
              $('#sageModal').modal('hide');
            }, 2000);            
        }        
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if(data.status == "Fail") {
            $(".loading-icon").addClass('hidden');
            $('#forgot-password-error').show().html("Email not registered. Please signup");
        } else {
            $(".loading-icon").addClass('hidden');
            $('#forgot-password-error').show().html("Password reset link sent to "+"“"+userEmail+"”");
            $('#fp-user-email').html("“"+userEmail+"”");
        }
    }    
</script>
</body>
</html>