<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
.sortable:hover {
    cursor: pointer;
    text-decoration: underline;
}
.table td, .table th {
    vertical-align: middle;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Integration Report</h3>
                    <g:form controller="sdk" action="sdkIntegrationReport" method="GET">
                        <table>
                            <tr>
                                <td><label for="startDate">Start Date:</label></td>
                                <td>
                                    <g:datePicker name="startDate" value="${params.startDate}" precision="day" />
                                </td>
                            </tr>
                            <tr>
                                <td><label for="endDate">End Date:</label></td>
                                <td>
                                    <g:datePicker name="endDate" value="${params.endDate}" precision="day" />
                                </td>
                            </tr>
                            <tr>
                                <td><label for="uniqueId">Unique Id:</label></td>
                                <td>
                                    <g:textField name="uniqueId" value="${params.uniqueId}" />
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <g:submitButton name="Search" value="Search" />
                                </td>
                            </tr>
                        </table>
                    </g:form>

                <!-- Display Report Table -->
                    <table border="1">
                        <thead>
                        <tr>
                            <th>Date Created</th>
                            <th>Unique Id</th>
                            <th>Name</th>
                            <th>Course</th>
                        </tr>
                        </thead>
                        <tbody>
                        <g:if test="${reportData?.size() > 0}">
                            <g:each in="${reportData}" var="item">
                                <tr>
                                    <td><g:formatDate date="${item.dateCreated}" format="yyyy-MM-dd HH:mm:ss" /></td>
                                    <td>${item.uniqueId.encodeAsHTML()}</td>
                                    <td>${item.name.encodeAsHTML()}</td>
                                    <td>${item.bookTitle.encodeAsHTML()}</td>
                                </tr>
                            </g:each>
                        </g:if>
                        <g:else>
                            <tr>
                                <td colspan="4" class="text-center">No data available</td>
                            </tr>
                        </g:else>
                        </tbody>
                    </table>

                    <!-- Pagination Controls -->
                    <g:paginate total="${total}" params="${params}" />
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
