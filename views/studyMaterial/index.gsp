<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1, h3 {
    color: #343a40;
}

.card {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #e8f0fe, #ffffff);
}

.card .card-title {
    font-size: 1.1rem;
}

.btn-primary {
    background-color: #4a90e2;
    border-color: #4a90e2;
}

.btn-primary:hover {
    background-color: #357ab8;
    border-color: #357ab8;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.table-hover tbody tr:hover {
    background-color: #eef1f5;
}

.fa-folder {
    color: #f0ad4e;
}

.fa-file-pdf {
    color: #d9534f;
}

.fa-file-word {
    color: #0275d8;
}

/* Additional styles for AI theme */
.ai-assistant-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #dee2e6;
    padding: 20px;
    overflow-y: auto;
    display: none; /* Hide by default */
    z-index: 1000;
}

.ai-assistant-toggle {
    position: fixed;
    right: 20px;
    bottom: 20px;
    background-color: #4a90e2;
    color: #fff;
    border-radius: 50%;
    padding: 15px;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1001;
}

.ai-assistant-toggle:hover {
    background-color: #357ab8;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container mt-5">

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>iBookGPT Drive</h1>
        <div>
            <button class="btn btn-primary" data-toggle="modal" data-target="#uploadModal">
                <i class="fas fa-upload"></i> Upload
            </button>
            <button class="btn btn-secondary" data-toggle="modal" data-target="#createFolderModal">
                <i class="fas fa-folder-plus"></i> New Folder
            </button>
        </div>
    </div>

    <!-- Breadcrumb for navigation -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="#" onclick="navigateToFolder('')">Home</a>
            </li>
            <%-- Build breadcrumb segments --%>
            <%
                String path = ''
                if (folderPath) {
                    String[] segments = folderPath.split('/')
                    for (int i = 0; i < segments.length; i++) {
                        path += (i > 0 ? '/' : '') + segments[i]
            %>
            <li class="breadcrumb-item">
                <a href="#" onclick="navigateToFolder('${path}')">${segments[i]}</a>
            </li>
            <%
                    }
                }
            %>
        </ol>
    </nav>

    <!-- Recently Accessed Materials -->
    <h5>Recently Accessed</h5>
    <div id="recentMaterialsSection">
        <% if (!recentMaterials?.isEmpty()) { %>
        <div class="row">
            <% recentMaterials.each { material -> %>
            <div class="col-md-3">
                <div class="card mb-4 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-file-alt"></i>
                            ${material.fileName}
                        </h5>
                        <p class="card-text">
                            Last accessed: ${material.lastAccessed?.format('yyyy-MM-dd HH:mm:ss')}
                        </p>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewMaterial(${material.id})">Open</button>
                    </div>
                </div>
            </div>
            <% } %>
        </div>
        <% } else { %>
        <p>No recently accessed files.</p>
        <% } %>
    </div>

    <!-- Folder and File List -->
    <h5>Folders and Files</h5>
    <table class="table table-hover" id="filesTable">
        <thead class="thead-light">
        <tr>
            <th><a href="#" onclick="sortTable(0)">Name</a></th>
            <th><a href="#" onclick="sortTable(1)">Date Modified</a></th>
            <th>Actions</th>
        </tr>
        </thead>
        <tbody id="filesTableBody">
        <%-- Folders --%>
        <% folders.each { folder -> %>
        <tr>
            <td>
                <i class="fas fa-folder"></i>
                <a href="#" onclick="navigateToFolder('${folder.folderPath}')">${folder.folderName}</a>
            </td>
            <td>
                ${folder.lastUpdated?.format('dd-MM-yyyy HH:mm:ss')}
            </td>
            <td>Folder</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteFolder(${folder.id})">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        <% } %>

        <%-- Files --%>
        <% materials.each { material -> %>
        <tr>
            <td>
                <i class="fas fa-file-alt"></i>
                <a href="#" onclick="viewMaterial(${material.id})">${material.fileName}</a>
            </td>
            <td>
                ${material.lastUpdated?.format('yyyy-MM-dd HH:mm:ss')}
            </td>

            <td>
                <button class="btn btn-sm btn-success" onclick="downloadMaterial(${material.id})">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="btn btn-sm btn-info" onclick="openShareModal(${material.id})">
                    <i class="fas fa-share-alt"></i> Share
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteMaterial(${material.id})">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        <% } %>
        </tbody>
    </table>

</div>

<!-- AI Assistant Toggle Button -->
<div class="ai-assistant-toggle" onclick="toggleAIAssistant()">
    <i class="fas fa-robot"></i>
</div>

<!-- AI Assistant Sidebar -->
<div class="ai-assistant-sidebar" id="aiAssistantSidebar">
    <h4>AI Assistant</h4>
    <p>How can I assist you today?</p>
    <!-- AI assistant UI components go here -->
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload File</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetUploadForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="fileInput">Choose file</label>
                        <input type="file" class="form-control-file" id="fileInput" name="file" required>
                    </div>
                 </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetUploadForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1" role="dialog" aria-labelledby="createFolderModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="createFolderForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="createFolderModalLabel">Create New Folder</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetCreateFolderForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="folderNameInput">Folder Name</label>
                        <input type="text" class="form-control" id="folderNameInput" name="folderName" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetCreateFolderForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Create Folder</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Share Material Modal -->
<div class="modal fade" id="shareMaterialModal" tabindex="-1" role="dialog" aria-labelledby="shareMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="shareMaterialForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="shareMaterialModalLabel">Share Material</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetShareMaterialForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="batchList">
                        <!-- Batch checkboxes will be loaded here via AJAX -->
                    </div>
                    <input type="hidden" name="materialId" id="shareMaterialId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetShareMaterialForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Share</button>
                </div>
            </form>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    var currentFolder = '${folderPath}';
    var currentFolderPath = '${folderPath}';
    function sortTable(n) {
        // Sorting function as provided earlier
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("filesTable");
        switching = true;
        dir = "asc";
        while (switching) {
            switching = false;
            rows = table.querySelectorAll("tbody tr");
            for (i = 0; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].querySelectorAll("td")[n];
                y = rows[i + 1].querySelectorAll("td")[n];
                if (dir == "asc") {
                    if (x.textContent.trim().toLowerCase() > y.textContent.trim().toLowerCase()) {
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (x.textContent.trim().toLowerCase() < y.textContent.trim().toLowerCase()) {
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
            } else {
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }

    function toggleAIAssistant() {
        var sidebar = document.getElementById('aiAssistantSidebar');
        if (sidebar.style.display === 'none' || sidebar.style.display === '') {
            sidebar.style.display = 'block';
        } else {
            sidebar.style.display = 'none';
        }
    }

    // Ensure AI assistant sidebar is hidden on page load
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('aiAssistantSidebar').style.display = 'none';
    });

    // AJAX functions
    function navigateToFolder(folderPath) {
        currentFolder = folderPath;
       loadFolderData(folderPath);
    }

    function viewMaterial(id) {
        window.location.href = '${createLink(controller: 'prompt', action: 'myDriveReader')}/' + id;
    }

    function downloadMaterial(id) {
        window.location.href = '${createLink(controller: 'studyMaterial', action: 'download')}/' + id;
    }

    function deleteMaterial(id) {
        if (confirm('Are you sure you want to delete this file?')) {
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'studyMaterial', action: 'deleteMaterial')}/' + id,
                type: 'POST',
                data: { _method: 'DELETE' },
                success: function() {
                    loadFolderData(currentFolder);
                    $('.loading-icon').addClass('hidden');
                },
                error: function() {
                    alert('Failed to delete file.');
                }
            });
        }
    }

    function deleteFolder(id) {
        if (confirm('Are you sure you want to delete this folder and all its contents?')) {
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'studyMaterial', action: 'deleteFolder')}/' + id,
                type: 'POST',
                data: { _method: 'DELETE' },
                success: function() {
                    loadFolderData(currentFolder);
                    $('.loading-icon').addClass('hidden');
                },
                error: function() {
                    alert('Failed to delete folder.');
                }
            });
        }
    }

    // Handle file upload
    $('#uploadForm').submit(function(e) {

        e.preventDefault();
        var formData = new FormData(this);
        formData.append("folderPath", currentFolderPath);
        //open the loading icon
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'upload')}',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function() {
                $('#uploadModal').modal('hide');
                loadFolderData(currentFolderPath)
            },
            error: function(response) {
                alert('Failed to upload file: ' + response.responseText);
            }
        });
    });

    function resetUploadForm() {
        $('#uploadForm')[0].reset();
    }

    // Handle folder creation
    $('#createFolderForm').submit(function(e) {
        $('.loading-icon').removeClass('hidden');
        e.preventDefault();

        var formData = $(this).serialize();
        //add the current folder path to the form data
        formData += '&parentPath=' + currentFolder;
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'createFolder')}',
            type: 'POST',
            data: formData,
            success: function() {
                $('#createFolderModal').modal('hide');
                loadFolderData(currentFolder);
            },
            error: function(response) {
                alert('Failed to create folder: ' + response.responseText);
            }
        });
    });

    function resetCreateFolderForm() {
        $('#createFolderForm')[0].reset();
    }

    // Handle sharing material
    function openShareModal(materialId) {
        $('#shareMaterialId').val(materialId);
        // Load batch list via AJAX
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'getBatchesForSharing')}',
            success: function(data) {
                $('#batchList').html(data);
                $('#shareMaterialModal').modal('show');
            },
            error: function() {
                alert('Failed to load batch list.');
            }
        });
    }

    $('#shareMaterialForm').submit(function(e) {
        e.preventDefault();
        var formData = $(this).serialize();
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'shareMaterial')}',
            type: 'POST',
            data: formData,
            success: function() {
                $('#shareMaterialModal').modal('hide');
                resetShareMaterialForm();
                alert('Material shared successfully.');
            },
            error: function(response) {
                alert('Failed to share material: ' + response.responseText);
            }
        });
    });

    function resetShareMaterialForm() {
        $('#shareMaterialForm')[0].reset();
        $('#batchList').html('');
    }
</script>
<script type="text/javascript">
    function loadFolderData(folderPath) {
        currentFolderPath = folderPath;
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: "<g:createLink controller='studyMaterial' action='loadFolderData' />",
            data: { folderPath: folderPath },
            type: 'GET',
            success: function(response) {
                updateFolderAndFiles(response.folders, response.materials, response.folderPath, response.recentMaterials);
                $('.loading-icon').addClass('hidden');
            },
            error: function() {
                alert('Failed to load folder data.');
            }
        });
    }

    function updateFolderAndFiles(folders, materials, folderPath, recentMaterials) {
        var tableBody = document.getElementById('filesTableBody');
        tableBody.innerHTML = '';  // Clear existing rows

        // Update breadcrumb navigation
        updateBreadcrumb(folderPath);

        // Add folders to the table
        folders.forEach(function(folder) {
            var folderRow = '<tr>' +
                '<td><i class="fas fa-folder"></i>' +
                '<a href="#" onclick="loadFolderData(\'' + folder.folderPath + '\')">&nbsp;' + folder.folderName + '</a>' +
                '</td>' +
                '<td>' + formatDate(folder.lastUpdated) + '</td>' +
                '<td>Folder</td>' +
                '<td>' +
                '<button class="btn btn-sm btn-danger" onclick="deleteFolder(' + folder.id + ')">' +
                '<i class="fas fa-trash-alt"></i> Delete' +
                '</button>' +
                '</td>' +
                '</tr>';
            tableBody.innerHTML += folderRow;
        });

        // Add materials to the table
        materials.forEach(function(material) {
            var materialRow = '<tr>' +
                '<td><i class="fas fa-file-alt"></i>&nbsp;' +
                '<a href="#" onclick="viewMaterial(' + material.id + ')">' + material.fileName + '</a>' +
                '</td>' +
                '<td>' + formatDate(material.lastUpdated) + '</td>' +
                '<td>' +
                '<button class="btn btn-sm btn-success" onclick="downloadMaterial(' + material.id + ')">' +
                '<i class="fas fa-download"></i> Download' +
                '</button>&nbsp;' +
                '<button class="btn btn-sm btn-info" onclick="openShareModal(' + material.id + ')">' +
                '<i class="fas fa-share-alt"></i> Share' +
                '</button>&nbsp;' +
                '<button class="btn btn-sm btn-danger" onclick="deleteMaterial(' + material.id + ')">' +
                '<i class="fas fa-trash-alt"></i> Delete' +
                '</button>' +
                '</td>' +
                '</tr>';
            tableBody.innerHTML += materialRow;
        });

        // Update the recently accessed materials
        updateRecentMaterials(recentMaterials);
    }

    function updateBreadcrumb(folderPath) {
        var breadcrumb = document.querySelector('.breadcrumb');
        breadcrumb.innerHTML = '';  // Clear existing breadcrumb

        // Create 'Home' breadcrumb item
        breadcrumb.innerHTML += '<li class="breadcrumb-item"><a href="#" onclick="loadFolderData(\'\')">Home</a></li>';

        if (folderPath) {
            var pathSegments = folderPath.split('/');
            var path = '';
            pathSegments.forEach(function(segment) {
                path += (path.length > 0 ? '/' : '') + segment;
                breadcrumb.innerHTML += '<li class="breadcrumb-item"><a href="#" onclick="loadFolderData(\'' + path + '\')">' + segment + '</a></li>';
            });
        }
    }

    function updateRecentMaterials(recentMaterials) {
        var recentSection = document.getElementById('recentMaterialsSection');
        recentSection.innerHTML = '';  // Clear existing recently accessed section

        if (recentMaterials.length > 0) {
            var materialsHtml = '<div class="row">';
            recentMaterials.forEach(function(material) {
                materialsHtml += '<div class="col-md-3">' +
                    '<div class="card mb-4 shadow-sm">' +
                    '<div class="card-body">' +
                    '<h5 class="card-title"><i class="fas fa-file-alt"></i> ' + material.fileName + '</h5>' +
                    '<p class="card-text">Last accessed: ' + formatDate(material.lastAccessed) + '</p>' +
                    '<button class="btn btn-sm btn-outline-primary" onclick="viewMaterial(' + material.id + ')">Open</button>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
            });
            materialsHtml += '</div>';
            recentSection.innerHTML = materialsHtml;
        } else {
            recentSection.innerHTML = '<p>No recently accessed files.</p>';
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '';
        var date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
</script>



</body>
</html>
