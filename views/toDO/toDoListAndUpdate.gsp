<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">

<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<section class="page-main-wrapper mdl-js pb-5 todo_section">
    <div class="container pt-3 pt-md-5 mt-3 mt-md-0">
        <div class="row page_title d-flex justify-content-start align-items-center pb-3 pb-lg-4 px-0 mx-0">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3><strong>My To-Do</strong></h3>
            <a id="addTodo" href="#" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect add-todo-btn ml-2" data-toggle="modal" data-target="#addToDoModal">
                <i class="material-icons">add</i> <span>Add</span>
            </a>
        </div>
        <div id="mobile_calendar" class="calendar_filter pb-2 border-bottom d-md-none">
            <div class="row m-0 justify-content-between" id="monthYear" style="display: flex;">
                <a href="javascript:selectMonth();" class="d-flex align-items-center">
                    <strong id="selectedMonthYear"></strong>
                    <span class="material-icons">
                        arrow_drop_down
                    </span>
                </a>
                <a href="javascript:selectMonth();">
                    <span class="material-icons">
                        date_range
                    </span>
                </a>
            </div>
            <div id="mobileCalendar" style="display: none;"></div>
        </div>
        <div class="row flex-md-row-reverse">
            <div class="col-12 col-md-6">
                <div class="row">
                    <div class="todo_calendar_filter px-md-4 px-lg-5 d-none d-md-block">
                        <div id="todo_calendar" class="calendar_filter"></div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 px-0 px-md-3" >
                <div class="todo_filter d-flex align-items-center" style="margin-bottom: -40px;">
                    <div class="d-flex align-items-center justify-content-end flex-grow-1" id="statusFilter" >
                        <a href="javascript:getToDoList('','')" id="all">All</a>
                        <a href="javascript:getToDoList('Completed','status')" >Completed</a>
                        <a href="javascript:getToDoList('Pending','status')" class="active">Pending</a>
                    </div>
                </div>
                <div class="todo_lists mt-4">
                    <div id="recordsData"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal modal-modifier modal-mobile-fullscreen fade" id="addToDoModal" tabindex="-1" role="dialog" aria-labelledby="addToDoTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered shadow" role="document">
            <div class="modal-content modal-content-modifier">
                <div class="modal-header modal-header-modifier pl-4">
                    <h4 class="modal-title" id="addToDoTitle">Add To-Do</h4>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">x</span>
                    </button>
                </div>
                <div class="modal-body modal-body-modifier add_todo p-4">
                    <div class="add_todo_form">
                        <div class="form-group form-group-modifier mb-4">
                            <input type="text" class="form-control form-control-modifier border-bottom" id="todoName" name="taskName" placeholder="Name*" autocomplete="off">
                        </div>
                        <div class="form-group">
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="calendar-addon"><asset:image src="ws/icon-calendar.svg"/></span>
                                </div>
                                <input type="text" class="form-control border-0 calendar_input" id="toDoDate" name="taskDate" value="" placeholder="Select Date*" autocomplete="off">
                            </div>
                        </div>
                        <div class="form-group">
                            <label><small><em>Time (Optional)</em></small></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="time-addon"><asset:image src="ws/icon-time.svg"/></span>
                                </div>
                                <input type="text" class="form-control border-0 time_input mr-2" id="toDoStartTime" name="fromTime" value="" placeholder="From">
                                <input type="text" class="form-control border-0 time_input ml-2" id="toDoEndTime" name="toTime" value="" placeholder="To">
                            </div>
                        </div>
                        <div class="form-group mb-5">
                            <label><small><em>Priority (Optional)</em></small></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="priority-addon"><asset:image src="ws/icon-priority.svg"/></span>
                                </div>
                                <select class="form-control border-0 priority_input" id="toDoPriority" name="priority">
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center py-3">
                            <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                            <button type="submit" onclick="javascript:submitToDo()" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 ml-2 submit_btn">Save & Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal modal-modifier modal-mobile-fullscreen fade" id="editToDoModal" tabindex="-1" role="dialog" aria-labelledby="editToDoTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered shadow" role="document">
            <div class="modal-content modal-content-modifier">
                <div class="modal-header modal-header-modifier border-0 pl-4">
                    <h4 class="modal-title" id="editToDoTitle">Edit To-Do</h4>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">x</span>
                    </button>
                </div>
                <div class="modal-body modal-body-modifier add_todo p-4">
                    <div class="add_todo_form">
                        <div class="form-group form-group-modifier mb-4">
                            <input type="text" class="form-control form-control-modifier border-bottom" id="todoName1" name="taskName1" placeholder="Name*" autocomplete="off">
                        </div>
                        <div class="form-group">
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="calendar-addon1"><asset:image src="ws/icon-calendar.svg"/></span>
                                </div>
                                <input type="text" class="form-control border-0 calendar_input" id="toDoDate1" name="taskDate1" value="" placeholder="Select Date*" autocomplete="off">
                            </div>
                        </div>
                        <div class="form-group">
                            <label><small><em>Time (Optional)</em></small></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="time-addon1"><asset:image src="ws/icon-time.svg"/></span>
                                </div>
                                <input type="text" class="form-control border-0 time_input mr-2" id="toDoStartTime1" name="fromTime1" value="" placeholder="From">
                                <input type="text" class="form-control border-0 time_input ml-2" id="toDoEndTime1" name="toTime1" value="" placeholder="To">
                            </div>
                        </div>
                        <div class="form-group mb-5">
                            <label><small><em>Priority (Optional)</em></small></label>
                            <div class="input-group mb-3">
                                <div class="input-group-prepend">
                                    <span class="input-group-text border-0" id="priority-addon1"><asset:image src="ws/icon-priority.svg"/></span>
                                </div>
                                <select class="form-control border-0 priority_input" id="toDoPriority1" name="priority1">
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                        </div>
                        <input type="hidden" id="toDoTaskId" name="toDoTaskId" value="">
                        <div class="d-flex justify-content-center py-3">
                            <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                            <button type="submit" onclick="javascript:updateToDo()" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 ml-2 submit_btn">Save & Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script>
    var tab = "";
    var filterValue = "";
    var filterType = "";
    var status="";

function getToDoList(filterValue,filterType){
    $('.loading-icon').removeClass('hidden');
    $('#mobileCalendar').hide();
    $('#monthYear').show();

    if(selectedDate!="undefined" && selectedDate!=null && selectedDate!=""){

        filterType="date";
        status=filterValue;
        <g:remoteFunction controller="toDo" action="getToDoList" params="'filterValue='+selectedDate+'&filterType='+filterType+'&status='+status" onSuccess="showTodoList(data)"/>
    }else{

        tab = filterValue;
        <g:remoteFunction controller="toDo" action="getToDoList" params="'filterValue='+filterValue+'&filterType='+filterType" onSuccess="showTodoList(data)"/>
    }
}



    function showTodoList(data){

        $('.loading-icon').addClass('hidden');
        var todo= data.toDoTask;
        var htmlStr = "";
        var htmlStrDate="";
        // if(tab == ""){
        var allData = getDateWiseTaskList(data);
        var keys = Object.keys(allData)
        for(var j=0;j<keys.length;j++){

            var date =moment.utc(keys[j],'DD/MM/YYYY').local().format("MMMM Do YYYY");
            var today= moment.utc(new Date()).local().format("MMMM Do YYYY");


            if(date==today){
                htmlStr += "<div class='todo_filter d-flex'><h6 class=\"mb-2 d-block\" style='color: black'>Today</h6></div>";
            }else {
                htmlStr += "<div class='todo_filter d-flex'><h6 class=\"mb-2 d-block\" style='color: black'>" + moment.utc(keys[j],'DD/MM/YYYY').local().format("MMMM Do YYYY") + "</h6></div>";
            }
            // htmlStr += "<div class='todo_filter d-flex'><h4 class=\"mb-2\"><strong>" + moment.utc(keys[j]).local().format("MMMM Do YYYY") + "</strong></h4></div>";
            // document.getElementById("today").innerHTML = htmlStrDate
            var temp = allData[keys[j]];
            for(var k=0;k<temp.length;k++){
                htmlStr +="<div class=\"row card border-0 mx-0 mb-3 p-3\">" +
                    "<div class=\"custom-control custom-checkbox\">\n";
                if(temp[k].status == "completed") {
                    htmlStr += "<input type=\"checkbox\" class=\"custom-control-input\" id='" + temp[k].id + "' checked='checked'>\n";
                } else {
                    htmlStr += "<input type=\"checkbox\" class=\"custom-control-input\" id='" + temp[k].id + "'>\n";
                }
                htmlStr += "<label class=\"custom-control-label\" for='"+temp[k].id+"'></label>\n" +
                    "</div>";
                if(temp[k].status == "completed") {
                    htmlStr += "<del  class=\"todo_info pl-4 ml-2\">";
                } else {
                    htmlStr += "<div  class=\"todo_info pl-4 ml-2\">";
                }
                if(temp[k].resId!=null && temp[k].resId!="" &&  temp[k].resId!="null") {
                    var  resourceLink = "/resources/ebook?resId="+temp[k].resId+"&passUrl&myActivity=true";
                    htmlStr += "<a href="+resourceLink+" target=\"_blank\"> <p id=\"desc\">" + temp[k].taskName + "</p>" +
                        "<div class=\"d-flex align-items-start\">";
                }else{
                    htmlStr += "<p id=\"desc\">" + temp[k].taskName + "</p>" +
                        "<div class=\"d-flex align-items-start\">";
                }

                if(temp[k].priority!=null && temp[k].priority!="" &&  temp[k].priority!="null"){
                    htmlStr += "<small class=\"todo_priority pr-3 pr-lg-4\"><em>Priority</em><br><span class='text-capitalize "+ temp[k].priority +"'>" + temp[k].priority + "</span></small>";
                }

                if(temp[k].fromTime!=null && temp[k].toTime!=null && temp[k].fromTime!="" &&  temp[k].toTime!=""){
                    htmlStr += "<small class=\"todo_time\"><em>Time</em><br><span>" + temp[k].fromTime + "<span> - </span>" + temp[k].toTime +"</span></small>";
                }

                htmlStr += "</div>";
                if(temp[k].status == "completed") {
                    htmlStr += "</del>\n";
                } else {
                    htmlStr += "</div>\n";
                }
                htmlStr += "<div class='edit-access'>" +
                        "<a href='javascript:deleteTodo("+temp[k].id+")' class='delete-icon'><i class='material-icons'>delete</i></a>" +
                        "<a href='javascript:editTodo("+temp[k].id+")' class='edit-icon'><i class='material-icons'>edit</i></a>" +
                    "</div>"+
                    "</div>";
                document.getElementById("recordsData").innerHTML = htmlStr;

                $('#recordsData input[type="checkbox"]').click(function(){
                    if($(this).prop("checked") == true){
                        var id =this.id;
                        var typed="addStatus";
                        location.reload();
                        $('.loading-icon').removeClass('hidden');
                        <g:remoteFunction controller="toDo" action="editToDoTask" params="'toDoTaskId='+id+'&type='+typed+'&status=completed'" />
                    }
                    else if($(this).prop("checked") == false){
                        var id =this.id;
                        $(this).addClass('inactive').removeClass('active');
                        var  typed1="addStatus";
                        location.reload();
                        $('.loading-icon').removeClass('hidden');
                        <g:remoteFunction controller="toDo" action="editToDoTask" params="'toDoTaskId='+id+'&type='+typed1+'&status=pending'"/>
                    }
                });

            }
        }
    }

function deleteTodo(id){

    // Delete to-do alert
    swal({
            title: "Are you sure?",
            text: "Once deleted, you will not be able to recover this To-Do.",
            type: "warning",
            allowOutsideClick: true,
            showConfirmButton: true,
            showCancelButton: true,
            confirmButtonColor: "#FF4B33",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "Cancel",
            closeOnConfirm: false,
            closeOnCancel: true,
            allowEscapeKey: false,
            customClass: '',
        },
        function(isConfirm){
            if (isConfirm) {
                swal({
                    title: "Deleted",
                    text: "To-Do deleted successfully!",
                    type: "success",
                    confirmButtonColor: "#27AE60",
                    allowEscapeKey: false,
                }, function() {
                    <g:remoteFunction controller="toDo" action="deleteToDo" params="'id='+id" onSuccess="deletedTodo()"/>
                });
            }
            // else {
            //     swal({
            //         title: "Cancelled",
            //         text: "To-Do deleting process cancelled.",
            //         type: "error",
            //         confirmButtonColor: "#98309C"
            //     });
            // }
        });
}

function  deletedTodo(){
    location.reload();
    $('.loading-icon').removeClass('hidden');

}
var type = "";
function editTodo(id){
    type = "editTask";
    <g:remoteFunction controller="toDo" action="editToDoTask" params="'toDoTaskId='+id+'&type='+type" onSuccess="editedTodo(data)"/>
}

function  editedTodo(data){
    var todo= data.toDoTask;
   var date =moment.utc(todo.taskDate,'YYYY-MM-DD').local().format("DD-MM-YYYY");
    $('#editToDoModal').modal({
        backdrop: 'static',
        keyboard: false
    }).show();
    $('#todoName1').val(todo.taskName);
    $('#toDoDate1').val(date);
    $('#toDoPriority1').val(todo.priority);
    $('#toDoTaskId').val(todo.id);

    currentDate = moment().format('DD-MM-YYYY');
    if (date === currentDate) {
        $('#toDoStartTime1').data("DateTimePicker").minDate(new Date());
        $('#toDoEndTime1').data("DateTimePicker").minDate(new Date());
        $('#toDoStartTime1').val(todo.fromTime);
        $('#toDoEndTime1').val(todo.toTime);
    } else {
        $('#toDoStartTime1').data("DateTimePicker").minDate('+1d');
        $('#toDoEndTime1').data("DateTimePicker").minDate('+1d');
        $('#toDoStartTime1').val(todo.fromTime);
        $('#toDoEndTime1').val(todo.toTime);
    }

}

var toDoTaskId = "";
function updateToDo() {
    type = "editTask";
        if($("#todoName1").val() == "") {
            alert("Please enter name.");
            $("#todoName1").focus();
        } else if($("#toDoDate1").val() == "") {
            alert("Please select date.");
            $("#toDoDate1").focus();
        } else {
            taskName = $("#todoName1").val();
            taskDate = $("#toDoDate1").val();
            fromTime = $("#toDoStartTime1").val();
            toTime = $("#toDoEndTime1").val();
            priority = $("#toDoPriority1").val();
            toDoTaskId = $("#toDoTaskId").val();

            // Update to-do alert
            swal({
                    title: "Updated",
                    text: "To-Do has been updated successfully!",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#27AE60",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function() {
                    <g:remoteFunction controller="toDo" action="editToDoTask" params="'toDoTaskId='+toDoTaskId+'&taskName='+taskName+'&taskDate='+taskDate+'&fromTime='+fromTime+'&toTime='+toTime+'&priority='+priority+'&type='+type" onSuccess="updatedAlert()"/>
                });

        }
    }

function updatedAlert() {
    location.reload();
    $('.loading-icon').removeClass('hidden');
}

function getDateWiseTaskList(data){
    var todo= data.toDoTask;
    var obj = {};
    var htmlStr = "";
    var taskList = []
    if(todo!=null && todo!="") {
        $("#addTodo").show();
         for (var i = 0; i < todo.length; i++) {
            var date = todo[i]['date1'].split(" ")[0];

            if (obj[date] == undefined || obj[date] == "" || obj[date] == null) {
                taskList = [];
                taskList.push(todo[i]);
                obj[date] = taskList
            } else if (obj[date].length > 0 && obj[date][0]['date1'].split(" ")[0] == date) {
                taskList = obj[date];
                taskList.push(todo[i]);
                obj[date] = taskList
            }
        }
    }else{
        htmlStr +=  "<br><br><div class='no-books-available'>" +
            "<p class='text-center pt-4'>There's always a new beginning.<br><strong>Start creating your to do list and streamline your learning</strong></p>" +
            "<div class='text-center'><a href='#' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect' data-toggle=\"modal\" data-target=\"#addToDoModal\">Add</a></div>" +
            "</div>";
        document.getElementById("recordsData").innerHTML = htmlStr;
        $("#addTodo").hide();

    }
    return obj;
}

getToDoList('','');

// Prevent closing add to-do popup when click inside
$(document).on('click', '.add_todo_form', function (e) {
    e.stopPropagation();
});

var currentDate = "";
$(document).ready(function(){

    var today = moment().format('MMMM YYYY');
    document.getElementById("selectedMonthYear").innerHTML = today;

    $('#toDoDate').datepicker({
        format: 'dd-mm-yyyy',
        minDate:new Date(),
        startDate: new Date(),
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        //endDate: '+0d',
    }).on('changeDate', addEqualSelectedDate);

    $('#toDoDate1').datepicker({
        format: 'dd-mm-yyyy',
        minDate:new Date(),
        startDate: new Date(),
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
    }).on('changeDate', editEqualSelectedDate);

    currentDate = moment().format('DD-MM-YYYY');

    function addEqualSelectedDate(){
        var addToDoDate = $('#toDoDate').datepicker('getFormattedDate');
        //Add To-Do time
        if (currentDate === addToDoDate) {
            $('#toDoStartTime').data("DateTimePicker").minDate(new Date());
            $('#toDoEndTime').data("DateTimePicker").minDate(new Date());
        } else {
            $('#toDoStartTime').data("DateTimePicker").minDate('+1d');
            $('#toDoEndTime').data("DateTimePicker").minDate('+1d');
        }
    }

    function editEqualSelectedDate(){
        var editToDoDate = $('#toDoDate1').datepicker('getFormattedDate');
        //Edit To-Do time
        if (currentDate === editToDoDate) {
            $('#toDoStartTime1').data("DateTimePicker").minDate(new Date());
            $('#toDoEndTime1').data("DateTimePicker").minDate(new Date());
        } else {
            $('#toDoStartTime1').data("DateTimePicker").minDate('+1d');
            $('#toDoEndTime1').data("DateTimePicker").minDate('+1d');
        }
    }

    $(function () {
        $('#toDoStartTime, #toDoEndTime, #toDoStartTime1, #toDoEndTime1').datetimepicker({
            format: 'LT',
            icons: {
                up: "fa fa-arrow-up",
                down: "fa fa-arrow-down"
            }
        });
    });

    // $('#todo_calendar').datetimepicker({
    //     format: 'LT',
    //     inline: true,
    //     sideBySide: true,
    //     icons: {
    //         up: "fa fa-arrow-up",
    //         down: "fa fa-arrow-down"
    //     }
    // });

    $('#todo_calendar').datepicker({
        maxViewMode: 0,
        format: 'dd-mm-yyyy',
        todayHighlight: true,
    }).on('changeDate', showTestDate);

    $(function() {
        $("#statusFilter a").on("click", function() {
            $("#statusFilter a.active").removeClass("active");
            $(this).addClass("active");
        });
    });

});

var selectedDate="";
    function showTestDate(){

        $('.loading-icon').removeClass('hidden');
        tab="date";
        selectedDate = $('#todo_calendar').datepicker('getFormattedDate');
        $('#toDoDate1').val(selectedDate);
        $('#toDoDate').val(selectedDate);
        $("#statusFilter a.active").removeClass("active");
        $("#all").addClass("active");
        <g:remoteFunction controller="toDo" action="getToDoList" params="'filterValue='+selectedDate+'&filterType=date'" onSuccess="showTodoList(data)"/>
    }

    var taskName = "";
    var taskDate = "";
    var fromTime = "";
    var toTime = "";
    var priority = "";
    function submitToDo() {
        if($("#todoName").val() == "") {
            alert("Please enter name.");
            $("#todoName").focus();
        } else if($("#toDoDate").val() == "") {
            alert("Please select date.");
            $("#toDoDate").focus();
        } else {
            taskName = $("#todoName").val();
            taskDate = $("#toDoDate").val();
            fromTime = $("#toDoStartTime").val();
            toTime = $("#toDoEndTime").val();
            priority = $("#toDoPriority").val();

            // Add To-do alert
            swal({
                    title: "Success",
                    text: "To-Do has been added successfully!",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: "#27AE60",
                    confirmButtonText: "Ok",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                },function() {
                    <g:remoteFunction controller="toDo" action="addToDoTask" params="'taskName='+taskName+'&taskDate='+taskDate+'&fromTime='+fromTime+'&toTime='+toTime+'&priority='+priority" onSuccess="successAlert()"/>
                });
        }
    }

    function successAlert() {
        location.reload();
        $('.loading-icon').removeClass('hidden');
    }

    function selectMonth() {
        $('#mobileCalendar').datepicker({
            maxViewMode: 0,
            format: 'dd-mm-yyyy',
            todayHighlight: true,
        }).show();
        $("#monthYear").hide();
    }

    $('#mobileCalendar').on('changeDate', function(event) {
        $(this).hide();
        var monthYear = event.format('MM yyyy');
        $("#monthYear").show();
        document.getElementById("selectedMonthYear").innerHTML = monthYear;

        $('.loading-icon').removeClass('hidden');
        tab="date";
        selectedDate = $('#mobileCalendar').datepicker('getFormattedDate');
        <g:remoteFunction controller="toDo" action="getToDoList" params="'filterValue='+selectedDate+'&filterType=date'" onSuccess="showTodoList(data)"/>
    });

</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

<script>
    $(document).ready(function(){
        $("#addToDoModal").on("shown.bs.modal", function () {
            $("#todoName").focus();
        });
        $("#editToDoModal").on("shown.bs.modal", function () {
            $("#todoName1").focus();
        });
    });
</script>

</body>
</html>
