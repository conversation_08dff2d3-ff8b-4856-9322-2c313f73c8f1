<div class="modal fade in" tabindex="-1" role="dialog" id="answer-match-modal">
  <div class="modal-dialog practice-modal modal-lg" role="document">
   <div class="modal-content">
    <div class="modal-header text-center">
      <button type="button" class="close close-modal" data-dismiss="modal" aria-label="Close" onclick="javascript:resetTestType();">
        <i class="material-icons">close</i>
      </button>
      <h4 class="modal-title quizTitle">Practice - Q&A's</h4>
    </div>
    <div class="modal-body quiz-modal-body">
      <div class="container-fluid answer-match-container" id="answer-match-user-input">
      <div class='read-question col-md-12' id="answerMatchQuestion"></div>
        <div class="answer-wrapper">
          <p class="label-answer" id="your-answer-laber">Your Answer</p>
          <textarea id="userAnswerEntered" name="idealAnswer" class="answer-input-textarea" rows="5" placeholder="Enter a great answer here"></textarea>
        </div>
      </div>
      <div class="container-fluid answer-match-container" id="answer-match-container" style="display: none;">
        <div class="row">
          <div class="col-md-12 answer-match-wrapper">
            <div class="answer-wrapper">
              <p class="label-answer">Expected Answer</p>
              <textarea id="idealAnswer" name="idealAnswer" class="answer-input-textarea" rows="5" placeholder="Enter a great answer here">What is your name man</textarea>
            </div>

            <div class="answer-wrapper">
              <p class="label-answer">Your Answer</p>
              <textarea id="userAnswer" name="userAnswer" class="answer-input-textarea" rows="5" placeholder="Enter a great answer here"></textarea>
            </div>
            
            <p class="label-answer">Complete answer match</p>
            <div class="progress answer-match-progress-wrapper">
              <div class="progress-bar progress-bar-success answer-match-progress-success" id="similarityBar" role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
              </div>
            </div>
            <div class="answer-match-accuracy" id="totalSimilarity">0%</div>

            <div class="show-more-accuracy-details" id="show-accuracy-table-wrapper" style="display: none;">
              <a class="show-accuracy-table" id="show-accuracy-table" role="button" data-toggle="collapse" href="#resultBlock" aria-expanded="false" aria-controls="resultBlock">Show More Details <i class="icon-chevron"></i></a>
            </div>

            <div id="resultBlock" class="collapse">
              <div class="answer-phrase-match-table-wrapper">
                <table class="table table-responsive answer-phrase-match-table">
                  <thead>
                    <tr>
                      <th>Ideal answer phrases</th>
                      <th>user answer phrases</th>
                      <th>match %</th>
                    </tr>
                  </thead>
                  <tbody id="resultTable"></tbody>
                </table>
              </div>
              <div class="answer-phrase-match-table-wrapper">
               <table class="table table-responsive answer-phrase-match-table">
                 <thead>
                   <tr>
                     <th>Ignored Phrases</th>
                   </tr>
                 </thead>
                 <tbody id="ignored"></tbody>
               </table>
             </div>
           </div>
         </div>
       </div>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-practice-question-answer" onclick="javascript:submitForm();">Next</button>
  </div>
</div>
</div>

<script>
function openAnswerMatch(index) {
    document.getElementById("idealAnswer").value=qas[index].answer.replace(/<\/?[^>]+(>|$)/g, "").replace(/&nbsp;/gi,'');
    document.getElementById("answerMatchQuestion").innerHTML="<b>"+qas[index].ps+"</b>";
    $('#idealAnswer').attr('enabled','enabled');
    $('#userAnswer').attr('enabled','enabled');
    $('#answer-match-user-input').show();
    $('#answer-match-container').hide();
    document.getElementById('userAnswerEntered').value="";
    $('#answer-match-modal').modal('show');

}
function submitForm(){
  var idealAnswer = document.getElementById("idealAnswer").value;
  var userAnswer =  document.getElementById("userAnswerEntered").value;
  var userAnswerEntered = document.getElementById('userAnswerEntered').value;
  document.getElementById("userAnswer").value = userAnswerEntered;
  if($('#userAnswerEntered').val() != "") {
    <g:remoteFunction controller="wonderpublish" action="evaluateAnswers"  onSuccess='processResults(data);' params="'idealAnswer='+idealAnswer+'&userAnswer='+userAnswer" />
    $('#idealAnswer').attr('disabled','disabled');
    $('#userAnswer').attr('disabled','disabled');
    $('#answer-match-user-input').hide();
    $('#answer-match-container').show();
  } else {
    $('#userAnswerEntered').css({
      'border' : '1px solid red'
    });
    $('#your-answer-laber').html('Please enter answer').css({
      'color' : 'red'
    });
  }
}

function processResults(data){
  var highScores=data.highScores;
  var closeAnswers=data.closeAnswers;
  var failSentences=data.failSentences;
  var idealAnswer=data.idealAnswer;
  var userAnswer=data.userAnswer;
  
  $('#show-accuracy-table-wrapper').show();

  var htmlStr="";
  for(i=0;i<idealAnswer.length;i++){
    htmlStr+=" <tr>" +
    "              <td>"+idealAnswer[i]+"</td>" +
    "              <td>"+closeAnswers[i]+"</td>" +
    "              <td>"+(highScores[i]*100).toFixed(2)+"%</td>\n" +
    "            </tr>"
  }
  document.getElementById("resultTable").innerHTML=htmlStr;
  document.getElementById("totalSimilarity").innerHTML=(data.totalSimilarity*100/idealAnswer.length).toFixed(2)+"%";
  document.getElementById("similarityBar").style.width=(data.totalSimilarity*100/idealAnswer.length).toFixed(2)+"%";
  
  $('#totalSimilarity').each(function () {
    $(this).prop('Counter',0).animate({
        Counter: $(this).text()
    }, {
        duration: 600,
        easing: 'swing',
        step: function (now) {
            $(this).text((now*100/100).toFixed(2)+"%");
        }
      });
  });

  htmlStr="";
  for(i=0;i<failSentences.length;i++){
    if(failSentences[i]==null||failSentences[i]=="null") continue;
    htmlStr+=" <tr>" +
    "              <td>"+failSentences[i]+"</td>" +
    "            </tr>"
  }
  document.getElementById("ignored").innerHTML=htmlStr;
}

$('#show-accuracy-table').click(function() {
  $(this).html($(this).html() == 'Show More Details <i class="icon-chevron"></i>' ? 'Hide More Details <i class="icon-chevron is-rotated"></i>' : 'Show More Details <i class="icon-chevron"></i>');
});
</script>