<div class="modal fade chapters-modal" id="buy-chapters-modal" tabindex="-1" data-backdrop="static" role="dialog" aria-labelledby="chapters-modal">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-body">
        <div class="col-md-6 col-sm-6 book-details-aside hidden-xs">
          <div class="chapter-book-image-wrapper" id="chapterBookImage">
           </div>
          <h1 class="book-name" id="chapterBookTitle"></h1>
          <p class="publisher-name">by <span id="chapterPublisher"></span></p>

          <div class="col-md-5 chapter-book-price-details">
            <p class="chapter-book" id="chapterSingleLabel">Single </p>
            <p class="offer-price" id="chapterSinglePrice"> </p>
            <p class="original-price" id="chapterSingleListPrice"></p>
          </div>
          <div class="col-md-5 chapter-book-price-details">
            <p class="chapter-book">Complete Book</p>
            <p class="offer-price" id="chapterOfferPrice"></p>
            <p class="original-price" id="chapterListPrice">220</p>
          </div>
          
          <ul class="offer-chapters" id="chapterOfferChapters"></ul>

          <div class="book-buy">
            <p class="complete-book-legend">Complete book</p>
            <a href="javascript:buyFullbook();" class="buy-complete-book-btn">Buy Now</a>
          </div>
        </div>
        <div class="col-md-6 col-sm-6 chapter-details-aside">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true" style="font-size: 32px; position: absolute; top: 8px; right: 16px;">&times;</span>
          </button>
          <p class="select-chapters-legend" id="chapterSelectLabel"></p>
          <table class='table chapter-modal-chapter-selection-table table-responsive'>
            <tbody>
              <tr class='chapter-selection' id="chapterSelectionRows"></tr>
            </tbody>
          </table>
          <div class="book-unlock">
            <p class="complete-book-legend">
              <span id="chapterNoOfChapters"></span>
            </p>
            <a href="javascript:unlock()" class="unlock-complete-book-btn">
              <i class="fa fa-inr"></i> 
              <span id="chapterTotalCost"></span> | Unlock
            </a>
            <span id="chapterErrorMessage" class="redText"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  var allChaptersPrice;
  var chapterBookId;
  var chapterBookPrice;
  var chapterBookTitle;
  var allChapters;
  var alreadyBoughtChapters;
  var chapterTypeMain;
  var noOfChapters=[];
  var chaptersPrice=[];
  var selectedChaptersArray=[];
  var noOfChaptersLocal=0;
  var cost=0;
  function getInfo(bookId){
    <g:remoteFunction controller="wonderpublish" action="getBookDetails"  onSuccess='populateBuyModal(data);' params="'bookId='+bookId"/>
     $('#buy-chapters-modal').modal('show');
    document.getElementById("chapterErrorMessage").innerHTML="";
  }

  function populateBuyModal(data){
    chapterBookId = data.bookId;
    chapterBookPrice =data.price;
    chapterBookTitle=data.title;
    chapterTypeMain = data.testTypeBook;

     var imgSrc = "<img src='/funlearn/showProfileImage?id="+data.bookId+"&fileName="+data.coverImage+"&type=books&imgType=passport'  class=\"img-responsive\" alt=\"\">";
     document.getElementById("chapterBookImage").innerHTML=imgSrc;
     document.getElementById("chapterBookTitle").innerText=data.title;
     document.getElementById("chapterPublisher").innerText=data.publisher.name;
     document.getElementById("chapterOfferPrice").innerHTML="<i class=\"fa fa-inr\"></i>"+data.price;
     if(data.listPrice){
       document.getElementById("chapterListPrice").innerHTML=data.listPrice;
     }
     document.getElementById("chapterSingleLabel").innerHTML="Single "+data.testTypeBook;
    allChaptersPrice =  data.chaptersPrice;
     //console.log("chaptersPrice size="+chaptersPrice.size);


     var offersStr="";
    var chapterType="";
    noOfChapters=[];
    chaptersPrice=[];
     for(i=0;i<allChaptersPrice.length;i++){
       if(allChaptersPrice[i].noOfChapters==1){
         document.getElementById("chapterSinglePrice").innerHTML="<i class=\"fa fa-inr\"></i>"+allChaptersPrice[i].sellPrice;
         if(allChaptersPrice[i].displayPrice){
           document.getElementById("chapterSingleListPrice").innerHTML=allChaptersPrice[i].displayPrice;
         }
         chapterType=data.testTypeBook;
       }else chapterType=data.testTypeBook+"s";
       noOfChapters.push(allChaptersPrice[i].noOfChapters);
       chaptersPrice.push(allChaptersPrice[i].sellPrice);

       offersStr += "<li><i class=\"fa fa-tag\"></i>Get any "+allChaptersPrice[i].noOfChapters+" "+chapterType+" for only <i class=\"fa fa-inr\"></i> "+allChaptersPrice[i].sellPrice+"</li>" ;
     }
    document.getElementById("chapterOfferChapters").innerHTML=offersStr;
    document.getElementById("chapterSelectLabel").innerHTML="Select "+data.testTypeBook+"s";
    <g:remoteFunction controller="wonderpublish" action="getPurchasedChapters"  onSuccess='populateChaptersPart(data);' params="'bookId='+chapterBookId"/>

  }

  function populateChaptersPart(data){
    allChapters = data.allChapters;
    alreadyBoughtChapters = data.purchasedChapters;

    var chaptersStr="";
    var alreadyBought=false;
    for(i=0;i<allChapters.length;i++){
       if("true"==allChapters[i].previewChapter) continue;
       alreadyBought=false;

       //check to see if chapters are already bought.. if so, then skip them from the listing
       if(alreadyBoughtChapters!=null&&alreadyBoughtChapters.length>0){
         for(j=0;j<alreadyBoughtChapters.length;j++){
          if(alreadyBoughtChapters[j].chapterId==allChapters[i].id){
            alreadyBought=true;
            break;
          }
         }
       }
       if(alreadyBought) continue;
      chaptersStr += " <td class='chapter-name'>\n" +
        "                                <label class='not-active will-active'>\n" +
        allChapters[i].name +
        "                                    <input onchange='javascript:chapterSelected();' type='checkbox' value='"+allChapters[i].id+"' id='chapter_"+allChapters[i].id+"'>\n" +
        "                                    <span class='checkmark'></span>\n" +
        "                                </label>\n" +
        "                            </td>";

    }

    document.getElementById("chapterSelectionRows").innerHTML=chaptersStr;

  }

  function buyFullbook(){
    $('#buy-chapters-modal').modal('hide');
    buyBook(chapterBookId,chapterBookPrice,chapterBookTitle,'book',"");
  }

  function chapterSelected(){
    document.getElementById("chapterErrorMessage").innerHTML="";
    noOfChaptersLocal=0;
    selectedChaptersArray=[];
    for(i=0;i<allChapters.length;i++){
      var elementExists = document.getElementById("chapter_"+allChapters[i].id);
      if (typeof(elementExists) != 'undefined' && elementExists != null)
      {
        if(elementExists.checked) {
          noOfChaptersLocal++;
          selectedChaptersArray.push(elementExists.value);
        }
      }
    }

    var displayChapterType=chapterTypeMain;
    if(noOfChaptersLocal>1) displayChapterType=displayChapterType+"s";
    document.getElementById("chapterNoOfChapters").innerHTML="Unlock "+noOfChaptersLocal+" "+displayChapterType+" for";
    if(noOfChaptersLocal==0) {
      document.getElementById("chapterNoOfChapters").innerHTML="";
      document.getElementById("chapterTotalCost").innerHTML = "";
      selectedChaptersArray=[];
    }
    else calculatePrice(noOfChaptersLocal);

  }

  function calculatePrice(qty){
    var remainingQty = qty;
    cost=0;

    while(remainingQty>0){
      var closestQty=0;
      var closestQtyPrice=0;
      for(i=0;i<chaptersPrice.length;i++){
        console.log("number of chapters="+noOfChapters[i]+" and the price is="+chaptersPrice[i]);
        if(remainingQty>=noOfChapters[i]&&noOfChapters[i]>closestQty){
          closestQty = noOfChapters[i];
          closestQtyPrice = chaptersPrice[i];
        }

      }
      var noOfUnits = Math.floor(remainingQty/closestQty);
      remainingQty = remainingQty % closestQty;
      cost = cost + (noOfUnits*closestQtyPrice);

    }

    document.getElementById("chapterTotalCost").innerHTML=cost;
  }

  function unlock(){
    if(noOfChaptersLocal==0){
      document.getElementById("chapterErrorMessage").innerHTML="Select a "+chapterTypeMain;
    }else{
      $('#buy-chapters-modal').modal('hide');
      openRazorPay(cost, purchaseBookTitle,chapterBookId,"chapter", selectedChaptersArray.join());
    }
  }
</script>