<script>
    var loggedIn=false;
    <sec:ifLoggedIn>
    loggedIn=true;
    </sec:ifLoggedIn>
</script>

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>

<script>
    var purchaseBookId;
    var purchaseBookTitle;
    var purchaseBookPrice;
    var purchaseType;
    var purchaseChapterIds;
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    var name="";
    var image="";
    var discountId;
    var siteId="${session["siteId"]}";
    var prepjoySite="${session["prepjoySite"]}";
    var commonWhitelabel = "${session["commonWhiteLabel"]}";

    <%com.wonderslate.data.SiteMst siteMst = com.wonderslate.data.SiteMst.findById(session["siteId"])%>
    if("books"==defaultSiteName) {
        if(siteId==1) {
             name = "${grailsApplication.config.grails.appServer.siteName}";
            image="https://www.wonderslate.com/assets/wonderslate/logo.svg";
        }else if (siteId==5){
             name = "Blackspine";
            image="/assets/landingpageImages/blackspine_logo.png";
        }else if(siteId==3){
            name = "Arihant Publications";
            image="/assets/arihant/footer-logo.png";
        }
        else if(siteId==11){
            name = "EduGorilla Community Pvt. Ltd.";
            image="/assets/edugorilla/logo-icon.png";
        }
        else if(siteId==37){
            name = "Radian Books";
            image="/assets/radianbooks/radianbooks-logo.png";
        }
        else if(siteId==38){
            name = "Oswaal Books";
            image="/assets/oswaal/oswaal-logo-icon.png";
        }
        else if(siteId==39){
            name = "Oswal Publishers";
            image="/assets/oswalpublisher/logo-icon.png";
        }
        else if(siteId==46){
            name = "MTG Learning India";
            image="/assets/mtg/mtg-logo.png";
        }
        else if(siteId==21){
            name = "The Winners Institute";
            image="/assets/clients/winners_logo.jpg";
        }
        else if(prepjoySite){
            name = "The Prepjoy";
            image="/assets/prepJoy/prepjoy-website/lightlogo.png";
        } else if(commonWhitelabel == "true") {
            name = "${session["clientName"]}";
            image="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["logoIcon"]}";
        }
    }else{
        name="Utkarsh Classes and Edutech Pvt. Ltd.";
        image="https://www.e-utkarsh.com/assets/eutkarsh/logo.png"
    }

    function openRazorPay(price,title,bookId,type,chaptersId,discountId,subscriptionId,subscriptionDuration,subsStartingBookId) {
        var options = {
            "key": "${siteMst.razorPayKeyId}",
            "amount": (price*100).toFixed(2).toString(), // 2000 paise = INR 20
            "name": name,
            "description": title+" (Course "+bookId+")",
            "image": image,
            "handler": function (response) {
                if (!prepjoySite){
                    $('.loading-icon').removeClass('hidden');
                }else{
                    $('#loading').show();
                }

                $(".lmodal").show();
                $('body').removeClass('loaded');
                if (discountId !="undefined" && discountId !=null && discountId!=undefined ){
                    window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId="+bookId+"&type="+type+"&chaptersId="+chaptersId+"&discountId="+discountId+"&subscriptionId="+subscriptionId+"&subscriptionDuration="+subscriptionDuration+"&subsStartingBookId="+subsStartingBookId;
                }else{
                    window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId="+bookId+"&type="+type+"&chaptersId="+chaptersId+"&subscriptionId="+subscriptionId+"&subscriptionDuration="+subscriptionDuration+"&subsStartingBookId="+subsStartingBookId;
                }
            },
            "prefill": {
                "name": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].name!=null)?"${session['userdetails'].name}":""%>",
                "email": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].email!=null && session['userdetails'].email!='null')?"${session['userdetails'].email}":""%>",
                "contact": "<%=(session.getAttribute("userdetails")!=null && session['userdetails'].mobile!=null)?"${session['userdetails'].mobile}":""%>"
            },
            "notes": {
                 "bookId":bookId,
                 "username":"<%=(session.getAttribute("userdetails")!=null && session['userdetails'].username!=null)?"${session['userdetails'].username}":""%>",
                 "discountId":discountId,
                "subscriptionId":subscriptionId,
                "subscriptionDuration":subscriptionDuration,
                "subsStartingBookId":subsStartingBookId
                 <%if(session.getAttribute("affiliationCd")!=null){%>
                 ,"affiliationCd":"<%= session.getAttribute("affiliationCd") %>"
                 <%}%>
                <%if(session.getAttribute("fromInstitutePageinstituteId")!=null){%>
                ,"instituteId":"<%= session.getAttribute("fromInstitutePageinstituteId") %>"
                <%}else if  (session["userInstituteId"]!=null){%>
                ,"instituteId":"<%= session["userInstituteId"] %>"
                <%}%>

            },
            "theme": {
                "color": "#F37254"
            },
            "readonly": {
                "email": 1,
                "contact": 1
            }
        };
        var rzp1 = new Razorpay(options);
        rzp1.open();
    }

    function openRazorPayFromCart(cartId,totalPrice,username,name,mobile,email) {
        var options = {
            "key": "${siteMst.razorPayKeyId}",
            "amount": (totalPrice*100).toFixed(2).toString(), // 2000 paise = INR 20
            "name": name,
            // "description": title+" (Course "+bookId+")",
            "image": image,
            "handler": function (response) {
                if (!prepjoySite){
                    $('.loading-icon').removeClass('hidden');
                }else{
                    $('#loading').show();
                }

                $(".lmodal").show();
                $('body').removeClass('loaded');

                if(cartId!=null) {
                    window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&shoppingCartId="+cartId+"&type=book";

                } else {
                    if (discountId != "undefined" && discountId != null && discountId != undefined) {
                        window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId=" + bookId + "&type=" + type + "&chaptersId=" + chaptersId + "&discountId=" + discountId;
                    } else {
                        window.location = "/wonderpublish/purchase?razorpay_payment_id=" + response.razorpay_payment_id + "&bookId=" + bookId + "&type=" + type + "&chaptersId=" + chaptersId;
                    }
                }
            },
            "prefill": {
                "name":name,
                "email": email,
                "contact":mobile
            },
            "notes": {
                "shoppingCartId":cartId,
                //"bookId":bookId,
                "username":username

            },
            "theme": {
                "color": "#F37254"
            },
            "readonly": {
                "email": 1,
                "contact": 1
            },
            "modal": {
                "ondismiss": function () {
                    location.reload();
                }
            }
        };
        var rzp1 = new Razorpay(options);
        rzp1.open();
    }

   function buyBook(bookId,price,discountId,title,type,chapterIds){
       discountId=discountId;
       purchaseBookId = bookId;
       purchaseBookTitle = title;
       purchaseBookPrice = price;
       purchaseType= type;
       purchaseChapterIds=chapterIds;

       <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")==0){%>
       if (!prepjoySite){
           showRegistration();
       }else{
           openLoginModal();
       }
       <% } else {%>
            $('body').removeClass('loaded');
            <g:remoteFunction controller="wonderpublish" action="bookPurchased"  onSuccess='bookPurchasedCheck(data);'
      params="'bookId='+purchaseBookId" />
       <% } %>
    }

    function buyBookAppinApp(razorpayPaymentId,bookId,paymentFrom){
        <g:remoteFunction controller="wonderpublish" action="purchase"  onSuccess='buyBookAppinAppStatus(data);'
     params="'razorpay_payment_id='+razorpayPaymentId+'&bookId='+bookId+'&paymentFrom='+paymentFrom" />
    }

    function buyBookAppinAppStatus(data){
        if(data.status=="success" && data.poNo!=null){
            onBookPurchase("OK");
        }else{
            onBookPurchase("FAIL");
        }
    }

    function onBookPurchase(status) {
        JSInterface.onBookPurchase(status);
    }


    function addToMyLibrary(bookId,price,title){
        purchaseBookId = bookId;
        purchaseBookTitle = title;
        purchaseBookPrice = price;
        <sec:ifLoggedIn>
        $('body').removeClass('loaded');
        <g:remoteFunction controller="wonderpublish" action="bookPurchased"  params="'bookId='+purchaseBookId" onSuccess='bookPurchasedCheck(data);'/>
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
        <%if("12".equals(""+session["siteId"])||"20".equals(""+session["siteId"])||"23".equals(""+session["siteId"])){%>
        $('.evidyaLogin').show();
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
        <%}else{%>
        if (!prepjoySite){
            showRegistration();
        }else{
            openLoginModal();
            $('#addBookToLibPrep').modal('hide');
        }
        <%}%>
        </sec:ifNotLoggedIn>
    }
    
    function bookPurchasedCheck(data){
        if (!prepjoySite){
            showLoader();
        }
        //$('.loading-icon').removeClass('hidden');
        if(data.bookAlreadyPurchased==false){
            if(("0"==(""+purchaseBookPrice)||"0.0"==(""+purchaseBookPrice))) {
                <g:remoteFunction controller="wonderpublish" action="addFreeBookToLibrary"  params="'bookId='+purchaseBookId"  onSuccess='bookAddedToLibrary(data);'  />
            } else {
                if("chapter"==purchaseType) {
                    getInfo(purchaseBookId);

                }else{
<% if(grailsApplication.config.grails.appServer.default=="eutkarsh" && grailsApplication.config.grails.appServer.payGateway=="CCA") { %>
                    window.location = "/creation/ccavRequestHandler?book_id="+purchaseBookId+"&book_price="+purchaseBookPrice+"&book_title="+purchaseBookTitle+"&type="+purchaseType;
<%  } else { %>
                    openRazorPay(purchaseBookPrice, purchaseBookTitle, purchaseBookId,purchaseType,null,discountId);
<%  } %>                    
                }
            }
        }
        else {
            if(confirm("This book/test is already in your library. Would you like to check your library now?")) {
                if(siteId == 12){
                    $('#libraryTab [href="#mylibrary"]').tab('show');
                }
                else if (!(instituteLibrary=="true" || sageOnly=="true")) {
                    window.location = "/wsLibrary/myLibrary?mode=mybooks";
                }
                else if (!prepjoySite){
                    window.location = "/library?siteName=${session['entryController']}";
                }
            }
        }
    }
    
    function bookAddedToLibrary(data){
        if("added"==data.status){
            if("test"==data.bookType){
                if (!prepjoySite){
                    window.location="/library?siteName=${session['entryController']}&mode=testSeries";
                }else{
                    window.location.reload();
                }
            }
            else{
                if(siteId==12 || siteId==20 || siteId==23){
                    $('.loading-icon').addClass('hidden');
                   $('#readContinue').modal('show');
                   $('#messageLibrary').text('Book is successfully added to your library.');
                    $('#readNow').on('click',function () {
                        window.location = "/smartebook/" + purchaseBookId + "/topic/section/" + encodeURIComponent(purchaseBookTitle) + "?siteName=${session['entryController']}";
                    });
                }
                else {
                    if("books"==defaultSiteName && siteId==1){
                        window.location = "/"+replaceAll(purchaseBookTitle,' ','-')+"/ebook?bookId="+purchaseBookId+"&siteName=${session['entryController']}";
                    }else if(!prepjoySite){
                        window.location = "/smartebook/" + purchaseBookId + "/topic/section/" + purchaseBookTitle + "?siteName=${session['entryController']}";
                    }else{
                        window.location.reload();
                    }

                }
            }

        } else if("Invalid IP range"==data.status){
            alert("You do not have access to these books. Contact your librarian to get access.")
        }else if("Book Not Available For This IP"==data.status){
            alert("Your library currently do not subscribe to this title. Contact your librarian.")
        }  else{
            alert("Attempt to add invalid book");
        }
    }
    
    function showRegistration() {
            loginOpen();
        <g:remoteFunction controller="wonderpublish" action="storeBookIdForPurchase" params="'bookId='+purchaseBookId" />
    }

</script>
