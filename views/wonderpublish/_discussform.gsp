<div class="discussion-form">
    <textarea class="form-control" id="question" rows="4"></textarea>
    <button type="button" class="btn submit-question-btn" onclick="javascript:submitQuestion();">Submit Query</button>
    <div id="addedQuestion">


    </div>
</div>



<script>
    var firstQuestion=-1;
    function submitQuestion(){
        $("#questionalert").hide(500);
        if(document.getElementById("question").value==""){
            $("#questionalert").show(500);
        }else{
            var question = document.getElementById("question").value;
            $("#loading2").show();
            document.getElementById("question").value="";
            console.log("the chapter id ="+${params.resId});
            %{--alert(${params.resId});--}%
            <g:remoteFunction controller="wonderpublish" action="addQuestion" onSuccess='displayAddedQuestion(data);' params="'&question='+question+'&resId='+${params.resId}"/>
        }
    }

    function displayAddedQuestion(data){
            // alert("inside");
        $("#loading2").hide();
        if(data.result=="noaccess"){}
        else {
            var question = data.question;
            var questionStr = "   <div class='row'>" +
                "    <div class='col-md-10 col-md-offset-1'><p class='fontsize16'>"+question.question+"</p>" +
                "            </div>" +
                "    </div>"+
                "   <div class='row smallerText greytext'>" +

                "    <div class='col-md-8 text-right'><p class='dark1text'>added by You</p>" +
                "            </div>" +
                "    </div>"+ "   <div class='row'>" +
                "    <div class='col-md-10 col-md-offset-1'><hr>" +
                "            </div>" +
                "    </div>";
            document.getElementById("addedQuestion").innerHTML = questionStr + document.getElementById("addedQuestion").innerHTML;
            $("#addedQuestion").show(500);

        }
        getDFQuestions();
    }

    function getDFQuestions(){
        $("#loading2").show();
        <g:remoteFunction controller="wonderpublish" action="getQuestions" onSuccess='displayQuestions(data);'  params="'resId='+${params.resId}"/>
    }

    function displayQuestions(data){
        firstQuestion = -1;
        $("#loading2").hide();
        if(data.result=="noaccess"){}
        else {
            var questions = data.questions;
            var createdDate;
            var questionStr="";
            var answeroption="";
            for(i=0;i<questions.length;i++) {
                createdDate=moment(questions[i].dateCreated).format("DD-MMM-YY hh:mm");
                console.log("the answer count is="+questions[i].answercount);
                if(questions[i].answercount==0) answeroption = "No answer received yet.";
                else answeroption = "<a href='javascript:showAnswers("+questions[i].id+")'>Show answers</a>";
                questionStr += "   <div class='row'>"+
                    "    <div class='col-md-10 col-md-offset-1'><p class='fontsize16'>" + questions[i].question + "</p>" +
                    "            </div>" +
                    "    </div>" +
                    "   <div class='row smallerText greytext'>" +
                    " <div class='col-md-2 col-md-offset-1' id='answeroption"+questions[i].id+"'>"+answeroption+" </div>"+
                    " <div class='col-md-3 '><a href='javascript:answerthisquestion("+questions[i].id+")'>Answer this question</a> </div>"+
                    "    <div class='col-md-6  text-right'><p class='dark1text'>asked by "+questions[i].name+" on "+createdDate+"</p></div>" +
                    "    </div>"+
                    "   <div class='row'>" +
                    "    <div class='col-md-10 col-md-offset-1' id='answerholder"+questions[i].id+"'>" +
                    " </div>" +
                    "    </div>"+
                    "   <div class='row' style='display: none' id='displayanswer"+questions[i].id+"'>" +
                    "    <div class='col-md-10 col-md-offset-1 smallText' >" +
                    " </div>" +
                    "    </div><hr>";
            }
            if(questionStr.length==0) questionStr = "<div class='row'><div class='col-md-10 col-md-offset-1 smallText greytext'>**No questions asked in this chapter yet.</div></div>";
            //     document.getElementById("addedQuestion").innerHTML = questionStr + document.getElementById("addedQuestion").innerHTML;
            document.getElementById("addedQuestion").innerHTML = questionStr;
            $("#addedQuestion").fadeIn(300);
            getDFAnswers();

        }
    }


    function answerthisquestion(id){
        var answerStr = " <div class='row'>" +
            "            <div class='col-md-10 col-md-offset-1'><br><textarea  rows='4' class='form-control' id='answer"+id+"' name='answer"+id+"' placeholder='Enter your answer here'></textarea></div>" +
            "        </div><br>" +
            "        <div class='row'>" +
            "            <div class='col-md-10 col-md-offset-1 text-center'  id='answersubmit'><button type='button' onclick='javascript:submitAnswer("+id+")' class='btn btn-success' >Submit Answer</button></div>" +
            "        </div>" +
            "        <div class='row'><br><div class='col-md-10 col-md-offset-2 red' id='answeralert"+id+"' style='display: none'>Enter the answer before submitting.</div></div><br>" +
            "        <div class='row text-center' style='display: none' id='loading_"+id+"'><div class='col-md-12 orange' ><i class='fa fa-spinner fa-2x fa-spin'></i> </div></div>";
        document.getElementById("answerholder"+id).innerHTML = answerStr;
        $("#answerholder"+id).show(500);
        $("#answer"+id).focus();


    }

    function submitAnswer(id){

        $("answeralert"+id).hide(500);
        if(document.getElementById("answer"+id).value==""){
            $("#answeralert"+id).show(500);
        }else{
            var answer = document.getElementById("answer"+id).value;
            $("#loading_"+id).show();
            document.getElementById("answer"+id).value="";
            $("#answerholder"+id).hide(500);
            firstQuestion = id;
            <g:remoteFunction controller="wonderpublish" action="addAnswer" onSuccess='displayAddedAnswer(data);' params="'chapterId='+previousChapterId+'&questionId='+id+'&answer='+answer"/>
        }
    }

    function displayAddedAnswer(data){
        getDFAnswers();
    }

    function getDFAnswers(){
        if(firstQuestion!=-1){
            <g:remoteFunction controller="wonderpublish" action="getAnswers" onSuccess='displayAnswers(data);' params="'chapterId='+previousChapterId+'&questionId='+firstQuestion"/>

        }
    }

    function displayAnswers(data){
        var answers = data.answers;
        var answerStr="";
        var createdDate;
        for(i=0;i<answers.length;i++) {
            createdDate=moment(answers[i].dateCreated).format("DD-MMM-YY hh:mm");
            answerStr +=
                "   <div class='row smallerText greytext'>" +
                " <div class='col-md-1 col-md-offset-1'></div>"+
                "    <div class='col-md-10'><hr>"+answers[i].answer+"</div>" +
                "    </div>"+
                "   <div class='row smallerText greytext'>" +
                "    <div class='col-md-12  text-right'><p class='dark1text'>by "+answers[i].name+" on "+createdDate+"</p></div>" +
                "    </div>";
        }
        if(answers.length==0) answerStr="<div class='row'><div class='col-md-10 col-md-offset-1 smallText greytext'>**No answers have been received to this question yet.</div></div>";

        document.getElementById("displayanswer"+firstQuestion).innerHTML=answerStr;
        document.getElementById("answeroption"+firstQuestion).innerHTML="<a href='javascript:showAnswers("+firstQuestion+")'>Show answers</a>";

        $("#displayanswer"+firstQuestion).show(500);
    }

    function showAnswers(id){
        firstQuestion=id;
        getDFAnswers();
    }
</script>