<script>
    function populateQASection(cData) {


        for (var i = 0; i < cData.length; ++i) {
            var data = cData[i];
            showQAndA(data.link);
        }
    }
    function showQAndA(quizId) {
        console.log("I was called and the quizId is "+quizId);
        <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+quizId" onSuccess = "showQuestionAndAnswers(data);"/>
        $('#read .less-width').hide();
        $('.chapter-details-area').css({
            'overflow' : 'hidden',
            'overflow-y' : 'scroll'
        });

    }

    function showQuestionAndAnswers(data){
        var mjaxURL  = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML';
        $.getScript(mjaxURL, function() {
        var qAndAData;
        var qaStr="";
      if(siteId !=9)  qaStr +="<a href='javascript:backSolution()' class='pr-back-btn'><i class=\"material-icons\">\n" +
          "keyboard_arrow_left\n" +
          "</i>Back</a>" +"<ol class='read-question-answer'>";
        qas = data.results;
        var answerString="";

        for(var index = 0; index < qas.length; ++index) {


            if(siteId==9&&qas[0].resType == "QA"&&qas[index].answer.length>300){
                answerString = "<div class='read-answer answer-collapsed col-md-12' id='read-answer-"+index+"'>"+qas[index].answer+"</div>" + "<a class='sage-read-more' id='show-full-answer-"+index+"' href='javascript:displayFullAnswer("+index+");'>See full answer</a>";
            }else{
                    answerString = "<div class='read-answer col-md-12'>"+qas[index].answer+"</div>";
            }

            qaStr +=  "<div class='col-md-12 read-question-answer-wrapper read-question-answer'>"+
                "<li>"+
                "<div class='read-question col-md-12'>"+qas[index].ps+"</div>"+
                answerString +
                "</li></div>";

        }
        qaStr+= "</ol>";
        if(siteId==9) {
            if (qas[0].resType == "QA") {
                document.getElementById("content-data-QA").innerHTML = qaStr;
                MathJax.Hub.Queue(["Typeset", MathJax.Hub, "content-data-QA"]);
            }
            else {
                document.getElementById("content-data-shortQAndA").innerHTML = qaStr;
                MathJax.Hub.Queue(["Typeset", MathJax.Hub, "content-data-shortQAndA"]);
            }
        }else{
            document.getElementById("qainwebref").innerHTML = qaStr;
            $("#additional-refs").hide();
            $("#addRefButton").hide();
            $("#qainwebref").show();
        }
        });
    }


    function displayFullAnswer(index) {
        var readHeight = $('#book-read-material').height();
        var chapterWrapper = $('.read-book-chapters');
        if($('#show-full-answer-'+index).html() == 'See full answer') {
            $('#show-full-answer-'+index).html('Hide full answer');
        } else {
            $('#show-full-answer-'+index).html('See full answer');
            $(window).scrollTop('#read-answer-'+index);

        }
        $('#read-answer-'+index).toggleClass('answer-collapsed');
    }
    function backSolution() {
        $('#qainwebref').hide();
        $('#additional-refs').show();
    }

</script>