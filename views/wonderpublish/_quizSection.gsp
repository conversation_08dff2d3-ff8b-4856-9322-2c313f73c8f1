<script>
    function populateQuizSection(cData) {
        quizPresent = true;
        var cStr="";
        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];
            var testSeriesQuiz=false;
            if (instructor && data.sharing == "createdbyuser") {
                showShare = true;
            }
            if (instructor && data.sharing == null && instructorControlledBook) {
                showShare = true;
            }
            if(siteId == 9) {
                cStr += "<div class='quiz-item-wrapper'>" +
                    "<div class='quiz-item'>" +
                    "<p class='quiz-name'>"+data.title+"</p>" +
                    "</div>" +
                    "<div class='quiz-buttons'>" +
                    "<a href='javascript:showQuizRead(" + data.link + "," + data.id + ")' class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Learn</a>" +
                    "</div>"+
                    "</div>"+
                    "<div class='quiz-item-wrapper'>" +
                    "<div class='quiz-item'>" +
                    "<p class='quiz-name'>"+data.title+"</p>" +
                    "</div>" +
                    "<div class='quiz-buttons'>" +
                    "<a href='#' class='btn btn-block quiz-learn-btn waves-effect waves-ripple dropdown-toggle' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>Practice <span class='caret'></span></a>"+
                    "<ul class='dropdown-menu practice-dropdown'>" +
                    "<li>" +
                    "<a href='javascript:showQuizImmediate(true," + data.link + "," + data.id + ")'>Immediate Feedback</a>"+
                    "</li>" +
                    "<li>" +
                    "<a href='javascript:showQuiz(" + data.link + "," + data.id + ")'>Summarized</a>"+
                    "</li>" +
                    "</ul>" +
                    "</div>"+
                    "</div>";
            } else {
                cStr += "<div class='quiz-item-wrapper'>" +
                    "<p class='quiz-number'>"+(i+1)+"</p>" ;
                if(showShare) {

                    cStr += "<div class=\"dropdown\" style='position: absolute;top:0;right:0;padding: 5px 9px;'> <a class=\"dropdown-toggle\" id=\"android-menu\" data-toggle=\"dropdown\" aria-haspopup=\"true\"" +
                        " aria-expanded=\"true\" style=\"cursor:pointer;\">" +
                        "<i class=\"fas fa-ellipsis-v\"></i></a> <ul class=\"dropdown-menu\" aria-labelledby=\"android-menu\"> <li><a href=\"javascript:showShareOptions("+data.id+",false);\">Share</a></li> " +
                        "<li><a href=\"javascript:showShareOptions(" + data.link + ",true);\">Share as assignment</a></li>"+
                        "</ul> </div>";
                }
                cStr += "<div class='quiz-item'>" +
                    "<p class='quiz-name'>"+data.title+"</p>" +
                    "</div>" +
                    "<div class='quiz-buttons'>";
                if ("Multiple Choice Questions" == data.resType)
                {
                     if(data.testStartDate){
                         testSeriesQuiz=true;
                         var displayName="Take test";
                         var testAlreadyTaken=false;
                         if(seenResources.includes(","+data.id+",")) {
                             testAlreadyTaken=true;
                             displayName="Re attempt";
                         }

                         if("true"==data.testEnded) cStr += "<p class='testStarts'>Test already ended</p>";
                         else if("true"==data.testStarted){
                             cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.link + "&resId=" + data.id + "' class='btn quiz-practice-btn waves-effect waves-ripple'>"+displayName+"</a>";

                         }else cStr += "<p class='testStarts'> Test Starts On </p>"+
                             "<p>"+
                             "<span class='testDate'>"+moment.utc(data.testStartDate).format("D MMM YYYY")+"</span>"+
                             "<span class='seperator'>|</span>"+
                             "<span class='testTime'>"+moment.utc(data.testStartDate).format("h:mm a")+"</span>"+
                             "</p>";
                      
                             if(data.testResultAnnounced=="true")
                             cStr += "<a href='javascript:getRankDetailsForTestSeries(" + data.id + ")' class='btn btn-block showRank waves-effect waves-ripple'>Show Rank</a>";
                             else if(data.testResultDate){
                                 cStr += "<p class='testStarts'> Results on </p>"+
                                     "<p>"+
                                     "<span class='testDate'>"+moment.utc(data.testResultDate).format("D MMM YYYY")+"</span>"+
                                     "<span class='seperator'>|</span>"+
                                     "<span class='testTime'>"+moment.utc(data.testResultDate).format("h:mm a")+"</span>"+
                                     "</p>";
                         }



                     }
                     else {
                         cStr += "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&fromTab=quiz&viewedFrom=quiz&quizId=" + data.link + "&resId=" + data.id + "&resName=" + data.title+"' class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Learn</a>" +
                             "<div class='d-flex align-items-center mt-4'>"+
                             "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=quiz&viewedFrom=quiz&quizId=" + data.link + "&resId=" + data.id + "&resName=" + data.title+"' class='btn quiz-practice-btn waves-effect waves-ripple'>Practice</a>";
                     }
                }else if ("Forms" == data.resType){
                    cStr +=     "<a href='/formstesting/" + data.title + "?quizId="+data.link+"' target='_blank' class='btn quiz-practice-btn waves-effect waves-ripple'>Practice</a>";
                } else{
                    cStr +=     "<a href='javascript:showQuizRead(" + data.link + "," + data.id + ")' class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Learn</a>" +
                            "<div class='d-flex align-items-center mt-4'>"+
                        "<a href='javascript:showQuiz(" + data.link + "," + data.id + ")' class='btn quiz-practice-btn waves-effect waves-ripple'>Practice</a>";

                }
                if(!testSeriesQuiz) {
                    if (loggedInUser) {
                        cStr += "<a href='javascript:getQuizScoreAnalytics(" + data.id + ")' class='score-btn'><i class='material-icons'>assignment</i></a>"+
                                "</div>";
                    } else {
                        cStr += "<a href='#' class='btn btn-block score-btn waves-effect waves-ripple disabled'><i class='material-icons'>assignment</i></a>"+
                                "</div>";
                    }
                }
                cStr += "</div>" +
                    "</div>";
            }
            cStr +="</div></div>";
        }
        document.getElementById('content-data-quiz').innerHTML=cStr;
    }

    function getQuizScoreAnalytics(quizId){
        <g:remoteFunction controller="analytics" action="getQuizScoreAnalytics" params="'quizid='+quizId"
    onSuccess = "showQuizScoreAnalytics(data);"/>
        $('.loading-icon').removeClass('hidden');
        $("#quizanalytics").modal('show');

    }

    function getRankDetailsForTestSeries(resId){
        <g:remoteFunction controller="wonderpublish" action="getRankDetails" onSuccess='displayRankDetails(data)'
                params="'testSeriesBook=true&resId='+resId" />
    }

</script>