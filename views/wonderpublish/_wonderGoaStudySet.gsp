<div class='modal fade in' tabindex='-1' role='dialog' id='flashCardModal'>
  <div class='modal-dialog  modal-dialog-centered' role='document'>
    <div class='modal-content'>
      <div class='modal-header text-center'>
        <button type='button' class='close' data-dismiss='modal'>
            &times;
        </button>
        <h4 class='modal-title' id="flashCardTitle">Flashcard</h4>
      </div>
      <div class='modal-body quiz-modal-body'>
        <div id="flash-card-carousel-modal" class="carousel slide flash-card-slider" data-interval="false">
          <ol class="carousel-indicators">
            <li data-target="#flash-card-carousel-modal" data-slide-to="0" class="active"></li>
          </ol>
          <div class="carousel-inner row quiz-section" id="falsh-card-modal-body" role="listbox"></div>

          <div class="carousel-control-wrapper col-md-12">
            <a class="left carousel-control" id="carousel-btn-left-modal" href="#flash-card-carousel-modal" role="button" data-slide="prev">
              <i class="material-icons">chevron_left</i>
            </a>
            <p class="card-number" id="card-number-modal"></p>
            <a class="right carousel-control" id="carousel-btn-right-modal" href="#flash-card-carousel-modal" role="button" data-slide="next">
              <i class="material-icons">chevron_right</i>
            </a>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="container study-set-wrapper-continer revision_ui" id="study-set-wrapper-container" style="display: none" >
    <div class="row">
    <div class="col-md-10 text-center"></div>
        <a href="javascript:closeRevisionScreen()" class="pr-back-btn"><i class="material-icons">
            keyboard_arrow_left
        </i>Back</a>
        %{--<div class="col-md-1 text-right"><i class="fas fa-times"><a href='javascript:closeRevisionScreen()'>&nbsp;Close</a></i></div>--}%
    </div>
  <div id="content-data-keyValues" class="row"></div>

  <div class="chapternav-wrapper">
    <ul class="chapternav-items">
      <li class="chapternav-item">
        <a class="chapternav-link" href="javascript:showTrainerQuiz('FLASHCARDS');">
          <figure class="chapternav-icon">
            <i class="material-icons">view_carousel</i>
          </figure>
          <span class="chapternav-label">Flash</span>
          <span class="chapternav-label">Cards</span>
        </a>
      </li>
      <li class="chapternav-item">
        <a class="chapternav-link" href="javascript:showTrainerQuiz('MCQ');">
          <figure class="chapternav-icon">
            <i class="material-icons">format_list_bulleted</i>
          </figure>
          <span class="chapternav-label">Multiple</span>
          <span class="chapternav-label">Choice Q</span>
        </a>
      </li>
      <li class="chapternav-item">
        <a class="chapternav-link" href="javascript:showTrainerQuiz('TOF');">
          <figure class="chapternav-icon">
            <i class="material-icons">drag_handle</i>
          </figure>
          <span class="chapternav-label">True or</span>
          <span class="chapternav-label">False</span>
        </a>
      </li>
    </ul>
  </div>

  <div class="study-set-wrapper revision_setup" id="study-set-wrapper">
      <div class="row" id="revisionTitleInput">
          <div class="ss col-md-offset-4 col-md-4 study-set-item">
              <div class="form-group">
                  <textarea class="study-set-textarea" placeholder="Revision set name" id="revisionTitle"></textarea>
              </div>
          </div>
      </div>
    <form id="study-set-form">
      <div class="col-md-12 study-set-main" id="keyvalueholder">
        <span class="term-counter"></span>
        <div class="col-md-3 study-set-item">
          <div class="form-group">
            <textarea class="study-set-textarea" placeholder="Enter Term" id="term"></textarea>

          </div>
        </div>
        <div class="col-md-9 study-set-item">
          <div class="form-group">
            <textarea class="study-set-textarea" placeholder="Enter Definition" id="definition"></textarea>

          </div>
        </div>
      </div>
    </form>

    <div class="col-md-12 add-study-card-btn-wrapper">
        <a href="javascript:cancelKeyValueCard();" class="add-study-card-btn">
            <span>Cancel</span>
        </a>
      <a href="javascript:addKeyValueCard();" class="add-study-card-btn">
        <span>+ Add Card</span>
      </a>
    </div>
  </div>
  <div id="study-set-from-notes"></div>

</div>

<script>
    var newStudySet=false;
    var revisionName;
  function createNewStudySet(){
      $("#content-data-all").hide();
      $("#content-data-studyset-nosets").hide();
      $("#content-data-studyset").hide();
      $("#study-set-wrapper-container").show();
      newStudySet=true;
      studySetResId=-1;
      document.getElementById("study-set-wrapper").innerHTML="<div class=\"\" id=\"revisionTitleInput\">\n" +
          "        <div class=\"study-set-item\">\n" +
          "<span class=\"input-studyset\">\n" +
          "                <textarea class=\"study-set-textarea\" placeholder=\"\" id=\"revisionTitle\"></textarea>\n" +
          "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"revisionTitle\">\n" +
          "                                <span class=\"input-label-content input-label-content-login\">Revision Set Name</span>\n" +
          "                            </label>\n" +
          "                        </span>"+
          "        </div>\n" +
          "    </div>\n" +
          "    <form id=\"study-set-form\">\n" +
          "      <div class=\"study-set-main\" id=\"keyvalueholder\">\n" +
          "        <span class=\"term-counter\"></span>\n" +
              "<div class='d-flex justify-content-center bg-revCard flex-wrap'>"+
          "        <div class=\"study-set-item\">\n" +
          "<span class=\"input-studyset termDimen\">\n" +
          "            <textarea class=\"study-set-textarea\" placeholder=\"Enter Term\" id=\"term\"></textarea>\n" +
          "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"term\">\n" +
          "                                <span class=\"input-label-content input-label-content-login\">Term</span>\n" +
          "                            </label>\n" +
          "                        </span>"+
          "        </div>\n" +
          "        <div class=\"study-set-item\">\n" +
                        "<span class=\"input-studyset\">\n" +
          "            <textarea class=\"study-set-textarea\" placeholder=\"Enter Definition\" id=\"definition\"></textarea>\n" +
          "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"definition\">\n" +
          "                                <span class=\"input-label-content input-label-content-login\">Definition</span>\n" +
          "                            </label>\n" +
          "                        </span>"+
          "        </div>\n" +
              "</div>"+
          "      </div>\n" +
          "    </form>\n" +
          "\n" +
          "    <div class=\"col-md-12 add-study-card-btn-wrapper\">\n" +
          "      <a href=\"javascript:cancelKeyValueCard();\" class=\"cancel-study-card-btn\">\n" +
          "        <span>Cancel</span>\n" +
          "      </a>\n" +
          "      <a href=\"javascript:addKeyValueCard();\" class=\"add-study-card-btn\">\n" +
          "        <span>+ Add Card</span>\n" +
          "      </a>\n" +
          "    </div>";
  }

  function showTrainerQuiz(quizType){
    if(studySetResId!=-1) {
      if ("MCQ" == quizType){
          if(studySetKeyValues.length>1)
        quizFromFlash(studySetResId,'Multiple Choice Questions');
      else {
              alert("You need to atleast 2 key terms for MCQ");
          }
      } else if ("TOF" == quizType) {
          if(studySetKeyValues.length>1)
        quizFromFlash(studySetResId,'True or False');
          else {
              alert("You need to atleast 2 key terms for True or False");
          }
      } else if ("FLASHCARDS" == quizType) {
        $('#flashCardModal').modal('show');
        getFlashCards(studySetResId);
      }
    }else{
      alert("Please add study set first");

    }
  }

function saveKeyValues(noOfItems){
  var allEntered=true;
    if(document.getElementById("revisionTitle").value==""){
         document.getElementById("revisionTitle").focus();
        allEntered = false;

    }else {
        for (i = 0; i < noOfItems; i++) {
            if (document.getElementById("notesterm" + (i + 1)).value == "") {
                allEntered = false;
                document.getElementById("notesterm" + (i + 1)).focus();
                break;

            } else if (document.getElementById("notesdefinition" + (i + 1)).value == "") {
                alert("Enter Definition");
                document.getElementById("notesdefinition" + (i + 1)).focus();
                allEntered = false;
                break;

            }
        }
    }
  if(allEntered){
      var title=document.getElementById("revisionTitle").value;
    var params;
     params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&title="+title;
    for (i = 0; i < noOfItems;i++) {
      params += "&term"+i+"="+document.getElementById("notesterm"+(i+1)).value;
      params += "&definition"+i+"="+document.getElementById("notesdefinition"+(i+1)).value;

    }
      $('.loading-icon').removeClass('hidden');
    <g:remoteFunction controller="wonderpublish" action="addKeyValues" onSuccess='keyValuesSaved(data);' params="params"></g:remoteFunction>

  }
}


  function getStudySets(id){
      $("#content-data-all").hide();
      $('.loading-icon').removeClass('hidden');
      initCallGoogle(id,"Revision");
      <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displayStudySets(data);' params="'resId='+id"/>
      if(loggedInUser){

              updateUserView(id,"all","revision");

      }
      else{

              updateView(id,"all","revision");

      }
  }

function keyValuesSaved(data){
    if(studySetResId==-1){
        //new revision set. Add to the existing list
        var cStr = "<div class='quiz-item-wrapper'>" +
                "<p class='quiz-number'>"+(i+1)+"</p>" ;

        cStr += "<div class='quiz-item'>" +
                "<p class='quiz-name'>"+data.title+"</p>" +
                "</div>" + "<div class='quiz-buttons'>";

        cStr +="<a href='javascript:getStudySets(" + data.resId + ")' class='btn btn-block quiz-practice-btn waves-effect waves-ripple'>Revise</a>" ;
        cStr += "</div>" + "</div>";
        cStr +="</div></div>";

        document.getElementById('content-data-studyset').innerHTML=cStr+document.getElementById('content-data-studyset').innerHTML;
    }

    $('.loading-icon').addClass('hidden');
    if(allTabMode){
        addRevisionToAllTab(data.title,data.resId);
    }
    getStudySets(data.resId);
}
    function addRevisionToAllTab(linkName,resId){
        var cStr = "<div class=\"container\">\n" +
            "<div class=\"all-container\">";
        cStr +="<div class=\"container-wrapper\">\n" +
            "<div class='d-flex justify-content-between align-items-center'>"+
            "<p class='quiz-number'></p>";
        cStr +="</div>";
        cStr += "        <div class=\"media\">\n" ;
        cStr +="            <i class=\"align-self-center material-icons yellow\">\n" +
            "                note\n" +
            "            </i>\n";
        cStr += "            <div class=\"media-body\">\n" +
            "                <span class=\"date mt-0\">Added by you now</span>\n" +
            "                <p class=\"title\">"+linkName+"</p>\n" +
            "                <div class=\"d-flex align-items-center justify-content-between\">\n" ;
        cStr += "                    <a class=\"mb-0 readnow\" href='javascript:getStudySets(" + resId + ")'>Revise</a>\n";


        cStr +=  "                </div>\n" +
            "            </div>\n" +
            "        </div>\n" +
            "    </div>\n";
        cStr +=     "        </div>\n" +
            "      </div>" ;
        document.getElementById('content-data-all').innerHTML += cStr;
    }
function cancelKeyValueCard(){
    if(revisionPresent){
          $('#content-data-studyset').show();
          $('#study-set-wrapper-container').hide();
      }
      else{
        $('#content-data-studyset-nosets').show();
        $('#study-set-wrapper-container').hide();
    }
}
function addKeyValueCard() {
    var noOfItems=1;

    if(document.getElementById("revisionTitle").value==""){
        document.getElementById("revisionTitle").focus();
    } else if(document.getElementById("term").value=="") {
        document.getElementById("term").focus();
    } else if(document.getElementById("definition").value=="") {
        document.getElementById("definition").focus();
    } else {
        var title=document.getElementById("revisionTitle").value;
        //  $('.loading-icon').removeClass('hidden');
        var params = "noOfItems="+noOfItems+"&chapterId="+previousChapterId+"&studySetResId="+studySetResId+"&title="+title;

        for (i = 0; i < noOfItems;i++) {
            params += "&term"+i+"="+document.getElementById("term").value;
            params += "&definition"+i+"="+encodeURIComponent(document.getElementById("definition").value);
        }

        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="addKeyValues" onSuccess='keyValuesSaved(data);'params="params"></g:remoteFunction>
  }
}



function updateResId(data){
  $('#term').attr('id', 'term'+data.keyValueId);
  $('#definition').attr('id', 'definition'+data.keyValueId);
  $('#keyvalueholder').attr('id', 'keyvalueholder_'+data.keyValueId);
  document.getElementById('term'+data.keyValueId).setAttribute("onchange", "javascript:flashCardEdited("+data.keyValueId+");");
  document.getElementById('definition'+data.keyValueId).setAttribute("onchange", "javascript:flashCardEdited("+data.keyValueId+");");
  document.getElementById('keyvalueholder_'+data.keyValueId).setAttribute("onclick", "javascript:deleteCard(("+data.keyValueId+");");
  resId = data.resId;
  studySetSection = "<div class='col-md-12 study-set-main' id='keyvalueholder'>" +
    "<span class='term-counter'>" +
    "<input type='hidden'>" +
    "</span>" +
    "<div class='col-md-3 study-set-item'>" +
    "<div class='form-group'>" +
    "<textarea class='study-set-textarea' placeholder='Enter Term' id='term'></textarea>" +
    "</div>" +
    "</div>" +
    "<div class='col-md-9 study-set-item'>" +
    "<div class='form-group'>" +
    "<textarea class='study-set-textarea' placeholder='Enter Definition' id='definition'></textarea>" +
    "</div>" +
    "</div>" +
    "</div>";
  $(studySetForm).append(studySetSection);
  $('.loading-icon').addClass('hidden');
}

function deleteCard(keyValueId) {
    $("#keyvalueholder_"+keyValueId).hide();
    <g:remoteFunction controller="wonderpublish" action="deleteFlashCard" params="'keyValueId='+keyValueId"></g:remoteFunction>
}

function flashCardEdited(keyValueId){
    var term =  document.getElementById("term"+keyValueId).value;
    var definition = encodeURIComponent(document.getElementById("definition"+keyValueId).value);
    <g:remoteFunction controller="wonderpublish" action="updateFlashCard"
            params="'term='+term+'&definition='+definition+'&keyValueId='+keyValueId"></g:remoteFunction>
}

  var studySetKeyValues;

  function displayStudySets(data){
      var keyValues=data.keyValues;
      var disabledString="disabled";
      if(data.canEdit=="true") {
          disabledString="";
      }

      studySetKeyValues = data.keyValues;
      newStudySet=false;
      var cStr="<div  id=\"revisionTitleInput\">\n" +
          "        <div class=\"ssss study-set-item\">\n" +
          "            <div class=\"form-group\">\n" +

              "<span class=\"input-studyset\">\n" +
          "                <textarea "+disabledString+" class=\"input-field input-field-login\" placeholder=\"Revision set name\" id=\"revisionTitle\"></textarea>\n" +
          "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"revisionTitle\">\n" +
          "                                <span class=\"input-label-content input-label-content-login\">Revision Set Name</span>\n" +
          "                            </label>\n" +
          "                        </span>"+
          "            </div>\n" +
          "        </div>\n" +
          "    </div>";
      studySetResId = data.resId;

      for(i=0;i<keyValues.length;i++) {
          cStr += "<div class=\"study-set-main\" id=\"keyvalueholder_"+keyValues[i].id+"\">\n" ;
          if(data.canEdit=="true"){
              cStr +=     "            <span  class=\"term-counter\" onclick='javascript:deleteCard("+keyValues[i].id+");'></span>\n" ;

          }  cStr +=  "<div class='bg-revCard'>"+ "<div class='d-flex justify-content-center flex-wrap'>"+" <div class=\"study-set-item\">\n" +
              "<span class=\"input-studyset termDimen\">\n" +
              "                    <textarea "+disabledString+" class=\"study-set-textarea\"  placeholder=\"Term\" id=\"term"+keyValues[i].id+"\" onchange=\"flashCardEdited("+keyValues[i].id+");\">"+keyValues[i].term+"</textarea>\n" +
              "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"term"+keyValues[i].id+"\">\n" +
              "                                <span class=\"input-label-content input-label-content-login\">Term</span>\n" +
              "                            </label>\n" +
              "                        </span>"+
              "            </div>\n" +
              "            <div class=\"study-set-item\">\n" +
              "<span class=\"input-studyset\">\n" +
              "                    <textarea "+disabledString+" class=\"study-set-textarea\" placeholder=\"Definition\" id=\"definition"+keyValues[i].id+"\" onchange=\"flashCardEdited("+keyValues[i].id+");\">"+keyValues[i].definition+"</textarea>\n" +
              "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"definition"+keyValues[i].id+"\">\n" +
              "                                <span class=\"input-label-content input-label-content-login\">Definition</span>\n" +
              "                            </label>\n" +
              "                        </span>"+
              "            </div>" +"</div>"+
              "<div class='user-analytic-data-colored' id='keyHolder_"+keyValues[i].id+"'></div>\n"+
              "</div>" +"</div>";
      }

      if(data.canEdit=="true") {
          cStr += "  <form id=\"study-set-form\">\n" +
              "      <div class=\"study-set-main\" id=\"keyvalueholder\">\n" +
              "        <span class=\"term-counter\"></span>\n" +
              "<div class='d-flex justify-content-center bg-revCard flex-wrap'>"+
              "          <div class=\"study-set-item\">\n" +
              "<span class=\"input-studyset termDimen\">\n" +
              "              <textarea class=\"study-set-textarea\" placeholder=\"Term\" id=\"term\"></textarea>\n" +
              "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"term\">\n" +
              "                                <span class=\"input-label-content input-label-content-login\">Term</span>\n" +
              "                            </label>\n" +
              "                        </span>"+
              "          </div>\n" +
              "          <div class=\"study-set-item\">\n" +
              "<span class=\"input-studyset\">\n" +
              "              <textarea class=\"study-set-textarea\" placeholder=\"Definition\" id=\"definition\"></textarea>\n" +
              "                            <label class=\"input-label input-label-login input-label-login-color-1\" for=\"definition\">\n" +
              "                                <span class=\"input-label-content input-label-content-login\">Definition</span>\n" +
              "                            </label>\n" +
              "                        </span>"+
              "          </div>\n" +
                  "</div>"+
              "      </div>\n" +
              "    </form>\n" +
              "\n" +
              "    <div class=\"col-md-12 add-study-card-btn-wrapper\">\n" +
              "      <a href=\"javascript:addKeyValueCard();\" class=\"add-study-card-btn\">\n" +
              "        <span>+ Add Card</span>\n" +
              "      </a>\n" +
              "    </div>";
      }

      $("#study-set-wrapper").show();
      $("#study-set-from-notes").hide();
      document.getElementById("study-set-wrapper").innerHTML = cStr;
      $('.loading-icon').addClass('hidden');
      $("#content-data-studyset").hide();
      $("#study-set-wrapper-container").show();
      $('.loading-icon').addClass('hidden');
      document.getElementById("revisionTitle").innerHTML=data.resourceName;
      revisionName = data.resourceName;
      getUserAnalyticsForStudySets();
  }

  function closeRevisionScreen(){
      $("#study-set-wrapper-container").hide();
      //$("#content-data-studyset").show();
      $("#content-data-all").show();
  }

  function getUserAnalyticsForStudySets(){
      <g:remoteFunction controller="funlearn" action="getUserAnalyticsForStudySets" onSuccess='displayStudySetAnalytics(data);' params="'resId='+studySetResId"/>
  }

  function displayStudySetAnalytics(data){
      var analyticData = data.keyValueRecordings;
      var totalAttempts=0;
      var rightAttempts=0;
      var wrongAttempts=0;
      var searchString;
      var youTubeSearch;
      var webSearch;
      for(i=0;i<studySetKeyValues.length;i++) {
          totalAttempts=0;
          rightAttempts=0;
          wrongAttempts=0;
          for(j=0;j<analyticData.length;j++){
              if(analyticData[j].keyValueId==studySetKeyValues[i].id){
                  if("true"==analyticData[j].correct) rightAttempts = analyticData[j].count;
                  else if("false"==analyticData[j].correct) wrongAttempts = analyticData[j].count;
              }
          }
          totalAttempts = rightAttempts+wrongAttempts;
          if(totalAttempts>0){
              searchString = studySetKeyValues[i].term+" "+chapterName+" "+chapterSyllabus;
              youTubeSearch = "https://www.youtube.com/results?search_query="+searchString;
              webSearch="https://www.google.com/search?source=hp&ei=oRkBW8S5DonYvgTclaegDQ&q="+searchString;

              if((rightAttempts/totalAttempts)>0.8){
                  //green zone
                  $('#keyvalueholder_'+studySetKeyValues[i].id).addClass('green-top-border');
                  document.getElementById("keyHolder_"+studySetKeyValues[i].id).innerHTML= "<div>"+
                      "<span class='you-are-awesome'>"+"Perfect! "+rightAttempts+" right out of "+totalAttempts+"</span>"+"</div>";
              } else if((rightAttempts/totalAttempts)>0.5){
                  //yellow zone
                  $('#keyvalueholder_'+studySetKeyValues[i].id).addClass('yellow-top-border');
                  document.getElementById("keyHolder_"+studySetKeyValues[i].id).innerHTML="<div>"+
                      "<span class='good-enough'>"+"Missed "+rightAttempts+" out of "+totalAttempts+" times</span>"+"</div>"+

                      "<div class=''>" +
                      "<a href='"+youTubeSearch+"' class='recommend-link' target='_blank'>Recommended Videos</a>"+
                      "</div>"+
                      "<div class=''>" +
                      "<a href='"+webSearch+"' class='recommend-link' target='_blank'>Recommended Links</a>"+
                      "</div>";

              } else {
                  //red zone
                  $('#keyvalueholder_'+studySetKeyValues[i].id).addClass('red-top-border');
                  document.getElementById("keyHolder_"+studySetKeyValues[i].id).innerHTML="<div class='col-md-3'>"+
                      "<span class='prepare-more'>"+"Missed "+rightAttempts+" out of "+totalAttempts+" times</span>" +"</div>"+

                      "<div class=''>" +
                      "<a href='"+youTubeSearch+"' class='recommend-link' target='_blank'>Recommended Videos</a>"+
                      "</div>"+
                      "<div class=''>" +
                      "<a href='"+webSearch+"' class='recommend-link' target='_blank'>Recommended Links</a>"+
                      "</div>";
              }
          }

      }
  }

  function populateRevisionSection(cData) {
    var cStr="";
      <%if(session["userdetails"]!=null){%>
      cStr +="<div class='add-notesPr container'>"+
          "<a href='javascript:createNewStudySet();' class='btn btn-block quiz-practice-btn waves-effect waves-ripple'><i class='material-icons'>add</i><span>Add new</span></a>"+
          "</div>"+"<div>";
      <%}%>
      <%if(session["userdetails"]==null){%>
      cStr +="<div class='add-notesPr container'>"+
          "<a href='javascript:loginOpen();' class='btn btn-block quiz-practice-btn waves-effect waves-ripple'><i class='material-icons'>add</i><span>Add new</span></a>"+
          "</div>"+"<div>";
      <%}%>
      for (var i = 0; i < cData.length; ++i) {
          var showShare = false;
          var data = cData[i];
          //    console.log("sharing="+data.sharing+" instrcutir"+instructor+" and constrolled by "+instructorControlledBook);
          //assumption is the createdbyuser items will come of the items created by the instructor himself and not anybody else
          if (instructor && data.sharing == "createdbyuser") {
              showShare = true;
          }
          if (instructor && data.sharing == null && instructorControlledBook) {
              showShare = true;
          }

              cStr += "<div class='quiz-item-wrapper'>" +
                  "<p class='quiz-number'>"+(i+1)+"</p>";
              if(showShare) {

                  cStr += "<div class=\"dropdown\" style='position: absolute;top:0;right:0;padding: 5px 9px;'> <a class=\"dropdown-toggle\" id=\"android-menu\" data-toggle=\"dropdown\" aria-haspopup=\"true\"" +
                      " aria-expanded=\"true\" style=\"cursor:pointer;\">" +
                      "<i class=\"fas fa-ellipsis-v\"></i></a> <ul class=\"dropdown-menu\" aria-labelledby=\"android-menu\"> <li><a href=\"javascript:showShareOptions("+data.id+",false);\">Share</a></li> " +
                      "</ul> </div>";
              }
              cStr += "<div class='quiz-item'>" +
                  "<p class='quiz-name'>"+data.title+"</p>" +
                  "</div>" +
                  "<div class='quiz-buttons'>";

                  cStr +="<a href='javascript:getStudySets(" + data.id + ")' class='btn btn-block quiz-practice-btn waves-effect waves-ripple'>Revise</a>" ;

              cStr += "</div>" +
                  "</div>";

          cStr +="</div></div>";
      }
      cStr +="</div>";
      $("#content-data-studyset-nosets").hide();
      if(elementExists('content-data-studyset')) {
          document.getElementById('content-data-studyset').innerHTML = cStr;
          $("#content-data-studyset").show();
      }
  }

</script>
