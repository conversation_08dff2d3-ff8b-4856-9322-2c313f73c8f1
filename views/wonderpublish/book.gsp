<!DOCTYPE html>
<%@ page import="com.wonderslate.data.ChaptersMst" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%} else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<g:render template="/funlearn/topicinclude"></g:render>
<asset:javascript src="soundmanager2.js"/>
<asset:stylesheet href="katex.min.css"/>

<script>
    var myResource="${params.resourceId}";
    var myResourceName="${params.resourceName}";
    var flashcardPresent="${params.flashcard}";
    var chapterId="${params.chapterId}";
    var showPubSlide=false;
</script>
<script src="https://apis.google.com/js/api.js"></script>
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
<script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.js"></script>
<link href="https://vjs.zencdn.net/7.0.3/video-js.css" rel="stylesheet">
<style type="text/css">
.app_in_app.hasScrolled .mdl-layout__container {
    position: relative !important;
    z-index: unset;
}
#chapters-toggle i {
    font-size: 16px;
    transform: rotate(90deg);
}
#chapters-toggle.left i {
    transform: rotate(0deg);
}
body.whitelabel_ws
{
padding-top:0 !important;
}

.shine {
    background: #d4d7d9;
    background-image: linear-gradient(to right, #d4d7d9 0%, #e4e9ed 20%, #d4d7d9 40%, #d4d7d9 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    display: inline-block;
    position: relative;

    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-name: placeholderShimmer;
    -webkit-animation-timing-function: linear;
}

box {
    height: 104px;
    width: 100%;
}

div.line-wrapper {
    display: inline-flex;
    flex-direction: column;
    margin-left: 25px;
    margin-top: 15px;
    vertical-align: top;
}

lines {
    height: 10px;
    margin-top: 10px;
    width: 100%;
}

photo {
    display: block!important;
    width: 325px;
    height: 30px;
    margin-top: 15px;
}
.mt-20{
    margin-top: 10px;
}
.modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
.modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}
@-webkit-keyframes placeholderShimmer {
    0% {
        background-position: -468px 0;
    }

    100% {
        background-position: 468px 0;
    }
}
.flexAlign{
    display: flex;
    justify-content: center;
}
.modal-backdrop{
    z-index: 1 !important;
}
</style>

<% if(previewMode) {%>
<style>
.chapter-name a.orangeText {
    pointer-events: none;
    cursor: default;
}
</style>
<%}%>

<% if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])|| "ebouquet".equals(session["entryController"])){ %>
<style>
.bookTemplate .content-wrapper #book-sidebar .read-book-chapters-wrapper {
    padding-left: 15px !important;
    padding-right: 15px !important;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li {
    padding: 8px 2px;
    list-style: none;
}
</style>
<%}%>
<div class="modal" id="myModal">
    <div class="modal-dialog">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Modal Heading</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body">
                Modal body..
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>
<section class="bookTemplate <% if(previewMode) {%>book_preview<%}%>">
    <div id="overlay" class="d-none"></div>
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>

    <div class="row content-wrapper no-gutters">
        <div class="d-flex align-items-center justify-content-between d-sm-none mobChapname">
            <div class="d-flex align-items-center" id="chapters-toggle"> <i class="material-icons">
                arrow_back_ios
            </i>
                <span>Back
                </span>
            </div>
            <p id="mobChapname" style="display: none" class="text-right"></p>
        </div>
        <div class="row no-gutters shadowHeader">
            <div class="col-2 ChapterHeader d-none d-md-flex align-items-center">
                <p class="bookTitle"><%=title%></p>
            </div>
            <div class="col-12 col-md-10">

                <div class="tab-header">
                    <nav class="navbar navbar-expand-sm flex-nowrap navbar-default">
                        <%if(!"true".equals(session["appInApp"])){%>
                        <ul class="menu nav nav-tabs flex-nowrap" id="chapter-details-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#read" id="tabread" onclick="changeActiveTab('read');">Read</a>
                            </li>
                        </ul>
                        <%}%>
                        <div class="d-none justify-content-between prevnextbtn" style="width: 768px;">
                            <button type="button" id="prevBtn" class="btn prev" >Previous</button>
                            <button type="button" id="nextBtn" class="btn next" >Next</button>
                        </div>
                        <ul class="navbar-nav flex-nowrap flex-row contentEdit ml-auto">
                            <% if("evidya".equals(session["entryController"])||"etexts".equals(session['entryController'])){%>
                            <li class="nav-item" id="printepub-sage" style="display: none;">
                                <a class="nav-link" href="javascript:printNotes()"><i class="material-icons">
                                    print
                                </i></a>
                            </li>
                            <li class="nav-item" id="printpdf-sage" style="display: none;">
                                <a class="nav-link" href="javascript:printPdf()"><i class="material-icons">
                                    print
                                </i></a>
                            </li>
                            <%}%>
                            <li class="nav-item" id="notesMenu" >
                                <a class="nav-link" href="javascript:"><i class="material-icons">
                                    description
                                </i></a>
                            </li>
                            <li class="nav-item">
                                <% if("true".equals(session["appInApp"])){%>
                                <a class="nav-link" href="javascript:closeResourceScreen();"><i class="material-icons">
                                    clear
                                </i></a>
                                <%} else {%>
                                <a class="nav-link" href="${referer}"><i class="material-icons">
                                    clear
                                </i></a>
                                <%}%>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        <div class="col-lg-2 chapterSection" style="padding: 0">
            <div class="" style="position: absolute;
            right: 30px;">
                <a class="slide-toggle d-none d-sm-flex" id="chapters-toggle">Chapters<i class="material-icons">
                    double_arrow
                </i></a>
            </div>
            <div id="book-sidebar">
                <div class="side-content">
                    <div class="d-flex align-items-center justify-content-between d-sm-none mobile-title">
                        <a class="backtolibrary" href="${referer}"><i class="material-icons">
                            arrow_back_ios
                        </i>Back</a>
                        <p class="bookTitle text-right"><%=("9".equals(""+session.getAttribute("siteId")))?"CHAPTERS":title%></p>
                    </div>
                    <h2 id="chapter-text">Chapters</h2>
                    <h2 id="expiry-date" style="display: none"></h2>
                    <ol class="read-book-chapters-wrapper" style="padding-left: 30px;padding-right: 20px;">

                        <g:each in="${topicMst}" var="chapter" status="i">
                            <%lockedChapter=true;

                            %>

                            <%  if(!sageOnly){ %>
                            <%if(!previewMode||(previewMode&&(""+topicId).equals(""+chapter.id))||"${fullBookBought}"=="true"){%>

                            <li class="chapter-name" id="chapterName${chapter.id}">
                                <a href="javascript:getChapterDetails('${chapter.id}','${chapter.name.replace("'","\\'")}')" id="chapter${chapter.id}" class="<%=(""+topicId).equals(""+chapter.id)?'orangeText':''%>">${chapter.name}</a>


                                <ul class="chapter-sections" id="sections-dropdown-${chapter.id}"></ul>
                            </li>
                            <%} else {%>
                            <li class="chapter-name">${chapter.name}&nbsp;<i class="material-icons">
                                lock
                            </i></li>
                            <%}%>
                            <%}else{%>
                            <%if(!previewMode||(previewMode&&(""+topicId).equals(""+chapter.id))||"0".equals(""+book.price)||"0.0".equals(""+book.price)|| book.price==null||"null".equals(""+book.price)){%>

                            <li class="chapter-name" id="chapterName${chapter.id}">
                                <a href="javascript:getChapterDetails('${chapter.id}','${chapter.name.replace("'","\\'")}')" id="chapter${chapter.id}" class="<%=(""+topicId).equals(""+chapter.id)?'orangeText':''%>">${chapter.name}</a>


                                <ul class="chapter-sections" id="sections-dropdown-${chapter.id}"></ul>
                            </li>
                            <%} else {%>
                            <li class="chapter-name">${chapter.name}&nbsp;<i class="material-icons">
                                lock
                            </i></li>
                            <%}%>
                            <%}%>
                        </g:each>

                    </ol>

                </div>
            </div>
        </div>
        <div class="offset-lg-2 col-lg-10 read-content">
            <div class="container" id="book-read-material">

                <div class="tab-content">

                    <div role="tabpanel" class="tab-pane fade in show active" id="all">
                        <div class="container">
                            <%if(!"true".equals(session["appInApp"])){%>
                            <div id="allAddButton" class="justify-content-end" style="display: flex;">

                                <div class="dropdown">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                        <i class="material-icons">
                                            add
                                        </i>  <span>Add</span>
                                    </button>
                                    <sec:ifLoggedIn>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:openVideos();"><span>Add new videos (Direct)</span></a>
                                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Add related videos (Recommended)</span></a>
                                            <a class="dropdown-item" href="javascript:openSolution();"><span>Add related Ref.link (Recommended)</span></a>
                                            <a class="dropdown-item nght" href="javascript:createNewSet();"><span>Add revision</span></a>
                                        </div>
                                    </sec:ifLoggedIn>
                                    <sec:ifNotLoggedIn>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add new videos (Direct)</span></a>
                                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related videos (Recommended)</span></a>
                                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related Ref.link (Recommended)</span></a>
                                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add revision</span></a>
                                        </div>
                                    </sec:ifNotLoggedIn>
                                </div>

                            </div>
                            <%}%>
                            <%if(downloadBookChapters){%>
                          <sec:ifLoggedIn>
                            <div class="download-btn d-flex justify-content-end" style="position: fixed; right: 20px;z-index: 990">
                                <button type="button" class="btn btn-primary  d-flex align-items-center" id="downloadChapter" onclick="javascript:downloadChapter(${topicId})">
                                    <i class="material-icons">file_download</i>  <span style="font-weight: 600;">Download</span>
                                </button>
                            </div>
                         </sec:ifLoggedIn>
                            <%}%>
                            <div id="content-data-all" class="col-md-12 col-xs-12 col-sm-12"></div>
                            <div id="htmlreadingcontent" class="row quiz-section" style="margin-top: 40px"></div>

                        </div>
                    </div>
                </div>


            </div>


            <% if(g.cookie(name: 'siteName')!=null && g.cookie(name: 'siteName') !="sage") { %>
            <div class="export-notes d-none">
                <div class="position-relative">
                    <button class="close-notes"><i class="material-icons">close</i></button>
                </div>
                <div class="content">
                    <div class="notes-creation-header d-flex justify-content-around align-items-center">
                        <p class="notes-creation-header-title">Notes and Highlights
                        </p>
                        <a class="btn export-study-set btn-book-buy" href="javascript:displaySelectionCheckbox();" id="ExportToStudySet">Export to Revision</a>
                    </div>
                    <ul class="notes-list-wrapper" id="user-notes">
                        <li class='notes-list-item'></li>
                    </ul>
                </div>
            </div>
            <%}%>

            <div class="price-wrapper" style="padding-bottom: 0;display: block;">
                <% if(!"sage".equals(session["entryController"])){%>
                <div class="section-btns d-flex justify-content-between">
                    <div>
                        <a href="#" id="prev-section" class="prev-sec px-3 py-2" name="prev">
                            <i class="material-icons prev_chap">
                                arrow_right_alt
                            </i> Previous Topic</a>
                    </div>
                    <div>
                        <a href="#" id="next-section" class="next-sec px-3 py-2" name="next">Next Topic
                            <i class="material-icons next_chap">
                                arrow_right_alt
                            </i> </a>
                    </div>
                </div>
                <%}%>
                <%if(previewMode){%>
                <div class="preview-book-btns">
                    <div class="card-wrapper">
                        <% if(!("0".equals(""+book.price)||"0.0".equals(""+book.price))) {%>
                        <div class="rprice-tag">


                        </div>
                        <div class="buy">
                          <% if ("true".equals(session['prepjoySite'])){%>
                            <a class="btn btn-block btn-book-buy r-buy-butn waves-effect" href="/wonderpublish/bookdtl?bookId=${book.id}&mode=buy&buyType=book">Buy Now</a>
                          <%}else if("true".equals(session["appInApp"])){%>
                            <a class="btn btn-block btn-book-buy r-buy-butn waves-effect" href="/${book.title.replaceAll(" ","-")}/ebook-details?bookId=${book.id}">Buy Now</a>
                          <%}else{%>
                            <a class="btn btn-block btn-book-buy r-buy-butn waves-effect" href="/${book.title.replaceAll(" ","-")}/ebook-details?siteName=${session['siteName']}&bookId=${book.id}">Buy Now</a>

                            <%}%>
                        </div>
                    </div>
                    <% } else {%>
                    <div class="rprice-tag">
                        <div class="complte-book">
                            <h4>COMPLETE BOOK</h4>
                            <p> Free</p>
                        </div>
                    </div>
                    <div>
                        <a class="btn btn-block btn-book-buy r-buy-butn waves-effect" href="javascript:addToMyLibrary(${book.id},${book.price},'${book.title}');">Add to Library</a></div>
                    <%}%>
                </div>
                <%}

                %>
            </div>
        </div>

    </div>

</section>

<!-- Modal for  Downloading -->
<div class="modal modal-modifier fade" id="downloadModal" >
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-left p-5" id="downloadModalBody">
                <div class="downloadWrapper d-flex justify-content-center flex-column align-items-center">
                    <lottie-player src="https://assets8.lottiefiles.com/packages/lf20_szdrhwiq.json"  background="transparent"  speed="1" style="width: 250px" loop  autoplay></lottie-player>
                    <lottie-player src="https://assets8.lottiefiles.com/packages/lf20_9mkgi4qs.json"  background="transparent"  speed="1" style="width: 200px" loop  autoplay></lottie-player>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal for  Download complete -->
<div class="modal modal-modifier fade" id="downloadComplete" >
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close mr-3" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>
            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-left p-5" id="downloadCompleteBody">
                <div class="downloadWrapper d-flex justify-content-center flex-column align-items-center">
                    <img src="${assetPath(src: 'evidya/an.gif')}" style="width: 200px">
                </div>
                <div class="text-center">
                    <h5 class="text-primary">Downloaded successfully!</h5>
                    <button class="btn btn-primary btn-primary-modifier text-center" id="successOk" style="font-weight: bold">OK</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="networkCheckModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered text-center">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close" id="videoClose"> <span aria-hidden="true">&times;</span></button>
                </div>
                <div>
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_6s2xGI.json"  background="transparent"  speed="1"  style="height: 300px;position: relative !important;"  loop  autoplay></lottie-player>
                    <h5 class="text-danger">No Internet Connection.</h5>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>


%{--<g:render template="/${session['entryController']}/footer_new"></g:render>--}%

<%if(previewMode){%>
<g:render template="/wonderpublish/buyOrAdd"></g:render>
<%}%>


<script>
    var bookLang = "${book.language}" ;
    var bookContentCreatedBy = "${book.createdBy}";

    <%if(book!=null&&book.tags!=null){%>
    bookTags="${book.tags}";
    <%}%>
    var loggedInUser = false;

    <%if("true".equals(session["appInApp"])){%>
    $('body').addClass('videoTransformAppInApp');
    <%}%>

</script>

<sec:ifLoggedIn>
    <script>
        loggedInUser = true;
    </script>
</sec:ifLoggedIn>

<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<asset:javascript src="katex.min.js"/>
<asset:javascript src="auto-render.min.js"/>
<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

<g:render template="/wonderpublish/allSection"></g:render>
<g:render template="/wonderpublish/readingSection"></g:render>
<g:render template="/wonderpublish/notesHighlightSection"></g:render>
<g:render template="/wonderpublish/videoSection"></g:render>
<g:render template="/wonderpublish/videoPlay"></g:render>
<g:render template="/wonderpublish/weblinksSection"></g:render>
<g:render template="/wonderpublish/revision"></g:render>
<g:render template="/wonderpublish/qaSection"></g:render>
<g:render template="/wonderpublish/readerView"></g:render>
<script>
    var urlBookId = "${book.id}";
    var receivedResId="${resId}";
    var instituteLibrary="${instituteLibrary}";
    var previousChapterId=${topicId};
    var urlCheck ="${params.checkurl}";
    lastReadTopicId = ${lastReadTopicId};
    var sageOnly="${sageOnly}";
    var chapterIdForPDF;
    var online=true;
    var chapterResponse;
    var downloadChapterAccess=false;
    $('#zoom-section').hide();

    var getChapterID = ${topicId};
    var tempTopicId = JSON.parse(localStorage.getItem('lastReadPDF'));
    if (tempTopicId!=null){
        if((tempTopicId.pdfOpen=='true' || tempTopicId.pdfOpen==true) && tempTopicId.bookId==${params.bookId}){
            getChapterID = tempTopicId.chapterId;
        }
    }

    <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+getChapterID+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>


    $("#chapterName"+previousChapterId).addClass('orangeText');


    function getChapterDetails(chapterId,chapterName){
        $('.prevnextbtn').addClass('d-none').removeClass('d-flex');
        $('.read-book-chapters-wrapper li').removeClass('orangeText');
        if(!(siteId==12||siteId==23||siteId==24)) {
            backToMain();
        }

        $("#htmlreadingcontent").hide();

        $("#chapterName"+previousChapterId).removeClass('orangeText');
        $('.loading-icon').removeClass('hidden');

        if(elementExists('sections-dropdown-'+previousChapterId)){
            document.getElementById('sections-dropdown-'+previousChapterId).innerHTML="";
        }
        if(elementExists('content-data-studyset')){
            document.getElementById('content-data-studyset').innerHTML="";
            $("#content-data-studyset-nosets").show();
            $("#study-set-wrapper-container").hide();
        }
        if(elementExists('content-data-userNotes')){
            document.getElementById('content-data-userNotes').innerHTML="";
        }
        if(elementExists('content-data-no-notes')){
            $("#content-data-no-notes").show();
        }


        if(elementExists('addNotes')){
            $('#addNotes').hide();

        }
        //   $('#chapter-details-tabs a[href="#read"]').tab('show');
        previousChapterId=chapterId;
        $("#chapterName"+previousChapterId).addClass('orangeText');
        <g:remoteFunction controller="funlearn" action="chapterDetails" params="'topicId='+chapterId+'&mode=chapter&fromWeb=true'" onSuccess = "chapterDetailsRecieved(data);"/>

        if($(window).width() == 768){
            showHideChaptersTab();
        }

        var mobChapname=chapterName;
        document.getElementById('mobChapname').innerHTML=mobChapname;
        if($(window).width() < 767){
            $('header').hide();
            $('#chapters-toggle').toggleClass('left');
            $("#book-sidebar").toggleClass('d-none');
            if ($('.read-content').hasClass('offset-md-4')) {
                $('.read-content').removeClass('offset-md-4').addClass('col-md-12');
                $('.read-content').removeClass('col-md-8');
            } else {
                $('.read-content').removeClass('col-md-12').addClass('offset-md-4');
                $('.read-content').addClass('col-md-8');
            }
        }
        scrollTo(0,64);

    }



</script>
<asset:javascript src="zoom.js"/>


<script>
    $(document).ready(function(){
        if($(window).width() >= 768) {
            $("#chapters-toggle,.slide-toggle").click(function () {

                $('#chapters-toggle,.slide-toggle').toggleClass('left');
                $("#book-sidebar").toggleClass('d-none');
                if ($('.read-content').hasClass('offset-lg-2')) {
                    $('.read-content').removeClass('offset-lg-2').addClass('col-lg-12');
                } else {
                    $('.read-content').removeClass('col-lg-12').addClass('offset-lg-2');
                }
            });
        }
        else{
            $(window).on('load',function () {

                if(($('.read-content').hasClass('offset-lg-2'))&&($('.read-content').hasClass('col-lg-10'))){
                    $('.read-content').removeClass('offset-lg-2').addClass('offset-md-4');
                    $('.read-content').removeClass('col-lg-10').addClass('col-md-8');
                }

            });
            $("#chapters-toggle").click(function () {
                if($(window).width()<767) {
                    $('.contentEdit').width('0');
                }
                $('.shadowHeader').height('0');
                $(".price-wrapper").show();
                $('.prevnextbtn').addClass('d-none').removeClass('d-flex');
                $('#overlay,.export-notes').addClass('d-none');
                $('header').show();
                $('#chapters-toggle').toggleClass('left');
                $("#book-sidebar").toggleClass('d-none');
                if ($('.read-content').hasClass('offset-md-4')) {
                    $('.read-content').removeClass('offset-md-4').addClass('col-md-12');
                    $('.read-content').removeClass('col-md-8');
                } else {
                    $('.read-content').removeClass('col-md-12').addClass('offset-md-4');
                    $('.read-content').addClass('col-md-8');
                }
            });
        }
        $('#notesMenu').on('click',function () {
            $('#overlay').removeClass('d-none');
            $('.export-notes').removeClass('d-none');
            $('#notesMenu').addClass('active');

        });
        $('.close-notes').on('click',function () {
            $('#overlay').addClass('d-none');
            $('.export-notes').addClass('d-none');
            $('#notesMenu').removeClass('active');
        });

    });


</script>




<script>
    document.onkeydown = function(e) {
        if(event.keyCode == 123) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'I'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'J'.charCodeAt(0)){
            return false;
        }
        if(e.ctrlKey && e.keyCode == 'U'.charCodeAt(0)){
            return false;
        }
    }

    var $document = $(document),
        $element = $('body'),
        className = 'hasScrolled';

    $document.scroll(function() {
        if ($document.scrollTop() >= 64) {
            // user scrolled 50 pixels or more;
            // do stuff
            $element.addClass(className);
        } else {
            $element.removeClass(className);
        }
    });

    function closeAddLink(){
        $('#addWeburl').modal('hide');
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }
    function closeAddVideo(){
        $('#addVideo').modal('hide');
        if(allTabMode){
            $('#chapter-details-tabs a[href="#all"]').tab('show');
        }
    }
    function openSolution() {
        $('#addWeburl').modal('show');
    }

    if(flashcardPresent){
        reviseNow(myResource,myResourceName);
        showPubSlide=true;
    }
    function printNotes() {
        window.print();
    }

    function printPdf(){
        //var ab=document.getElementById('pdf-books');

        var PDF = document.getElementById("pdf-books");
        PDF.focus();
        PDF.contentWindow.print();
    }

    window.onbeforeprint = function(event) {
        if(!($('#chapters-toggle').hasClass('left'))) {
            $('#chapters-toggle').addClass('left');
        }
        else{
            $('#chapters-toggle').removeClass('left');
        }
            $('.bookTitle').hide();
            $('#book-sidebar').addClass('d-none');
            $('.read-content').removeClass('offset-lg-2').addClass('col-lg-12');
     }
     window.onafterprint = function(event) {
         $('#chapters-toggle.slide-toggle').removeClass('left');
         $('.bookTitle').show();
         $('#book-sidebar').removeClass('d-none');
         $('.read-content').addClass('offset-lg-2').removeClass('col-lg-12');

     }

    if (loggedInUser) {
        // Book usage duration
        var startTime, duration;
        var currentDate = new Date();
        var logDate = currentDate.getDate() + "/" + (currentDate.getMonth() + 1) + "/" + currentDate.getFullYear();

        window.onload = function () {
            startTime = new Date().getTime();
        }

        window.onbeforeunload = function () {
            duration = new Date().getTime() - startTime;
            <g:remoteFunction controller="usermanagement" action="updateUserBookDurationLog" params="'logDate='+logDate+'&logDuration='+duration+'&bookId='+urlBookId" />
        }

        document.addEventListener('visibilitychange', function () {
            var state = document["visibilityState"];
            if (state == "visible") {
                startTime = new Date().getTime();
            } else if (state == "hidden") {
                duration = new Date().getTime() - startTime;
                <g:remoteFunction controller="usermanagement" action="updateUserBookDurationLog" params="'logDate='+logDate+'&logDuration='+duration+'&bookId='+urlBookId" />
            }
        });
    }
    window.addEventListener('online',function (){
        online = true;
        $('#networkCheckModal').modal('hide');

    });
    window.addEventListener('offline', function (){
        online = false;
        $('#networkCheckModal').modal('show');

    });
    <%if(downloadBookChapters){%>
        <sec:ifLoggedIn>
            downloadChapterAccess = true;
        </sec:ifLoggedIn>
    <%}%>
</script>



</body>
</html>
