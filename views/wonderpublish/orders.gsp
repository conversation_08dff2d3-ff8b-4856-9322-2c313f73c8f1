<g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>
  body.blackspine {
    background-color: #F8F8F8;
  }

</style>

<g:render template="/funlearn/topicinclude"></g:render>
<asset:stylesheet href="font-awesome.css"/>

<asset:stylesheet href="wonderslate/ws_order.css" async="true"/>
<%if("true".equals(session['prepjoySite'])){%>
<style>
.purchase-details-container{
  position: relative;
  z-index: 99;
}
.purchased-book-item{
  background: transparent !important;
  border: 1px solid #fff;
}
</style>
<%}%>

<lottie-player src="https://assets3.lottiefiles.com/packages/lf20_OiqxPlq97j.json" id="celebrationAnim" style="z-index: 0 !important;"  background="transparent"  speed="1" autoplay></lottie-player>


<div class="container purchase-details-container ws-orders">
  <div class="row">
    <div class="purchase-heading col-12 mb-3">
      <h3>Purchase successful</h3>
    </div>
    <div class="col-md-12 col-xs-12 col-sm-12 purchase-details-wrapper d-lg-flex">
      <div class="browse-purchase-book col-12 col-lg-6 col-md-6 pl-0">
        <div class="browse-wrapper">

          <p class="purchase-success-confirmation">Your purchase of the following Book(s) is successful.</p>
          <%if(hasEbook){%>
          <p class="purchase-success-confirmation d-sm-none">This Book(s) has been added to your
            <strong>My Books.</strong>
          </p>
          <%}else if(hasRecharge){%>
            <p class="purchase-success-confirmation d-sm-none">Your recharge is successful.</p>
          <%}%>
          <%if(hasPrintBook){%>
          <p class="purchase-success-id mt-3">You can track your order at <a href="/usermanagement/orders">Order History</a></p>
          <%}else{%>
          <p class="purchase-success-id mt-3">For more details, go to <strong>Order History.</strong></p>
          <%}%>
          <div class="purchased-book-container">
            <div class="purchased-book-wrapper">

              <% if(purchasedBooks!=null){
                for(int i=0;i<purchasedBooks.size();i++){
              %>
              <div class="purchased-book-item shadow-sm">
                <div class="purchased-book-img-wrapper shadow-sm">
                  <%
                    String bookCoverImage=""
                  if(purchasedBooks[i].coverImage !=null && purchasedBooks[i].coverImage.startsWith("https")){
                    bookCoverImage = purchasedBooks[i].coverImage.replace("~", ":")
                  }else {
                    bookCoverImage = "/funlearn/showProfileImage?id="+purchasedBooks[i].bookId+"&fileName="+purchasedBooks[i].coverImage+"&type=books&imgType=webp"
                  }
                  %>
                  <img src="${bookCoverImage}" alt="">
                </div>
                <div class="purchased-book-info book-info col">
                  <p class="purchased-book-name">${purchasedBooks[i].title}</p>
                  <%if(!"recharge".equals(purchasedBooks[i].bookType)){%>
                  <p class="detail-book-author-name">${purchasedBooks[i].publisherName}</p>
                  <%}%>
                </div>

              </div>
              <%}
              } else {%>
              <div class="purchased-book-item shadow-sm">
                <div class="purchased-book-img-wrapper shadow-sm">
                  <img src="/funlearn/showProfileImage?id=${bookId}&fileName=${coverImage}&type=books&imgType=webp" alt="">
                </div>
                <div class="purchased-book-info book-info col">
                  <p class="purchased-book-name">${title}</p>
                  <p class="detail-book-author-name">${publisherName}</p>
                </div>

              </div>
              <%}%>

            </div>
          </div>

        </div>
      </div>

      <div class="purchase-details col-12 col-lg-6 col-md-6">
        <%if(hasEbook){%>
        <p class="purchase-success-confirmation d-none d-sm-block">This Book(s) has been added to your
          <strong>My Books.</strong>
        </p>

        <div class="instructions mt-4">
          <h5 class="mb-3"><u>Instructions:</u></h5>
          <ol class="pl-3 mb-0">
            <li>All your Books will be available in
              <strong>My Books.</strong>
            </li>
            <li>Your login id is <strong id="purchasedUserId">Fetching..</strong>.</li>
            <li>Your payment id is <strong id="purchasedPaymentId">Fetching..</strong>.</li>
          </ol>
        </div>

        <div class="pt-3">
          <% if("true".equals(session["commonWhiteLabel"])){%>
          <a href="/wsLibrary/myLibrary?mode=mybooks&siteName=${session['siteName']}" class="signup-btn learn-btn waves-effect waves-ripple">
          <%}else{%>
          <a href="/wsLibrary/myLibrary?mode=mybooks&siteName=${session['entryController']}" class="signup-btn learn-btn waves-effect waves-ripple">
          <%}%>
            Go to My Books
          </a>
        </div>
        <%}else if(hasRecharge){%>
        <p class="purchase-success-confirmation d-none d-sm-block">Your recharge is successful.</p>


        <div class="pt-3">
          <% if("true".equals(session["commonWhiteLabel"])){%>
          <a href="/wsLibrary/myLibrary?mode=mybooks&siteName=${session['siteName']}" class="signup-btn learn-btn waves-effect waves-ripple">
            <%}else{%>
            <a href="/wsLibrary/myLibrary?mode=mybooks&siteName=${session['entryController']}" class="signup-btn learn-btn waves-effect waves-ripple">
              <%}%>
              Go to My Books
            </a>
        </div>
       <% }else{%>
        <div class="instructions mt-4">
          <h5 class="mb-3"><u>Information:</u></h5>
          <ol class="pl-3 mb-0">
            <li>Your login id is <strong id="purchasedUserId">Fetching..</strong>.</li>
            <li>Your payment id is <strong id="purchasedPaymentId">Fetching..</strong>.</li>
          </ol>
        </div>
        <%}%>
      </div>
    </div>
  </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
<script>
    var pageType='book';
    var userLoginId = "${session['userdetails'].username}"?"${session['userdetails'].username}".split("${session["siteId"]}"+'_')[1]:"";
    var prepjoyAppUrl;
    var openBook = document.querySelector('#openBook');
    var playStoreLink;
    var appStoreLik;
    var userPurchasedPaymentId = "${params.paymentId}";
    var prepjoySite = "${session['prepjoySite']}";
      document.getElementById("purchasedUserId").innerHTML = userLoginId;
      document.getElementById("purchasedPaymentId").innerHTML = userPurchasedPaymentId;

    //localStorage.setItem('purchasedBookId',${bookId});

</script>

<script>
  var siteName = "${session['siteName']}";
  links(siteName)

  function links(siteName){
    if (siteName.includes('currentaffairs')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy';
      appStoreLik = 'https://apps.apple.com/in/app/prepjoy-current-affairs/id1595285082';
    }

    if (siteName.includes('karnataka')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
      appStoreLik = 'https://apps.apple.com/us/app/id1611175104';
    }

    if (siteName.includes('neet')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.neet&hl=en-GB'
      appStoreLik = 'https://apps.apple.com/in/app/prepjoy-neet/id1613665117';
    }

    if (siteName.includes('enggentrances')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.engineering&hl=en-GB';
      appStoreLik = 'https://apps.apple.com/in/app/prepjoy-engg-entrance-exams/id1615699605';
    }
    if (siteName.includes('cacscma')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.ca';
      appStoreLik = 'https://apps.apple.com/in/app/prepjoy-ca-cs-cma/id1616647213';
    }
    if (siteName.includes('ctet')){
      playStoreLink = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.ctet&hl=en-GB';
      appStoreLik = 'https://apps.apple.com/us/app/prepjoy-teaching-exams/id1620819618';
    }
  }

  $('#playstr').attr('href',playStoreLink);
  $('#appstr').attr('href',appStoreLik);

  window.onload = function() {
    document.getElementById('celebrationAnim').style.display='block';
  }
</script>
</body>
</html>
