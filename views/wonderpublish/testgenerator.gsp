<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


  <asset:stylesheet href="wonderslate/testGenerator.css"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<div class="container preview-book-container" style='min-height: auto;'>
  <div class="row hidden-xs">

  </div>
</div>



<section class="test_generator">

<div id="beforeGenerateTest" class="container main mx-auto" style="max-width: 960px;">

  <div class="row page_title d-flex py-4 mx-0 align-items-center">
    <i class="material-icons-round back-arrow" onclick="javascript:window.history.back();">keyboard_backspace</i>
    <h3 class="d-block"><strong>Online Test Creator</strong></h3>
  </div>

  <div id="count-div"></div>
  <%if(instructor){%>
  <div id="testInputDev">

    <g:form id="testForm" role="form">
      <!-- Select Batch -->
      <%if(institutes!=null){%>
        <div class="form-group col-sm-3">
            <label for="batches">Select Batch <span class="text-danger">*</span></label>
            <select class="form-control" id="batches" name="batches" required onchange="getSubjects()">
              <option value="">Select Class / Division</option>
              <%  String className="";
                  institutes.each{
                 if(it.name == "Default") className = "All "
                 else className = it.name;
              %>
              <option value="${it.batchId}">${className}</option>
              <%}%>

          </select>
        </div>
      <div class="form-group col-sm-3">
        <label for="batches">Select Subject <span class="text-danger">*</span></label>
        <select class="form-control" id="subject" name="subject" required">

        </select>
      </div>
      <%}%>
      <!-- Test Name -->
      <div class="form-group col-sm-5" >
        <label for="testName">Test Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control" id="testName" name="testName" required />
      </div>

      <!-- Start Date/Time -->
      <div class="form-group col-sm-4">
        <label for="startDateTime">Start Date/Time <span class="text-danger">*</span></label>
        <input type='text' class="form-control" id='startDateTime' name="startDateTime" required />
      </div>
      <div class="form-group col-sm-4">
        <label for="endDateTime">End Date/Time <span class="text-danger">*</span></label>
        <input type='text' class="form-control" id='endDateTime' name="endDateTime" required />
      </div>
      <div class="form-group col-sm-4">
        <label for="resultDateTime">Result Date/Time </label>
        <input type='text' class="form-control" id='resultDateTime' name="resultDateTime"  />
      </div>
      <!-- Duration in minutes -->
      <div class="form-group col-sm-4">
        <label for="duration">Duration (minutes) <span class="text-danger">*</span></label>
        <input type="number" class="form-control" id="duration" name="duration"
               min="5" step="5" required />
      </div>

      <!-- Hidden field to store browser timezone -->
      <input type="hidden" id="timeZone" name="timeZone" />



    </g:form>
  </div>
  <%}%>
  <div id="divi2"></div>
  <div class='test-gen-chapters row on-top-overlay' id='divi3'></div>
  <div class="container test-type-container">
    <div class='test-gen-chapters row on-top-overlay' id='divi4' style='display: none'>
      <div class='col-md-12'>
        <p class='test-type'>Select test type1</p>
        <table class='table chapter-selection-table table-responsive table-bordered'>
          <tbody>
          <tr class='chapter-selection'>
            <td class='chapter-name'>
              <label class='not-active'>Fill in the blanks
                <input type='radio' name='testSelection'>
                <span class='checkmark checkmark-radio'></span>
              </label>
            </td>
          </tr>
          <tr class='chapter-selection'>
            <td class='chapter-name'>
              <label class='not-active'>Multiple Choice Questions
                <input type='radio' name='testSelection'>
                <span class='checkmark checkmark-radio'></span>
              </label>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class='test-gen-chapters row on-top-overlay' id='divi5' style='display: none'>
      <div class='col-md-12'>
        <p class='test-type'>Select difficult level</p>
        <table class='table chapter-selection-table table-responsive table-bordered'>
          <tbody>
          <tr class='chapter-selection'>
            <td class='chapter-name'>
              <label class='not-active'>Easy
                <input type='radio' name='testTypeSelection' disabled>
                <span class='checkmark checkmark-radio'></span>
              </label>
            </td>
          </tr>
          <tr class='chapter-selection'>
            <td class='chapter-name'>
              <label class='not-active'>Medium
                <input type='radio' name='testTypeSelection' disabled>
                <span class='checkmark checkmark-radio'></span>
              </label>
            </td>
          </tr>
          <tr class='chapter-selection'>
            <td class='chapter-name'>
              <label class='not-active'>Hard
                <input type='radio' name='testTypeSelection' disabled>
                <span class='checkmark checkmark-radio'></span>
              </label>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class='test-gen-chapters row on-top-overlay' id='divi6' style='display: none'>
      <div class='col-md-12'>
        <p class='test-type' id="noofquestionslabel">Number of questions </p>
        <input type='text' class='form-control' name='noOfQuestions' id='noOfQuestions' placeholder='Enter Number (200 maximum)' disabled size="4">
      </div>
    </div>
  </div>
  <div class="container d-flex justify-content-center row ml-0">
    <div class="testgen-next-pre">
      <a href="javascript:prev()" class="btn previous-btn pull-left testgen-previous mr-md-4 mb-md-4 mb-lg-0" style="display: none">
        <i class="material-icons-round" aria-hidden="true" style="bottom: 1px;position: relative;">chevron_left</i> Previous
      </a>
      <button type='button' class='btn next-btn-testgen ml-auto mb-0' id="next-button" onclick="javascript:next()">Next</button>
    </div>
  </div>
  <div id="error_message" class="d-flex justify-content-center align-items-center mx-auto mt-2">
  </div>
</div>

<div id="afterGenerateTest" class="container my-4" style="display: none">
  <div id="quizModal">
    <div class="row page_title d-flex py-4 mx-0 align-items-center">
      <a href="/test-generator" class="d-flex justify-content-center align-items-center" style="text-decoration: none;">
        <i class="material-icons back-arrow">keyboard_backspace</i>
      </a>
      <h3 class="d-block"><strong>Created Test</strong></h3>
    </div>

    <div class="practice-questions mb-5">
      <div id="mcqquestionsection">

        <div class="quiz-modal-body">

          <div id="quizdescription" style="display: none"></div>
          <div style="display: flex;">
            <div class="questionumber-containter hidden-xs" id="questionNumberContainer" >
              %{-- <div class="modal-footer quiz-modal-footer row-centered" id="footer-containter"></div> --}%
              <div id="questionumber-containter" style="position: static;width:250px;"></div>
            </div>
            <div class="mcq-question-div" id="individualQuestionContainer">
              <div class="question-section-main">
                <div id="passage" style="display: none; margin-bottom: 15px;"></div>
                <div id="directions" style="display: none; margin-bottom: 15px;"></div>
                %{-- <p class="question-title">Question:</p> --}%
                <div class="question practice-question" id="question"></div>
                %{-- <p class="question-title" id="question-option" style="margin-top: 25px;">Options:</p> --}%
                <div class=" mcqquestion" id="mcqquestion" style="margin-top:0px;margin-bottom: 0px;"></div>
              </div>
              <div class="mcq-question-div mcq-learn">
                <div class="row" id="answerpassage" style="display: none"></div>
                <div class="row" id="quizanswerdescription" style="display: none"></div>
                <div id="answer-holder" class="answer-holder"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="quiz-modal-footer row-centered mt-4 flex-wrap" id="quiz-modal-footer">
          <div class="col-md-3 done-btn-div col-xs-12 col-sm-4 mt-3 text-center">
            <a href="javascript:done();" id="quiz-Done" class="btn submit-quiz-btn">Submit Exercise</a>
          </div>
          <div class="d-flex justify-content-around justify-content-md-between col-md-9 col-xs-12 col-sm-8 mt-3">
            <a href="javascript:prevQ();" id="prevButton" class="d-flex btn previous-btn pull-left disabled align-items-center">
              <i class="material-icons-round" aria-hidden="true">chevron_left</i> Previous
            </a>
            <a href="javascript:nextQ();" class="btn next-btn disabled">Next</a>
          </div>
        </div>

      </div>

      <div id="score-container-div">
        <div class="score-container" id="score-container"></div>

      </div>

      <div class='quiz-modal-footer align-items-center justify-content-center mt-4' id="review-footer" style="display: none;">
        <div class='pr-2'>
          <a href='javascript:showPerformanceDetails();' class='btn btn-review pull-left'>Review</a>
        </div>
        <div class='pl-2'>
          <a href='#' id="close-quizz-modal" class='btn next-btn pull-right close-modal text-white'>Close</a>
        </div>
      </div>

    </div>

  </div>

</div>

</section>

<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>





<g:render template="/${session['entryController']}/footer_new"></g:render>
<g:render template="/funlearn/topicscripts"></g:render>




</script>

<script>
  <%if(!instructor){%>
  $(document).ready(function (){
    startTestGenerator('multiplebooks');
  });
    <%}%>
</script>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script>
  // Initialize datepickers on page load
  $(function() {
    // Capture browser timezone in the hidden field . It should store the timezone in the three letter format, like IST, UTC, etc.

      var timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;


    $('*[name=startDateTime]').appendDtpicker({
      "futureOnly": true,
      "autodateOnStart": false,
      "dateFormat": 'DD-MM-YYYY h:mm',
      "minuteInterval": 5
    });
    $('*[name=endDateTime]').appendDtpicker({
      "futureOnly": true,
      "autodateOnStart": false,
      "dateFormat": 'DD-MM-YYYY h:mm',
      "minuteInterval": 5
    });
    $('*[name=resultDateTime]').appendDtpicker({
      "futureOnly": true,
      "autodateOnStart": false,
      "dateFormat": 'DD-MM-YYYY h:mm',
      "minuteInterval": 5
    });

  });

  var testName = $('#testName').val();

  // Extract date/time from the datetimepickers
  var startDateTime = $('#startDateTime').val();
  var endDateTime   = $('#endDateTime').val();
  var resultDateTime= $('#resultDateTime').val();

  var batchId = $('#batches').val();

  var duration = $('#duration').val();
  var subject = $('#subject').val();
  var timeZone = null;

  // Collect form data on click
  function captureFormValues() {
    testName = $('#testName').val();
    subject = $('#subject').val();

    // Extract date/time from the datetimepickers
     startDateTime = $('#startDateTime').val();
     endDateTime   = $('#endDateTime').val();
     resultDateTime= $('#resultDateTime').val();
    batchId = $('#batches').val();
     duration = $('#duration').val();
     timeZone = $('#timeZone').val();
     if(batchId == ""){
      alert("Please select a batch.");
      document.getElementById("batches").focus();
        return;
     }
    //check for the values of start date, name,end date and duration. They are mandatory fields
    if (testName == "" || startDateTime == "" || endDateTime == "" || duration == ""|| subject == "") {
      alert("Please fill in all the mandatory fields.");
      return;
    }
    //print the start date, end date and result date


    //enddate should be greater than start date and result date should be greater than end date
    if (new Date(startDateTime) >  new Date(endDateTime)) {
      alert("End Date/Time should be greater than Start Date/Time.");
      return;
    }
    if (resultDateTime != "" && new Date(endDateTime) > new Date(resultDateTime)) {
      alert("Result Date/Time should be greater than End Date/Time.");
      return;
    }
    //duration should be less than or equal to difference between end date and start date
    var diff = new Date(endDateTime) - new Date(startDateTime);
    var diffInMinutes = diff / 60000;
    if (duration > diffInMinutes) {
      alert("Duration should be less than or equal to the difference between End Date/Time and Start Date/Time.");
      return;
    }


    // TODO: Implement your submission or AJAX call here
    testBatchId = batchId;
    //check the conflict of the test with the existing tests
    //start the loader
    $('.loading-icon').removeClass('hidden');
    $.ajax({
      url: '/onlineTest/checkTestTimeConflict',
      type: 'POST',
      data: {
        batchId: batchId,
        startDateTime: startDateTime,
        endDateTime: endDateTime
      },
      success: function(response) {
        //stop the loader
        $('.loading-icon').addClass('hidden');
        if (response.status == "Conflict") {
          alert("Test conflicts with existing test. Please select a different date/time.");
          return;
        } else {
          startTestGenerator('multiplebooks');
        }
      }
    });

  }

  function getSubjects(){
    var batchId = $('#batches').val();
    if(batchId == ""){
      alert("Please select a batch.");
      document.getElementById("batches").focus();
        return;
     }
    //start loader
    $('.loading-icon').removeClass('hidden');
    $.ajax({
      url: '/onlineTest/getSubjectsForClass',
      type: 'POST',
      data: {
        batchId: batchId
      },
      success: function(response) {
        //stop loader
          $('.loading-icon').addClass('hidden');
         //parse the response and populate the subject dropdown
        var subjectDropdown = $('#subject');
        subjectDropdown.empty();
        subjectDropdown.append('<option value="">Select Subject</option>');
        response.forEach(function(subject) {
          subjectDropdown.append('<option value="' + subject.subject + '">' + subject.subject + '</option>');
        });
      }
    });
  }
</script>
<g:render template="/wonderpublish/testgeneratorscripts"></g:render>
