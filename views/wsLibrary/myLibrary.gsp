<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<sec:ifNotLoggedIn>
    <script>
        loggedIn=false;
    </script>
</sec:ifNotLoggedIn>

<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<%if(institutes==null && (instituteLibrary)){%>
<style>
.search-icon-lib {
    display: none;
}
</style>
<%}%>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="page-main-wrapper mdl-js library my_books" style="background:none;">
    <div class="container">
        <div class="row page_title justify-content-start align-items-center mx-0 mt-3 mt-md-0 py-4 pt-md-5 px-0 col-12 col-md-10 mx-auto show-library">
<% if(session['appInApp']==null){%>
<button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
<div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
<%}%>
<h3 class="text-primary text-primary-modifier">
    <% if("books".equals(session["entryController"])){%>
    <strong>My eBooks</strong>
    <% } else {%>
    <strong>My Library</strong>
    <%}%>

</h3>
<%if(institutes!=null){%>
<div class="search-icon-lib ml-2 btn btn-outline-primary btn-outline-primary-modifier rounded-circle d-flex justify-content-center align-items-center">
    <i class="material-icons">search</i>
</div>
<%}%>
<div class="input-group search-box" style="display: none;">
    <input type="text" class="search form-control form-control-modifier border-primary-modifier border-right-0 mt-3" placeholder="Search(title)" id="search-book" autocomplete="off" >
    <div class="input-group-append mt-3">
        <button class="btn btn-lg submit-search-btn border-primary-modifier border-left-0 text-primary text-primary-modifier bg-white" type="button" onclick="submitSearch()">
            <i class="material-icons">east</i>
        </button>
    </div>
</div>
<p id="searcherrormsg" class="mt-2 mb-0 text-danger" style="display: none"></p>

</div>
<div class="row page_title justify-content-start align-items-center mx-0 mt-3 mt-md-0 py-4 pt-md-5 px-0 col-12 col-md-10 mx-auto show-queue" style="display:none;">
    <button id="goBackLibrary" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:backToLibrary();">keyboard_backspace</button>
    <div class="mdl-tooltip" data-mdl-for="goBackLibrary">Go Back</div>
    <h3 class="text-primary text-primary-modifier"><strong>My Waiting List</strong></h3>
</div>



<div class="tab-content col-12 col-md-10 py-md-4 px-0 mx-auto">
    <div id="books" class="tab-pane active">
        <div class="row justify-content-between align-items-end m-0 show-library" id="sin">
            <div class="username">
                <%if(nameGreeting){%>
                <p class="mb-4" style="font-size: 32px;font-weight: bold;color: rgba(68,68,68,0.25);">Hey, <span class="greeting-user-name" style="color: #444;text-transform: capitalize;">${session['userdetails'].name.split(" ")[0]}!</span></p>
                <%}%>
                <div class="d-flex align-items-center mt-2 mt-md-0">
                    <% if(showMyShelf){%>
                    <%}%>

                    <%if(institutes!=null){%>

                    <div class="col-md-12 pl-0">
                        <div class="dropdown select-institute-dropdown">
                            <button class="btn btn-primary btn-primary-modifier dropdown-toggle mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" type="button" id="selectInstituteBtn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span id="selectedInstitute">Select Institute</span>
                            </button>
                            <div id="institute-list" class="dropdown-menu border-0" aria-labelledby="selectInstituteBtn">
                                <g:each in="${institutes}" var="institute" status="i">
                                    <li class="dropdown-item" class="inst-${institute.id}" data-value="${institute.id+'_'+institute.batchId}">${institute.name}</li>
                                </g:each>
                            </div>
                        </div>

                    </div>
                    <%}%>

                </div>
            </div>
            <div class="d-flex align-items-end mt-2 mt-md-0">
                <% if(showMyShelf){%>
                <div id="subjectFilter" class="row justify-content-between align-items-left ml-0 mr-3" style="display: none;">
                    <div class="dropdown form-inline">
                        Sort By &nbsp;&nbsp;<select id="sortBy" name="sortBy" onchange="sortDisplay();" class="form-control form-control-sm form-control-modifier">
                        <option value="lastRead">Last Read</option>
                        <option value="dateAdded">Date Added</option>
                        <option value="titleAsc">Title(A-Z)</option>
                        <option value="titleDesc">Title(Z-A)</option>
                    </select>
                    </div>
                </div>
                <%}%>
            </div>
        </div>
        <%if(showAccessCode){%>
        <div class="pt-3 show-library access-code-link">
            <p>Do you have  access code? <a href="/wsLibrary/accessCode">Click here</a></p>
        </div>
    </div>
    <%}%>

    <div class="books-content-wrapper show-library">
        <%if(institutes==null && (instituteLibrary)){%>
        <div id="">
            <div class='no-books-available'>
                <p class='text-center pt-4'>There's always a new beginning.<br><strong>Contact your institute for adding new books.</strong></p>
            </div>
        </div>
        <%}%>

<sec:ifLoggedIn>
        <div id="checkWaitingListBtn" class="queue-list-btn float-right show-library" style="display: none;">
            <a href="javascript:openQueueList();" class="btn btn-lg">Check Waiting List</a>
        </div>
    </sec:ifLoggedIn>
        <div id="institute-recent-read-books" class="mb-4 books-list" style="display: none;"></div>
        <div id="button-container" style="display: none;"></div>
        <div id="content-data-institute-books" class="books-list" style="display: none;"></div>
        <div id="content-data-institute-books-free" class="books-list" style="display: none;"></div>
        <div id="content-data-institute-books-paid" class="books-list" style="display: none;"></div>
    </div>
    <div class="books-content-wrapper books-list show-queue" id="content-data-books-queue" style="display: none;"></div>
    <div class="books-content-wrapper books-list show-library" id="content-data-search-books" style="display: none;"></div>


    <div class="books-content-wrapper books-list show-library" id="content-data-books">
        <%if(institutes==null && "1".equals(""+session["siteId"])) {%>
        <div class='no-books-available' id="emptyInstituteBooks" style="display: none;">
            <p class='text-center pt-5'>There's always a new beginning.<br><strong>Check some interesting books.</strong></p>
            <div class='text-center'>
                <a href='/${session['entryController']}/store?mode=browse' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Browse eBooks</a>
            </div>
        </div>
        <%}%>
    </div>
    <div id="ebpagination" class="mt-3"></div>
</div>
</div>
</div>
</section>
<script>
    var wsModes='${params.mode}';
    if(wsModes=='mybooks'){
        $('.select-institute-dropdown').remove();
        $('.search-icon-lib').remove();
    }
</script>
<div class="modal modal-modifier fade" id="deleteBook" data-backdrop="static">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h5 class="mt-3">Are you sure?</h5>
                <p id="remove-msg">You want to remove this eBook from your library?</p>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Cancel</button>
                    <button type="button" class="btn btn-lg btn-danger btn-danger-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:bookDelete();">Yes, Delete</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="bookQueueModal" class="modal modal-modifier fade">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>
            <div class="modal-body modal-body-modifier text-center">
                <div id="bookValidity" style="display: none;">
                    <h5 class="mt-3">Hey! This eBook is already issued to another student.<br></h5>
                    <p><strong>Don't worry!</strong>  You can still add this eBook to your waiting list and we will notify you soon.</p>
                    <p class="text-danger text-danger-modifier mt-2">Your waiting number <strong id="bookValidityModalText"></strong><br></p>
                    <div class="d-flex justify-content-end justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" id="addtoQueue" onclick="javascript:addBookToQueue();">Add eBook to waiting list</button>
                    </div>
                </div>
                <div id="bookReturned" style="display: none;">
                    <h5 class="mt-3 text-success text-success-modifier">Thanks! You've successfully returned the book.</h5>
                    <p>Feel free to check other eBooks to keep your learning game!</p>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Exit</button>
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:openLibrary();">View Library</button>
                    </div>
                </div>
                <div id="bookAddedQueue" style="display: none;">
                    <i class="material-icons text-success p-3" style="font-size: 40px">check_circle_outline</i>
                    <h5 id="checkAddedQueue" class="m-0 text-success text-success-modifier">Your eBook is added to waiting list.</h5>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" onclick="javascript:openQueueList();">Check waiting list</button>
                    </div>
                </div>
                <div id="bookRemovedQueue" style="display: none;">
                    <i class="material-icons text-success p-3" style="font-size: 40px">check_circle_outline</i>
                    <h5 class="m-0 text-success text-success-modifier">Book is removed successfully!</h5>
                    <div class="d-flex justify-content-center py-3">
                        <button type="button" class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0" data-dismiss="modal">Okay</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%if("1".equals(""+session["siteId"])){%>
<div class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>
<%}%>


<g:render template="/${session['entryController']}/footer_new"></g:render>

<asset:javascript src="moment.min.js"/>
<!--</div>-->

<asset:javascript src="searchContents.js"/>


<asset:javascript src="jquery.tablesorter.min.js"/>

<asset:javascript src="clock.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/funlearn/topicscripts"></g:render>
<asset:javascript src="mcq.js"/>
<script>
    document.title = "My Library";
    var urltags;
    var bookIds;
    var bookTitles;
    var batchIds;
    var bookQueueId;
    var bookurl;
    var bookTitledetails;
    var booksData;
    var pageNo;
    var pageNoForNew;
    var isAjaxCalled;
    var packageBookId;
    var libraryBooks;
    var storePackageBook=[];//Store book with packagebook Id
    var coverImage;
    var bookImageSrc;
    var urlTag='';
    var instituteId;
    var batchId;
    var queuebookId;
    var queBooksPresent=false;
    var deleteBookId;
    var lastReadBookPresent=false;
    var arrayPakcagebook=[];
    var typeOfBooks="paid";
    var allBooksLoaded = false;
    var insClickedForBatch;
    var institutePresent='${params.instituteId}';
    var showFreePaid = false;
    var instituteLibrary ='${instituteLibrary}';

    function getBooksList(){
        var insClicked= localStorage.getItem('instituteClicked');
        $('.loading-icon').removeClass('hidden');
        var check=false;
        pageNo = 1;
        allBooksLoaded = false;
        isAjaxCalled = false;
        if (!allBooksLoaded) {
            <g:remoteFunction controller="wsLibrary" action="getUsersBooks"  onSuccess='mainDisplayBooks(data);' params="'pageNo='+pageNo"/>
        }

    }
    function mainDisplayBooks(data){
        $('.loading-icon').addClass('hidden');
        booksData = data;
        if(instituteLibrary!='true') {
            displayBooks(data);
        }
    }


    //Main Library
    function displayBooks(data){

        libraryBooks=data.books;
        var totalBooksCount = data.count;
        var newObj = {};
        var newArr = [];
        if(libraryBooks.length<0 || libraryBooks == '' || libraryBooks == null || libraryBooks == 'null') {
            allBooksLoaded = true;
            isAjaxCalled = true;
            $("#loadMoreButtonSelf").hide();
        }

        if(libraryBooks != null && libraryBooks.length>0) {
            $("#loadMoreButtonSelf button").text("Show More");
            for(var i=0; i< libraryBooks.length; i++){
                newObj[libraryBooks[i]['id']] = libraryBooks[i];
            }
            var keys = Object.keys(newObj);
            for(var i=keys.length-1; i>=0; i--){
                newArr.push(newObj[keys[i]])
            }
            libraryBooks=newArr;

            var lastReadBooks = JSON.parse(data.lastReadBooks);
            for (var i = 0; i < libraryBooks.length; ++i) {
                //this is for the book ids which may not have been viewed.
                libraryBooks[i].sortNo=99999;
            }

            //now add the sort number to the books
            for(var j = 0; j < lastReadBooks.length;j++){
                for (var i = 0; i < libraryBooks.length; ++i) {
                    //this is for the book ids which may not have been viewed.
                    if(lastReadBooks[j].bookId==libraryBooks[i].id){
                        libraryBooks[i].sortNo=j;
                        break;
                    }
                }
            }

            if(document.getElementById("sortBy").selectedIndex==0) {
                libraryBooks.sort(SortByLastRead);
            }else if(document.getElementById("sortBy").selectedIndex==1) {
                //dont do anything as books are by default sorted by date added.
            } else if(document.getElementById("sortBy").selectedIndex==2) {
                libraryBooks.sort(SortByTitle);
            }else if(document.getElementById("sortBy").selectedIndex==3) {
                libraryBooks.sort(SortByTitle);
                libraryBooks.reverse(SortByTitle);
            }

            var htmlStr="";
            var libraryBooksCount = 0;

            <% if("books".equals(session["entryController"])){%>
            htmlStr ="<h4>eBooks <p id='total-books-of-user'></p></h4>";
            <%} else {%>
            htmlStr ="<h4>All eBooks <p id='total-books-of-user'></p></h4>";
            <%}%>
            htmlStr +="<div id='show-purchased-book' class='row'>";
            for(var books of libraryBooks ){
                if(books.showInLibrary=="Yes") {
                    libraryBooksCount++;
                    htmlStr +="<div class='col-6 col-md-4 col-lg-3 library-book' id='pack-"+books.id+"'>"+
                        displayLibraryBooks(books);
                    htmlStr +="</div>";
                }
            }
            htmlStr +="</div>" +
            "<div id='loadMoreButtonSelf' class='text-center mt-4' style='display: none;'>" +
            "<button class='btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'></button>" +
            "</div>";
            document.getElementById("content-data-books").innerHTML =  htmlStr;
            uncoverBookUI();
            var noOfBooks=data.count;
            if(!institutePresent) {
                document.getElementById('total-books-of-user').innerHTML = noOfBooks;
            }

            if(totalBooksCount >= 30) {
                $("#loadMoreButtonSelf").show();
                $("#loadMoreButtonSelf button").text("Show More");
            } else {
                $("#loadMoreButtonSelf").hide();
            }

            $("#loadMoreButtonSelf button").click(function() {
                $(this).text('Loading..');
                isAjaxCalled = true;
                pageNo = pageNo + 1;
                getPaginatedUsersBooks(pageNo);
            });

            //hide packageid book which displaying sperately instead of under book in library.
            var arrayHide;
            for(var m=0;m<storePackageBook.length;m++){
                arrayHide = JSON.parse("[" + storePackageBook[m] + "]");
                for(var n=0;n<arrayHide.length;n++){
                    var item = libraryBooks.find(item =>
                        item.id === arrayHide[n]
                    );
                    if(item != undefined) {
                        $('#content-data-books #pack-'+item.id).hide();
                    }
                }
            }
            $("#subjectFilter").show();
        }
        else{
            $("#loadMoreButtonSelf").hide();
            if(instituteLibrary=="true") {
                document.getElementById("content-data-books").innerHTML = nobooksUILayerLibwonder();
            }
            else{
                document.getElementById("content-data-books").innerHTML = nobooksUILayer();
            }
            $('.loading-icon').addClass('hidden');
            $('#show-more').hide();
            $('body').css({
                'position' : 'relative'
            });
        }

        <% if("books".equals(session["entryController"])){%>
        var purchasedBookId = localStorage.getItem('purchasedBookId');
        var listBookId = "";
        var check = false;
        if(purchasedBookId!="" && purchasedBookId!=null) {
            $('#content-data-books .library-book').each(function() {
                listBookId = $(this).attr('id').split('-').pop();
                if(purchasedBookId===listBookId) {
                    check = true;
                    $("#pack-"+listBookId).addClass("new-book");
                    $('#content-data-books').addClass('has-latest-books');
                } else if(purchasedBookId!=listBookId && check==false) {
                    $("#pack-"+listBookId).removeClass("new-book");
                }
            });
        } else {
            $("#content-data-books .library-book").removeClass("new-book");
            $('#content-data-books').removeClass('has-latest-books');
        }

        $(".has-latest-books .new-book").on('click', function () {
            localStorage.setItem('purchasedBookId','');
        });

        $('#content-data-books .new-book').prependTo('#content-data-books #show-purchased-book');

        <%}%>

        $(".app_in_app .my_books #content-data-books a.library_book, .app_in_app .my_books .package_books a").on('click', function () {
            $('.loading-icon').removeClass('hidden');
        });
    }

    //Paginated books
    function getPaginatedUsersBooks(pageNo) {
        if (!allBooksLoaded) {
            <g:remoteFunction controller="wsLibrary" action="getUsersBooks"  onSuccess='displayPaginatedUserBooks(data);' params="'pageNo='+pageNo"/>
        }
    }

    function displayPaginatedUserBooks(data) {
        libraryBooks=data.books;
        var totalBooksCount = data.count;
        var htmlStr = "";
        if(libraryBooks.length < 0 || libraryBooks == '' || libraryBooks == null || libraryBooks == 'null' || libraryBooks == '[]') {
            allBooksLoaded = true;
            isAjaxCalled = true;
            $("#loadMoreButtonSelf").hide();
        } else {
            $("#loadMoreButtonSelf button").text("Show More");
            isAjaxCalled = false;
            if(libraryBooks.length > 0 ){

                for(var books of libraryBooks ){
                    htmlStr +="<div class='col-6 col-md-4 col-lg-3 institute_book fadein-animated'>"+
                        displayLibraryBooks(books);
                    htmlStr +="</div>";
                }

                $("#content-data-books").find("#show-purchased-book").append(htmlStr);
                uncoverBookUI();

                //hide packageid book which displaying sperately instead of under book in library.
                var arrayHide;
                for(var m=0;m<storePackageBook.length;m++){
                    arrayHide = JSON.parse("[" + storePackageBook[m] + "]");
                    for(var n=0;n<arrayHide.length;n++){
                        var item = libraryBooks.find(item =>
                            item.id === arrayHide[n]
                        );
                        if(item != undefined) {
                            $('#content-data-books #pack-'+item.id).hide();
                        }
                    }
                }

                if(totalBooksCount >= 30) {
                    $("#loadMoreButtonSelf").show();
                    $("#loadMoreButtonSelf button").text("Show More");
                } else {
                    $("#loadMoreButtonSelf").hide();
                }


            }
        }
    }

    //InstituteLibrary
    function getBooksListLibwonder(){
        var insClicked= localStorage.getItem('instituteClicked');
        $('.loading-icon').addClass('hidden');
        var check=false;
        if(insClicked!="" && insClicked!=null){
            $("#subjectFilter, #generateBtn, #relatedBooksContainer, #bestSellerBooksContainer").hide();
            $("#institute-list").find('li').each(function(){
                var current = $(this);
                if(current.children().size() > 0) {return true;}
                var selectedValue = current.data("value");
                instituteId = selectedValue.substring(0,selectedValue.indexOf('_'));
                batchId = selectedValue.substring(selectedValue.lastIndexOf('_')+1);

                if(insClicked===batchId) {
                    check=true;
                    $("#mySelfBtn").removeClass("generate");
                    $("#selectInstituteBtn").addClass("generate");
                    $("#selectedInstitute").text($(this).text());
                    $(this).addClass('selected');
                    $('.loading-icon').addClass('hidden');
                }
                else if(insClicked!=batchId && check==false) {
                    $("#content-data-books").hide();
                }
            });
        } else {
            $('.loading-icon').addClass('hidden');
            $("#institute-list li:first-child").trigger('click');
        }
    }
    function displayBooksForInstitute(data) {
        var htmlStr = "";
        if (data.lastReadBooksForInstitute != null) showInstituteLastReadBooks(data);
        $("#content-data-institute-books, #institute-recent-read-books, #checkWaitingListBtn, #total-books-of-user, #content-data-institute-books-paid , #content-data-institute-books-free, #button-container ").show();
        $("#content-data-books, #content-data-search-books").hide();
        var instituteBooks = JSON.parse(data.books);

        var freeBooksCount = data.totalBooksFree;
        var paidBooksCount = data.totalBooksPaid;

        for (var i = 0; i < instituteBooks.length; i++)
        {
            if(instituteBooks[i].showtabs == "true")
            {
                var showTab = true;
                showFreePaid = showTab;
            }
            else {
                showTab = false;
                showFreePaid = showTab;
            }
        }
        if (showTab == false ) {
            typeOfBooks = "";
            $('#content-data-institute-books-paid , #paidBooks,#content-data-institute-books-free , #freeBooks').hide();
            $('#content-data-institute-books').show();
            if (instituteBooks.length > 0) {
                $('.loading-icon').addClass('hidden');
                htmlStr += "<div><a id='allBooks'>All eBooks<p id='total-allbooks-of-user' style='display:inline'></p></a>" +"</div>"+ "<div class='row institute-books-list pt-3'>";
                for (var books of instituteBooks) {
                    htmlStr += "<div class='col-6 col-md-4 col-lg-3 institute_book'>" +
                        displayLibraryBooks(books);
                    htmlStr += "</div>";
                }
                htmlStr += "</div>" +
                "<div id='loadMoreButton' class='text-center mt-4' style='display: none;'>" +
                "<button class='btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'></button>" +
                "</div>";
                document.getElementById("content-data-institute-books").innerHTML = htmlStr;

                $('#content-data-institute-books .dropup').remove();
                uncoverBookUI();
                var noOfBooks = instituteBooks.length;
                document.getElementById('total-allbooks-of-user').innerHTML = data.totalBooks ;
                $('.institute_book a').on('click', function () {
                    $('.loading-icon').removeClass('hidden');
                    setTimeout(function () {
                        $('.mozilla .loading-icon').addClass('hidden');
                    }, 1500);
                });
                var list = $('.institute-books-list .institute_book').size();
                if(instituteBooks.length >= 30) {
                    $("#loadMoreButton").show();
                    $("#loadMoreButton button").text("Show More");
                } else {
                    $("#loadMoreButton").hide();
                }

                $("#loadMoreButton button").click(function() {
                    $(this).text('Loading..');
                    isAjaxCalled = true;
                    pageNo = pageNo + 1;
                    getPaginatedInstituteBooksData(batchId, pageNo);
                });
            }
            else {
                if (instituteLibrary="true") {
                    document.getElementById("content-data-institute-books").innerHTML = nobooksUILayerLibwonder();
                } else {
                    document.getElementById("content-data-institute-books").innerHTML = nobooksUILayer();
                }
                $('.loading-icon').addClass('hidden');
                $('#show-more').hide();
                $('#loadMoreButton').hide();
                $('body').css({'position': 'relative'});
            }
        }
        else if(showTab == true ){
            $('#content-data-institute-books').hide();
            htmlStr += "<div id='button-inner-container' style='display:flex'> <div><a id='paidBooks' href='javascript:paidBooks()'; >Paid eBooks<p id='total-paidbooks-of-user' style='display:inline'></p></a></div>" + "<div><a  id ='freeBooks' href='javascript:freeBooks()'; >Free eBooks<p id='total-freebooks-of-user' style='display:inline'></p></a></div></div>";
            document.getElementById("button-container").innerHTML = htmlStr;
            var insClicked= localStorage.getItem('instituteClicked');
            $('.loading-icon').removeClass('hidden');
            var check=false;
            if(insClicked!="" && insClicked!=null){
                $("#subjectFilter, #generateBtn, #relatedBooksContainer, #bestSellerBooksContainer").hide();
                $("#institute-list").find('li').each(function() {
                    var current = $(this);
                    if (current.children().size() > 0) {
                        return true;
                    }
                    var selectedValue = current.data("value");
                    instituteId = selectedValue.substring(0, selectedValue.indexOf('_'));
                    batchId = selectedValue.substring(selectedValue.lastIndexOf('_') + 1);
                    if (insClicked === batchId) {
                        check = true;
                        $("#mySelfBtn").removeClass("generate");
                        $("#selectInstituteBtn").addClass("generate");
                        $("#selectedInstitute").text($(this).text());
                        $(this).addClass('selected');
                        pageNoForNew = 1;
                        isAjaxCalled = false;
                        insClickedForBatch = insClicked;
                        $('.loading-icon').addClass('hidden');
                        if (!allBooksLoaded) {
                            $('.loading-icon').addClass('hidden');

                            if ((freeBooksCount <= 0 || freeBooksCount == null || freeBooksCount == "null" || freeBooksCount == "") && (paidBooksCount > 0 || paidBooksCount != "null" || paidBooksCount != null || paidBooksCount != "")) {
                                typeOfBooks = "paid";
                                $('#button-inner-container').css('display','flex');
                                $('#content-data-institute-books-free , #freeBooks').hide();
                                $('#content-data-institute-books-paid , #paidBooks').show();
                            } else if ((freeBooksCount > 0 || freeBooksCount != null || freeBooksCount != "null" || freeBooksCount != "") && (paidBooksCount <= 0 || paidBooksCount == null || paidBooksCount == "null" || paidBooksCount == "")) {
                                typeOfBooks = "free";
                                $('#button-inner-container').css('display','unset');
                                $('#content-data-institute-books-paid , #paidBooks').hide();
                                $('#content-data-institute-books-free , #freeBooks').show();
                            } else if ((freeBooksCount > 0 || freeBooksCount != null || freeBooksCount != "null" || freeBooksCount != "") && (paidBooksCount > 0 || paidBooksCount != "null" || paidBooksCount != null || paidBooksCount != "")) {
                                typeOfBooks = "paid";
                            }

                            if((freeBooksCount != 0 || freeBooksCount != null || freeBooksCount != "null" || freeBooksCount != "") || (paidBooksCount != 0 || paidBooksCount != null || paidBooksCount != "null" || paidBooksCount != "")) {
                                getFreePaidBooks(insClickedForBatch,typeOfBooks,pageNoForNew);
                            } else {
                                $('#content-data-institute-books-free , #freeBooks, #paidBooks').hide();
                                $('#content-data-institute-books-paid').html(" <div class='no-books-available'><p class='text-center pt-5'>There's always a new beginning.<br><strong>Contact your institute for adding new books.</strong></p></div>");
                            }
                        }
                    }
                })
            }
        }

        else {
            if (instituteLibrary=="true") {
                document.getElementById("content-data-institute-books").innerHTML = nobooksUILayerLibwonder();
            } else {
                document.getElementById("content-data-institute-books").innerHTML = nobooksUILayer();
            }
            $('.loading-icon').addClass('hidden');
            $('#show-more').hide();
            $('#button-container').css('display','none');
            $('#content-data-institute-books-paid').css('display','none');
            $('#content-data-institute-books-free').css('display','none');
            $('body').css({'position': 'relative'});
        }
    }
    function freeBooks() {
        $('#freeBooks  ').addClass('mystyle');
        $('#paidBooks').removeClass('mystyle');
        typeOfBooks = "free";
        pageNoForNew = 1;
        allBooksLoaded = false;
        isAjaxCalled = false;
        getFreePaidBooks(insClickedForBatch,typeOfBooks,pageNoForNew);
    }

    function paidBooks(){
        $('#paidBooks').addClass('mystyle');
        $('#freeBooks').removeClass('mystyle');
        typeOfBooks = "paid";
        pageNoForNew = 1;
        allBooksLoaded = false;
        isAjaxCalled = false;
        getFreePaidBooks(insClickedForBatch,typeOfBooks,pageNoForNew);
    }

    function getFreePaidBooks(batchId,typeOfBooks,pageNoForNew) {
        if (!allBooksLoaded) {
            <g:remoteFunction controller="wsLibrary" action="getPaginatedInstituteBooksNew" params="'batchId='+batchId+'&pageNo='+pageNoForNew+'&type='+typeOfBooks"  onSuccess='displayBooksPaidFreeForInstitute(data);'/>
            return false;
        }
        return false;
    }

    function displayBooksPaidFreeForInstitute(data){

        var Nbooks = JSON.parse(data.books);
        if(Nbooks.length<0 || data.books == '[]' || data.books == null || data.books == 'null' ){
            allBooksLoaded = true;
            $('.loading-icon').addClass('hidden');
            $('.loading-icon').css('display','none');
            $("#loadMoreButtonFree, #loadMoreButtonPaid").hide();
        }
        var htmlStr = "";
        if (data.lastReadBooksForInstitute != null) showInstituteLastReadBooks(data);
        if(typeOfBooks == "paid") {
            $("#content-data-institute-books-paid, #institute-recent-read-books, #checkWaitingListBtn, #total-books-of-user").show();
            $("#content-data-books, #content-data-search-books,#content-data-institute-books-free").hide();
            $('#paidBooks').addClass('mystyle');
            $('#freeBooks').removeClass('mystyle');
            $("#loadMoreButtonFree").hide();
        }
        else if(typeOfBooks == "free")
        {
            $("#content-data-institute-books-free, #institute-recent-read-books, #checkWaitingListBtn, #total-books-of-user").show();
            $("#content-data-books, #content-data-search-books,#content-data-institute-books-paid").hide();
            $('#paidBooks').removeClass('mystyle');
            $('#freeBooks').addClass('mystyle');
            $("#loadMoreButtonPaid").hide();
        }
        else if(typeOfBooks == ""){
            $('#paidBooks').addClass('mystyle');
            $('#freeBooks').removeClass('mystyle');
            $("#loadMoreButtonFree").hide();
        }
        var instituteFreePaidBooks = JSON.parse(data.books);
        if (instituteFreePaidBooks.length > 0) {
            htmlStr +=   "<div class='row institute-books-list pt-3'>";
            for (var books of instituteFreePaidBooks) {
                htmlStr += "<div class='col-6 col-md-4 col-lg-3 institute_book'>" +
                    displayLibraryBooks(books);
                htmlStr += "</div>";
            }
            htmlStr += "</div>";
            if(typeOfBooks == "paid") {
                htmlStr += "<div id='loadMoreButtonPaid' class='text-center mt-4' style='display: none;'>" +
                    "<button class='btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'></button>" +
                    "</div>";
            }
            if(typeOfBooks == "free") {
                htmlStr += "<div id='loadMoreButtonFree' class='text-center mt-4' style='display: none;'>" +
                "<button class='btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'></button>" +
                "</div>";
            }

            if(typeOfBooks == "free")
            {
                document.getElementById("content-data-institute-books-free").innerHTML = htmlStr;

                if(instituteFreePaidBooks.length >= 30) {
                    $("#loadMoreButtonFree").show();
                    $("#loadMoreButtonFree button").text("Show More");
                } else {
                    $("#loadMoreButtonFree").hide();
                }

                $("#loadMoreButtonFree button").click(function() {
                    $(this).text('Loading..');
                    isAjaxCalled = true;
                    pageNoForNew = pageNoForNew + 1;
                    displayBooksPaidFreeForInstitutePaginated(insClickedForBatch, pageNoForNew, typeOfBooks);
                });
            }
            else if(typeOfBooks == "paid")
            {
                document.getElementById("content-data-institute-books-paid").innerHTML = htmlStr;

                if(instituteFreePaidBooks.length >= 30) {
                    $("#loadMoreButtonPaid").show();
                    $("#loadMoreButtonPaid button").text("Show More");
                } else {
                    $("#loadMoreButtonPaid").hide();
                }

                $("#loadMoreButtonPaid button").click(function() {
                    $(this).text('Loading..');
                    isAjaxCalled = true;
                    pageNoForNew = pageNoForNew + 1;
                    displayBooksPaidFreeForInstitutePaginated(insClickedForBatch, pageNoForNew, typeOfBooks);
                });
            }
        }
        $('#content-data-institute-books-paid .dropup,#content-data-institute-books-free .dropup').remove();
        uncoverBookUI();
        $('.loading-icon').addClass('hidden');

        var noOfBooks = data.totalBooksFree;
        var totalUserBooks;
        document.getElementById('total-freebooks-of-user').innerHTML = noOfBooks;

        var noOfBooks = data.totalBooksPaid;
        var totalUserBooks;
        document.getElementById('total-paidbooks-of-user').innerHTML = noOfBooks ;



        $('.institute_book a').on('click', function () {
            $('.loading-icon').removeClass('hidden');
            setTimeout(function () {
                $('.mozilla .loading-icon').addClass('hidden');
            }, 1500);
        });


    }


    //Remove book from library
    function removeFromLibrary(bookId){
        deleteBookId = bookId;
        $('#deleteBook').modal('show');

    }

    //Paginated books
    function getPaginatedInstituteBooksData(batchId, pageNo) {
        if (!allBooksLoaded) {
            <g:remoteFunction controller="wsLibrary" action="getPaginatedInstituteBooks" params="'batchId='+batchId+'&pageNo='+pageNo"  onSuccess='displayPaginatedInstituteBooks(data);'/>
        }
    }
    function displayPaginatedInstituteBooks(data) {
        var htmlStr = "";
        var instituteBooks = JSON.parse(data.books);
        var booksData = instituteBooks?instituteBooks:[];
        if(booksData.length<0 ||data.books == '[]' || data.books == null || data.books == 'null' || booksData == null || booksData  == "" || booksData  == "null"){
            allBooksLoaded = true;
        }
        if(instituteBooks == '' || instituteBooks == null || instituteBooks == 'null') {
            isAjaxCalled = true;
            $("#loadMoreButton").hide();
        } else {
            $("#loadMoreButton button").text("Show More");
            isAjaxCalled = false;
            if(instituteBooks.length > 0 ){

                for(var books of instituteBooks ){
                    htmlStr +="<div class='col-6 col-md-4 col-lg-3 institute_book fadein-animated'>"+
                        displayLibraryBooks(books);
                    htmlStr +="</div>";
                }

                $("#content-data-institute-books").find(".institute-books-list").append(htmlStr);
                $('#content-data-institute-books .dropup').remove();
                uncoverBookUI();

                if(instituteBooks.length >= 30) {
                    $("#loadMoreButton").show();
                    $("#loadMoreButton button").text("Show More");
                } else {
                    $("#loadMoreButton").hide();
                }

                $("#loadMoreButton button").click(function() {
                    $(this).text('Loading..');
                    isAjaxCalled = true;
                    pageNo = pageNo + 1;
                    getPaginatedInstituteBooksData(batchId, pageNo);
                });
            }
        }
    }

    //Access Code
    function checkAccessCode(){
        $("#invalidCode").hide();
        var bookCode  = document.getElementById("accessCode").value;
        if(bookCode&&/^\d+$/.test(bookCode)){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wsshop" action="bookCdValidate"  onSuccess='accessModeChecked(data);' params="'bookCode='+bookCode"/>
        }
    }

    function accessModeChecked(data) {
        var status = data.status;
        $('.loading-icon').addClass('hidden');
        if(status=="allocated"){
            alert("eBook successfully added to your library. We will now load it.");
            $('.loading-icon').removeClass('hidden');
            getBooksList();
        }else{
            alert('Invalid code');
        }
    }

    //LastReadBooks
    function showInstituteLastReadBooks(data){
        var lastBooks=JSON.parse(data.lastReadBooksForInstitute);
        var newObj = {};
        var newArr = [];
        if(lastBooks!=null) {
            for (var i = 0; i < lastBooks.length; i++) {
                newObj[lastBooks[i]['bookId']] = lastBooks[i];
            }
            var keys = Object.keys(newObj);
            for (var i = keys.length - 1; i >= 0; i--) {
                newArr.push(newObj[keys[i]])
            }
            lastBooks = newArr;
        }
        var htmlStr='';
        if(lastBooks!=null&&lastBooks.length > 0) {
            lastReadBookPresent=true;
            htmlStr += "<div class='d-flex justify-content-between align-items-center queue-list-btn show-library mb-3'><h4>Currently Reading <p class='total-books mb-0 mt-3' id='total-books-of-user'></p></h4>" +
                "</div>" + "<div class='row recent-read-books-list pl-0 list-inline'>";
            for (var books of lastBooks) {
                htmlStr += "<div class='col-6 col-md-4 col-lg-3 recent_book'>" +
                    displayLibraryBooks(books);
                htmlStr += "</div>";
            }
            lastReadBookPresent=false;

            htmlStr += "</div>";
            htmlStr +="<span class='showMore'>Show more..</span><span class='showLess'>Show less..</span><div class='d-flex justify-content-end d-md-none mb-2'><img src='${assetPath(src: 'ws/icon-dots.svg')}'></div>";
            document.getElementById("institute-recent-read-books").innerHTML = htmlStr;
            $('.loading-icon').addClass('hidden');
            uncoverBookUI();
            var noOfBooks=lastBooks.length;
            var totalUserBooks;

            document.getElementById('total-books-of-user').innerHTML = noOfBooks ;
        }
        else {
            if(instituteLibrary=="true") {
                document.getElementById("institute-recent-read-books").innerHTML = "";
            } else {
                document.getElementById("institute-recent-read-books").innerHTML = "";
            }
            $('.loading-icon').addClass('hidden');
            $('#show-more').hide();
            $('body').css({
                'position' : 'relative'
            });
        }
        var list = $('.recent-read-books-list .recent_book').size();

        if($(window).width() >= 992) {
            if(list > 4){
                $('.recent_book').hide();
                $('.recent-read-books-list').find('.recent_book:lt(4)').show();
                $('.showLess').hide();
                $('.showMore').click(function(ev) {
                    $(ev.currentTarget).parent().find('.recent_book').show();
                    $('.showLess').show();
                    $(this).hide();
                });
                $('.showLess').click(function(ev) {
                    $(ev.currentTarget).parent().find('.recent_book').not(':lt(4)').hide();
                    $('.showMore').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop:0}, 'slow');
                });
            } else {
                $('.showMore, .showLess').hide();
            }
        } else if($(window).width() >= 768) {
            if(list > 3){
                $('.recent_book').hide();
                $('.recent-read-books-list').find('.recent_book:lt(3)').show();
                $('.showLess').hide();
                $('.showMore').click(function(ev) {
                    $(ev.currentTarget).parent().find('.recent_book').show();
                    $('.showLess').show();
                    $(this).hide();
                });
                $('.showLess').click(function(ev) {
                    $(ev.currentTarget).parent().find('.recent_book').not(':lt(3)').hide();
                    $('.showMore').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop:0}, 'slow');
                });
            } else {
                $('.showMore, .showLess').hide();
            }
        } else {
            $('.showMore, .showLess').hide();
        }


        $('.recent_book a').on('click', function () {
            $('.loading-icon').removeClass('hidden');
            setTimeout(function() {
                $('.mozilla .loading-icon').addClass('hidden');
            }, 1500);
        });

    }



    //MAIN CONTROLLER FOR LIBRARIES
    function displayLibraryBooks(books){
        var bookTitle=books.title || books.bookTitle;
        var bookId=books.id || books.bookId;
        coverImage=books.coverImage;
        if(siteId==1) {
            bookImageSrc = "/funlearn/showProfileImage?id=" + bookId + "&fileName=" + coverImage + "&type=books&imgType=webp";
        } else {
            bookImageSrc="/funlearn/showProfileImage?id="+bookId+"&fileName="+coverImage+"&type=books&imgType=passport";
        }
        var publisherName=books.publisher;
        var level=books.level;
        var syllabus=books.syllabus;
        var bookPrice=books.price;
        var grade=books.grade;
        var batchId=books.batchId;
        var dataState="registered";
        var institutePresent=books.instituteId;
        packageBookId=books.packageBookIds;
        var deleteBook='';
        var booksTag = books.bookType;
        var booksTagStr = '';
        if(level != "") {
            if ("School" === level){
                urlTag = replaceAll(syllabus?syllabus:'',' ', '-').toLowerCase();
            }
            else {
                urlTag = replaceAll(grade?grade:'',' ', '-').toLowerCase();
            }
        }
        else {
            urlTag = "book";
        }

        var bookUrl= "/" + replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId + "&siteName=${session['entryController']}";
        if(queBooksPresent){
            bookUrl='';
        }
        if(coverImage !=''){
            var coverImageStr ="<img src='" + bookImageSrc + "'>";
        }
        else{
            var coverImageStr = "<div class=\"uncover\">\n" +
                "                                            <p>"+bookTitle+"</p>\n" +
                "                                         </div>\n" ;
        }

        if(siteId!=3 && siteId!=37) {
            if ((bookPrice == 0 || bookPrice == 0.0 || bookPrice == undefined) && packageBookId == undefined) {
                deleteBook = "<div class='dropup d-flex'>" +
                    "<button class='btn dropdown-toggle bg-white' data-toggle='dropdown'><i class='material-icons'>more_vert</i></button>" +
                    "<div class='dropdown-menu'>";
                if (queBooksPresent) {
                    deleteBook += "<a class='dropdown-item delete' href='javascript:removeBookFromUserQueue(" + bookId + ", " + batchId + ");'>Remove</a>";
                } else if (lastReadBookPresent) {
                    deleteBook += "<a class='dropdown-item delete' href='javascript:returnBookFromUser(" + bookId + "," + batchId + ")'>Return</a>";
                } else {
                    deleteBook += "<a class='dropdown-item delete' href='javascript:removeFromLibrary(" + bookId + ")'>Delete</a>";
                }
                deleteBook += "</div>" +
                    "</div>";
            } else {
                deleteBook = '';
            }
        }

        if (booksTag == "print") {
            booksTagStr ="Print Book";
        } else if(booksTag == "ebook"){
            booksTagStr ="eBook";
        }else if(booksTag =="testseries"){
            booksTagStr ="Test Series";
        }else if(booksTag =="onlinecourse") {
            booksTagStr ="Online Course";
        }else if(booksTag =="liveclasses"){
            booksTagStr ="Live Classes";
        }else if(booksTag =="mcqsebook"){
            booksTagStr ="MCQs eBook";
        }else if(booksTag =="previouspapers"){
            booksTagStr ="Previous Papers";
        }else{
            booksTagStr ="eBook";
        }
        var template ='';
        if((packageBookId != "" && packageBookId != null && packageBookId != "null")){
            //Store book with packagebook Id
            var packageBooks;
            storePackageBook.push(packageBookId);//push package bookids to array
            for(var j=0;j<storePackageBook.length;j++){
                arrayPakcagebook = JSON.parse("[" + storePackageBook[j] + "]"); //parse to package bookid string to array

            }
            template += packageBookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,booksTagStr);
        }
        else {
            template +=bookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,batchId,institutePresent,booksTagStr);
        }
        return template;
    }


    //MAIN UI TEMPLATE
    function bookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,batchId,instituteId,booksTagStr){
        if(batchId!="undefined" && batchId!=undefined && batchId!=''){
            bookUrl='javascript:addBookToUserByUser('+bookId+',"'+batchId+'","'+urlTag+'","'+ replaceAll(bookTitle,' ','-').toLowerCase()+'");';
        }
        else if(batchId!="undefined" && batchId!=undefined && batchId!='' && institutePresent !='undefined' && institutePresent!=undefined && institutePresent!=''){
            bookUrl= "/" + replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId +"&instituteId=" + instituteId + "&batchId=" + batchId + "&siteName=${session['entryController']}";
        }
        var bookUItemplate= "  <div class=\"card\">\n" +
            "                                    <a class=\"lib-showcase library_book\"    href='" + bookUrl + "'>\n"
            + coverImageStr;
        <%if(!"3".equals(""+session["siteId"])){%>
        bookUItemplate +="<h3 class='book-tag'>"+ booksTagStr +"</h3>";
        <%}%>
        bookUItemplate +="</a>\n" +
            "                                    <div class=\"card-body d-flex justify-content-between\">\n" +
            "                                        <a class=\"card-text library_book\" href='" + bookUrl + "'>" + bookTitle + "</a>\n"
            + deleteBook +
            "                                    </div>" +
            "<small class=\"book-publisher-name\">" + publisherName + "</small>\n" +
            "                                </div>\n";
        return bookUItemplate;

    }

    //PACKAGE BOOK TEMPLATE
    function packageBookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,booksTagStr){
        var template='';
        template +="<div id='packbooks"+ bookId +"'>"+
            "<div class='lib-showcase' onclick='packageToggle("+bookId+")' data-toggle='collapse' aria-expanded='false' aria-controls='packageBooksLists"+ bookId +"'>\n";
        bookUrl='javascript:void(0)';
        template +=bookUILayer(bookId,bookTitle,bookUrl,coverImageStr,publisherName,deleteBook,'','',booksTagStr);
        bookUrl="/" +replaceAll(bookTitle,' ','-').toLowerCase() + "/ebook?bookId=" + bookId + "&siteName=${session['entryController']}";
        template +="</div>"+ "</div>";
        //collapsable dropdown
        template +="<div class='package_book_collapse collapse row' id='packageBooksLists"+ bookId +"'>\n" +
            "  <div class='package_books p-3 my-1 mb-4'>\n" +
            "<div class='package_book_list'>"+

            "<a class=\"d-block align-items-center\" href='" + bookUrl + "'>\n"
        if(coverImage !='') {
            template +="<img style='width:80px;height:100px;margin-bottom:10px;' src='" + bookImageSrc + "'><span>" + bookTitle + "</span>";
            if(publisherName !="" && publisherName !="null" && publisherName !=null) {
                template += "<small class='book-publisher-name'>" + publisherName + "</small>";
            }
            else{
                template += "<small class='book-publisher-name'></small>";
            }
            template += "</a>\n";
        }
        else{
            template +="<div class='uncover p-1' style='min-width:80px;height:100px;margin-bottom:10px;'>"+
                "<p style='font-size: 9px;'>"+bookTitle+"</p>"+
                "</div><span>"+ bookTitle +"</span>";
            if(publisherName !="" && publisherName !="null" && publisherName !=null) {
                template += "<small class='book-publisher-name'>" + publisherName + "</small>";
            }
            else{
                template += "<small class='book-publisher-name'></small>";
            }

            template += "</a>";
        }
        template +="</div>";
        for (var l = 0; l < arrayPakcagebook.length; l++) {
            //check packageid present in bookid and display package books
            var item = libraryBooks.find(item =>
                item.id === arrayPakcagebook[l]
            );
            if(item != undefined) {
                if (item.coverImage != '') {
                    if(siteId==1) {
                        var subImg = "/funlearn/showProfileImage?id=" + item.id + "&fileName=" + item.coverImage + "&type=books&imgType=webp";
                    } else {
                        var subImg = "/funlearn/showProfileImage?id=" + item.id + "&fileName=" + item.coverImage + "&type=books&imgType=passport";
                    }
                }

                template += "<div class='package_book_list'>" +
                    "<a href='/" + item.title.replace(/\s+/g, '-').toLowerCase() + "/ebook?bookId=" + item.id + "&siteName=${session['entryController']}' class='d-block align-items-center'>\n";
                if (item.coverImage != '') {
                    template += "<img style='width:80px;height:100px;margin-bottom:10px;' src='" + subImg + "'><span>" + item.title + "</span>";
                    if(item.publisher !="" && item.publisher !="null" && item.publisher !=null) {
                        template += "<small class='book-publisher-name'>" + item.publisher + "</small>";
                    }
                    template += "</a>\n";
                } else {
                    template += "<div class='uncover p-1' style='min-width:80px;height:100px;margin-bottom:10px;'>" +
                        "<p style='font-size: 9px;'>" + item.title + "</p>" +
                        "</div><span>" + item.title + "</span>";
                    if(item.publisher !="" && item.publisher !="null" && item.publisher !=null) {
                        template += "<small class='book-publisher-name'>" + item.publisher + "</small>";
                    }
                    template += "</a>";
                }
                template += "</div>";
            }

        }
        template +=  "</div>" +
            "</div>";

        return template;
    }
    function packageToggle(bookid){
        $('.collapse').collapse('hide');
        $("#packageBooksLists"+bookid).collapse('toggle');
        $('.loading-icon').addClass('hidden');
    }


    //VALIDATION MESSAGES
    function nobooksUILayerLibwonder(){
        var htmlStr="<div class='no-books-available'>" +
            "<p class='text-center pt-5'>There's always a new beginning.<br><strong>Contact your institute for adding new books.</strong></p>" +
            "</div>";
        return htmlStr;
    }
    function nobooksUILayer(){
        var htmlStr="<div class='no-books-available'>" +
            "<p class='text-center pt-5'>There's always a new beginning.<br><strong>Check some interesting books.</strong></p>" +
            "<div class='text-center'>" +
            "<a href='/${session['entryController']}/store?mode=browse' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Browse eBooks</a></div>" +
            "</div>";
        return htmlStr;
    }

    //COLOR THE NON COVERIMAGE BOOKS
    function uncoverBookUI(){
        var uncover = document.querySelectorAll(".uncover");
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        for(var i=0 ; i < uncover.length; i++) {
            uncover[i].style.background = colors[i%11];
        }
    }


    //ADD BOOK TO USER
    function addBookToUserByUser(bookId,batchId,urltag,bookTitle){
        $('.loading-icon').removeClass('hidden');
        urltags=urltag;
        bookIds=bookId;
        bookTitles=bookTitle;
        batchIds=batchId;
        <g:remoteFunction controller="wsLibrary" action="addBookToUserByUser"   params="'batchId='+batchId+'&bookId='+bookId+'&instituteId='+instituteId+'&shelfAdd=true'"
       onSuccess="afterAddBookToUserByUser(data)" />
    }
    function afterAddBookToUserByUser(data) {
        bookQueueId=bookIds;
        batchIds=batchIds;
        if(data.message=="added" || data.message=="Book already exists in the user library."){
            window.location.href = "/" +  replaceAll(bookTitles,' ','-').toLowerCase() + "/ebook?bookId=" + bookIds + "&instituteId=" + data.instituteId + "&batchId=" + data.batchId + "&siteName=${session['entryController']}";
        }else if(data.message=="No license left"){
            <g:remoteFunction controller="wsLibrary" action="checkBookQueueValidity"  onSuccess='bookValidityDate(data);' params="'batchId='+batchIds+'&bookId='+bookQueueId"/>

        }
    }
    function bookValidityDate(data) {
        $('.loading-icon').addClass('hidden');
        if (data.message == "added") {
            window.location.href =  "/" + bookTitledetails + "/ebook?bookId=" + bookQueueId + "&siteName=${session['entryController']}";
        } else if(data.message =="addToQueue") {
            document.getElementById("bookValidityModalText").innerHTML = "#" + data.queuePosition;
            if (data.booksQueueDtl == "Already Exist") {
                $("#bookQueueModal").modal("show");
                $("#bookAddedQueue").show();
                document.getElementById("checkAddedQueue").innerText = "Your eBook is already added in waiting list.";
                $("#bookValidity, #bookReturned, #bookRemovedQueue").hide();
            } else {
                $("#bookQueueModal").modal("show");
                $("#bookValidity").show();
                $("#bookAddedQueue, #bookReturned, #bookRemovedQueue").hide();
            }
        }
    }


    //RETURN BOOK
    function returnBookFromUser(bookId,returnbatchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="returnBookFromUser" params="'bookId='+bookId+'&batchId='+returnbatchId" onSuccess="bookReturnedResp(data)"/>
    }
    function bookReturnedResp(data) {
        if(data.status=='OK'){
            $('.loading-icon').addClass('hidden');
            $("#bookQueueModal").modal("show");
            $("#bookReturned").show();
            $("#bookValidity, #bookAddedQueue, #bookRemovedQueue").hide();
            $("#bookQueueModal").on("hidden.bs.modal", function () {
                $('.loading-icon').removeClass('hidden');
                location.reload();
            });
        }
    }


    //QUEUE LIST BOOKS
    function openQueueList() {
        $(".show-library").hide();
        $(".show-queue").show();
        $("#bookQueueModal").modal("hide");
        <g:remoteFunction controller="wsLibrary" action="getBooksInQueueForUser"  onSuccess='displayQueueBooks(data);'/>
    }
    function displayQueueBooks(data){
        queBooksPresent=true;
        $('html, body').animate({scrollTop:0}, 'slow');
        var queBooks = data.QueueBooks;
        var htmlStr = "";
        if(queBooks.length > 0) {
            htmlStr += "<div class='row'>";
            for (var books of queBooks) {
                htmlStr += "<div class='col-6 col-md-4 col-lg-3'>" +
                    displayLibraryBooks(books);
                htmlStr += "</div>";
            }
            uncoverBookUI();
            htmlStr += "</div>";
        }
        else{
            htmlStr  = "<div class='no-books-available'>" +
                "<p class='text-center pt-4'><strong>Congratulations,</strong> there are no eBooks on your waiting list.</p>" +
                "<div class='text-center'><a href='javascript:backToLibrary();' class='btn btn-lg btn-warning btn-warning-modifier btn-shadow click-here-link mt-3 px-4 mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect'>Back to Library</a></div>" +
                "</div>";
            $('.loading-icon').addClass('hidden');
            $('body').css({
                'position' : 'relative'
            });
        }
        document.getElementById("content-data-books-queue").innerHTML=htmlStr;
        $('.loading-icon').addClass('hidden');
    }

    //REMOVE BOOK FROM QUE
    function removeBookFromUserQueue(bookId,batchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="removeBookFromUserQueue" params="'bookId='+bookId+'&batchId='+batchId" onSuccess="bookRemovedResp(data)"/>
    }

    function bookRemovedResp(data) {
        $('.loading-icon').addClass('hidden');
        if(data.status=='OK'){
            $("#bookQueueModal").modal("show");
            $("#bookRemovedQueue").show();
            $("#bookValidity, #bookAddedQueue, #bookReturned").hide();
            $("#bookQueueModal").on("hidden.bs.modal", function () {
                //location.reload()
                openQueueList();
            });
        }
    }

    //OPEN LIBRARY
    function openLibrary() {
        $("#bookQueueModal").modal("hide");
        window.location.href = "/wsLibrary/myLibrary";
    }

    //Back to library
    function backToLibrary(){
        $(".show-library").show();
        $(".show-queue").hide();
        $("#content-data-books").hide();
    }


    // Replacing strings and spaces

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    //Sort
    function sortDisplay(){
        pg_start=0
        pg_end=6
        pg_no=1
        paginationIndex = 0
        displayBooks(booksData);
    }

    function SortByLastRead(x,y) {
        return ((x.sortNo == y.sortNo) ? 0 : ((x.sortNo > y.sortNo) ? 1 : -1 ));
    }
    function SortByTitle(x,y) {
        return ((x.title == y.title) ? 0 : ((x.title > y.title) ? 1 : -1 ));
    }



    //Add book to que
    function addBookToQueue(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="addInstituteBookToUserQueue"  onSuccess='bookAddedToQueue(data);' params="'batchId='+batchIds+'&bookId='+bookQueueId+'&instituteId='+instituteId"/>
    }

    function bookAddedToQueue(data){
        $('.loading-icon').addClass('hidden');
        $("#bookQueueModal").modal("show");
        $("#bookAddedQueue").show();
        document.getElementById("checkAddedQueue").innerText = "Your eBook is added to waiting list.";
        $("#bookValidity, #bookReturned, #bookRemovedQueue").hide();
        if(data.status=="Ok") {
            $("#bookQueueModal").modal("hide");
        }
    }

    //Delete book from library
    function bookDelete(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="log" action="removeBookFromLibrary" params="'bookId='+deleteBookId" onSuccess = "bookRemoved(data);"/>
    }

    function bookRemoved(data) {
        if (data.status == "Deleted") {
            $('#deleteBook').modal('hide');
            $('.loading-icon').removeClass('hidden');
            location.reload();
        }
    }

    // Institute Select

    $('#institute-list li').on('click', function(){
        allBooksLoaded = false;
        $('.loading-icon').removeClass('hidden');
        $('#search-book').val('');
        $("#mySelfBtn").removeClass("generate");
        $("#selectInstituteBtn").addClass("generate");
        $("#subjectFilter, #generateBtn, #relatedBooksContainer, #bestSellerBooksContainer").hide();
        $("#selectedInstitute").text($(this).text());
        $(this).addClass('selected');
        $(this).siblings('#institute-list li').removeClass('selected');
        var selectedValue = $(this).data('value');
        instituteId = selectedValue.substring(0,selectedValue.indexOf('_'));
        batchId = selectedValue.substring(selectedValue.lastIndexOf('_')+1);
        localStorage.setItem('instituteClicked',batchId);
        if(instituteLibrary=="true"){
            localStorage.setItem('myinstitute',$('#selectedInstitute').text());
        }
        pageNo = 1;
        isAjaxCalled = false;
        if(!allBooksLoaded ) {
            <g:remoteFunction controller="wsLibrary" action="getPaginatedInstituteBooks" params="'batchId='+batchId+'&pageNo='+pageNo"  onSuccess='displayBooksForInstitute(data);'/>
        }
    });

    $('#selectInstituteBtn').on('click', function () {
        $("#mySelfBtn").removeClass("generate");
        $("#selectInstituteBtn").addClass("generate");
    });

    function myLibraryBooks(){
        localStorage.setItem('instituteClicked','');
        window.location.href = '/wsLibrary/myLibrary';
        $('.loading-icon').removeClass('hidden');
        $('#search-book').val('');
        $("#mySelfBtn").addClass("generate");
        $("#selectInstituteBtn").removeClass("generate");
        $("#selectedInstitute").text("Select Institute");

    }
    //Search
    $('#search-book').keyup(function() {
        $("#searcherrormsg").hide();
        if($('#search-book').val() === '') {
            $('.loading-icon').removeClass('hidden');
            location.reload();
        }
    });

    $('#search-book').on('keyup keypress', function(e) {
        var keyCode = e.keyCode || e.which;
        if (keyCode === 13) {
            e.preventDefault();
            return false;
        }
    });
    $('.search-icon-lib').on('click',function () {
        $('.search-box').css('display','flex');
        $('.search-box input').focus();
        $(this).attr('style','display: none !important');
    });
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/wsLibrary/myLibrarySearchSuggestion',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }}
    });

    function submitSearch(){
        document.getElementById("institute-recent-read-books").innerHTML = "";
        $("#total-books-of-user").hide();
        $('.loading-icon').removeClass('hidden');
        var searchString =document.getElementById("search-book").value;
        <g:remoteFunction controller="wsLibrary" action="getMyLibrarySearchResults"  onSuccess='searchResults(data);'
                params="'searchString='+searchString" />
    }
    function searchResults(data) {
        $("#mySelfBtn, #selectInstituteBtn").removeClass("generate");
        var searchresults=data;
        if (data.status != "Nothing present") {
            $('.loading-icon').addClass('hidden');
            displayBooksForSearch(searchresults);
        } else {
            if (elementExists("searcherrormsg")) {
                $('.loading-icon').addClass('hidden');
                document.getElementById("searcherrormsg").innerText = "No eBooks available for your search";
                $("#searcherrormsg").show();
            }
        }
    }
    function displayBooksForSearch(data) {
        var htmlStr = "";
        var searchBooks = data.books;
        var newObj = {};
        var newArr = [];
        if(searchBooks != null) {
            for (var i = 0; i < searchBooks.length; i++) {
                newObj[searchBooks[i]['id']] = searchBooks[i];
            }
            var keys = Object.keys(newObj);
            for (var i = keys.length - 1; i >= 0; i--) {
                newArr.push(newObj[keys[i]])
            }
            searchBooks = newArr;
        }
        if(searchBooks.length > 0 ){
            htmlStr +="<h4>All eBooks<p id='searchBooksNo'></p></h4>"+"<div class='row search-books-list pt-3'>";
            for(var books of searchBooks ){
                htmlStr +="<div class='col-6 col-md-4 col-lg-3 institute_book'>"+
                    displayLibraryBooks(books);
                htmlStr +="</div>";
            }
            htmlStr +="</div>";

            document.getElementById("content-data-search-books").innerHTML =htmlStr;
            $('#content-data-search-books .dropup').remove();
            uncoverBookUI();
            var searchAllBooks=searchBooks.length;
            document.getElementById('searchBooksNo').innerHTML = searchAllBooks ;

        }
        $("#content-data-institute-books, #institute-recent-read-books, #checkWaitingListBtn, #total-books-of-user, #content-data-institute-books-paid , #content-data-institute-books-free, #button-container").hide();
        $("#content-data-search-books").show();

        $('.search_book a').on('click', function () {
            $('.loading-icon').removeClass('hidden');
            setTimeout(function() {
                $('.mozilla .loading-icon').addClass('hidden');
            }, 1500);
        });

    }


    $(document).ready(function(){
        <%if(showMyShelf){%>
        getBooksList();

        <% }else{%>
        <%if(institutes==null){%>
        localStorage.setItem('instituteClicked','');
        $("#emptyInstituteBooks").show();
        <%}%>
        getBooksListLibwonder();
        <% }%>
    });
</script>
<script>
    if(instituteLibrary!="true") {
        $('#institute-list li:first-child').trigger('click');
    }
    else{
        if (localStorage.getItem('myinstitute') === null) {
            $('#selectedInstitute').text($('#institute-list li:first-child').text());
            $('#institute-list li:first-child').trigger('click');
            localStorage.setItem("myinstitute", $('#selectedInstitute').text());
        } else {
            if (localStorage.getItem('myinstitute') === '') {
                $('#institute-list li:first-child').trigger('click');
            }
            $('#selectedInstitute').text(localStorage.getItem('myinstitute'));
            $("#institute-list li").each(function (index, element) {
                if ($(this).text() == localStorage.getItem('myinstitute')) {
                    $(this).trigger('click');
                }
            });
        }
    }


    function displayBooksPaidFreeForInstitutePaginated(batchId, pageNoForNew,typeOfBooks){
        if(!allBooksLoaded){
        <g:remoteFunction controller="wsLibrary" action="getPaginatedInstituteBooksNew" params="'batchId='+batchId+'&pageNo='+pageNoForNew+'&type='+typeOfBooks"  onSuccess='displayBooksPaidFreepaginated(data);'/>
    }}
    function displayBooksPaidFreepaginated(data){
        var htmlStr = "";
        var instituteBooks = JSON.parse(data.books);
        var booksData = instituteBooks?instituteBooks:[];
        if(instituteBooks == 'null')
        {
            booksData =[];
        }
        if(booksData.length<0 || data.books == '[]' || data.books == null || data.books == 'null' ){
            allBooksLoaded = true;

            $('.loading-icon').addClass('hidden');
            $('.loading-icon').css('display','none');
        }
        if(instituteBooks == '' || instituteBooks == null || instituteBooks == 'null') {
            isAjaxCalled = true;
            $("#loadMoreButtonFree, #loadMoreButtonPaid").hide();
        } else {
            $("#loadMoreButtonFree button, #loadMoreButtonPaid button").text("Show More");
            isAjaxCalled = false;
            if(instituteBooks.length > 0 ){

                for(var books of instituteBooks ){
                    htmlStr +="<div class='col-6 col-md-4 col-lg-3 institute_book fadein-animated'>"+
                        displayLibraryBooks(books);
                    htmlStr +="</div>";
                }
                if(typeOfBooks == "free") {
                    $("#content-data-institute-books-free").find(".institute-books-list").append(htmlStr);
                    $('#content-data-institute-books-free .dropup').remove();

                    if(instituteBooks.length >= 30) {
                        $("#loadMoreButtonFree").show();
                        $("#loadMoreButtonFree button").text("Show More");
                    } else {
                        $("#loadMoreButtonFree").hide();
                    }

                    $("#loadMoreButtonFree button").click(function() {
                        $(this).text('Loading..');
                        isAjaxCalled = true;
                        pageNoForNew = pageNoForNew + 1;
                        displayBooksPaidFreeForInstitutePaginated(insClickedForBatch, pageNoForNew, typeOfBooks);
                    });
                }
                else if(typeOfBooks == "paid")
                {
                    $("#content-data-institute-books-paid").find(".institute-books-list").append(htmlStr);
                    $('#content-data-institute-books-paid .dropup').remove();

                    if(instituteBooks.length >= 30) {
                        $("#loadMoreButtonPaid").show();
                        $("#loadMoreButtonPaid button").text("Show More");
                    } else {
                        $("#loadMoreButtonPaid").hide();
                    }

                    $("#loadMoreButtonPaid button").click(function() {
                        $(this).text('Loading..');
                        isAjaxCalled = true;
                        pageNoForNew = pageNoForNew + 1;
                        displayBooksPaidFreeForInstitutePaginated(insClickedForBatch, pageNoForNew, typeOfBooks);
                    });
                }

                uncoverBookUI();
                $('.loading-icon').addClass('hidden');
                $('.loading-icon').css('display','none');
            }
        }
    }


</script>

</body>
</html>
