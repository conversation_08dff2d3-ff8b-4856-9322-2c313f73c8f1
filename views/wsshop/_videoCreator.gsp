<body>
<main class="blog_container">
    <article>
        <header>
            <h1>Video for ${title}</h1>
            <div id="introduction"><a href="/autoVideo/automatedVideo?videoMode=slidesVideo&title=${title}">Create Video</a></div>
        </header>
        <section id="bestEBooks">
            <h3>For Youtube description</h3>
            <div id="bestEBooksText"></div>
        </section>

    </article>
</main>
<g:render template="/books/footer_new"></g:render>

<script>
    var data = { "books":"${booksList.get("books")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publishers":"${booksList.get("publishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&')
    };
    var books = JSON.parse(data.books);
    var noOfBooks = books.length;
    if(noOfBooks>10) noOfBooks=10;
    var imgSrc;
    var htmlSrc=" <b>Books links</b><br>"
    for(var i=0; i<noOfBooks;i++){
        htmlSrc +=(i+1)+"."+"<a href='https://www.wonderslate.com/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName=books&bookId=" + books[i].id + "&preview=true' target='_blank'>"+books[i].title+"</a><br>";


    }
    <%if(info.bestBooks!=null){%>
        htmlSrc +="<br><h3>Amazon book links</h3>${info.bestBooks}";
    <%}%>
    if(noOfBooks>0)
        document.getElementById("bestEBooksText").innerHTML = htmlSrc;
    else
        document.getElementById("bestEBooksText").innerHTML = "<b> Best seller list not available currently. Please try after some time.";


</script>
