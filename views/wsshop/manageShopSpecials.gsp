<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <br>
    <div class="d-flex justify-content-start align-items-center page_title col-12 col-md-10 mx-auto pb-2 px-0">

        <h3><strong>Manage Store Specials</strong></h3>

    </div>
    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="newlyReleasedEbook" style="display: block;">Newly Released Book (Single book id)</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="newlyReleasedEbook"  value="${keyValues.get("newlyReleasedEbook")}"></input><br>

                <button class="btn btn-primary" style="" onclick="updateShopSpecial('newlyReleasedEbook');">Update</button><br>

            </div>
        </div>
    </div>

    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="ebookOfTheDay" style="display: block;">eBook of the day (Single book id)</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="ebookOfTheDay"  value="${keyValues.get("ebookOfTheDay")}"></input><br>
                 <button class="btn btn-primary" style="" onclick="updateShopSpecial('ebookOfTheDay');">Update</button><br>

            </div>
        </div>
    </div>

    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="trendingNowEbook" style="display: block;">Trending eBook (Single book id)</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="trendingNowEbook"  value="${keyValues.get("trendingNowEbook")}"></input><br>
                <button class="btn btn-primary" style="" onclick="updateShopSpecial('trendingNowEbook');">Update</button><br>

            </div>
        </div>
    </div>

    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="featuredPublishers" style="display: block;">Featured Publishers (Comma separated publisher ids)</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="featuredPublishers"  value="${keyValues.get("featuredPublishers")}"></input><br>
                <button class="btn btn-primary" style="" onclick="updateShopSpecial('featuredPublishers');">Update</button><br>

            </div>
        </div>
    </div>



    <div class="row">
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <label for="featuredCategories" style="display: block;">Featured Categories (Comma separated syllabus)</label>
            <div class="dflex_pub">
                <input type="text" class="form-control" name="bookId" id="featuredCategories"  value="${keyValues.get("featuredCategories")}"></input><br>
                <button class="btn btn-primary" style="" onclick="updateShopSpecial('featuredCategories');">Update</button><br>

            </div>
        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>

    function updateShopSpecial(keyName){
        var keyValue = document.getElementById(keyName).value;

            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wsshop" action="updateShopSpecials" params="'keyName='+keyName+'&keyValue='+keyValue" onSuccess = "shopSpecialUpdated(data);"/>

    }

    function shopSpecialUpdated(data){
        $('.loading-icon').addClass('hidden');
    }

</script>


</body>
</html>
